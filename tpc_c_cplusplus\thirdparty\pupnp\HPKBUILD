# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=pupnp
pkgver=release-1.14.16
pkgrel=0
pkgdesc="libupnp: Build UPnP-compliant control points, devices, and bridges on several operating systems."
url="https://github.com/pupnp/pupnp"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD-3-Clause license")
depends=()
makedepends=()
source="https://github.com/$pkgname/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools=

builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L  > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test,需要连接网络
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}