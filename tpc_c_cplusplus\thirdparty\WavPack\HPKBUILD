# Contributor: wuping<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=WavPack
pkgver=5.6.0
pkgrel=0
pkgdesc="WavPack encode/decode library, command-line programs, and several plugins"
url="https://github.com/dbry/WavPack"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD")
depends=()
makedepends=()
source="https://github.com/dbry/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz

asmcompiler=

prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        asmcompiler=$OHOS_SDK/native/llvm/bin/arm-linux-ohos-clang
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        asmcompiler=$OHOS_SDK/native/llvm/bin/aarch64-linux-ohos-clang
    fi	
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DWAVPACK_BUILD_DOCS=OFF -DCMAKE_ASM_COMPILER=$asmcompiler -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 在OpenHarmony开发板中执行测试用例
    # ctest
}

cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packageName
}
