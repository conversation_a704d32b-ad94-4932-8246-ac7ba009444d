# Contributor: zhang<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=FFmpeg-ff4.0
pkgver=ijk0.8.8--20210426--001
pkgrel=0
pkgdesc="FFmpeg is a collection of libraries and tools to process multimedia content such as audio, video, subtitles and related metadata."
url="https://github.com/bilibili/FFmpeg/"
archs=("armeabi-v7a" "arm64-v8a" "x86_64")
license=("GPL2 or later" "LGPL2.1 or later" "MIT" "X11" "BSD-styl")
depends=("openssl_1_1_1w" "openh264" "libvpx")
makedepends=()
source="https://codeload.github.com/bilibili/FFmpeg/tar.gz/refs/tags/ff4.0--ijk0.8.8--20210426--001"
# 备用源（如果GitHub不可用）：
# source="https://gitee.com/lycium_pkg_mirror/FFmpeg/repository/archive/ff4.0--ijk0.8.8--20210426--001.zip"

autounpack=true
downloadpackage=true
buildtools="configure"

# GitHub tar.gz 解压后的实际目录名（单横线）
builddir=FFmpeg-ff4.0-ijk0.8.8-20210426-001
packagename=FFmpeg-ff4.0-ijk0.8.8-20210426-001.tar.gz
patchflag=true
source envset.sh
arch=
ldflags=

prepare() {
    if $patchflag
    then
        cd $builddir
        # fix bug
        patch -p1 < `pwd`/../oh_ffmpeg-bug-fixed.patch
        patch -p1 < `pwd`/../oh_ffmpeg_hls_optimization.patch
        patchflag=false
        cd $OLDPWD
    fi
    
    cp -rf $builddir $pkgname-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        arch=arm
        ldflags="-L${OHOS_SDK}/native/sysroot/usr/lib/arm-linux-ohos"
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        arch=aarch64
        ldflags="-L${OHOS_SDK}/native/sysroot/usr/lib/aarch64-linux-ohos"
    elif [ $ARCH == "x86_64" ]
    then
        setx86_64ENV
        arch=x86_64
        ldflags="-L${OHOS_SDK}/native/sysroot/usr/lib/x86_64-linux-ohos"
    else
        echo "${ARCH} not support"
        return -1
    fi
    export CFLAGS="-Wno-int-conversion -fPIC $CFLAGS"
    export CXXFLAGS="-Wno-int-conversion -fPIC $CXXFLAGS"
    return $ret
}

build() {
    cd $pkgname-$ARCH-build
    PKG_CONFIG_LIBDIR="${pkgconfigpath}" ./configure "$@" --enable-neon --enable-asm --enable-network \
    --enable-cross-compile --disable-x86asm --enable-openssl --enable-protocols --enable-libopenh264 \
    --enable-decoder=vp8 --enable-parser=vp8 --enable-libvpx --enable-libvpx-vp8-decoder \
    --disable-programs --enable-static --disable-shared --disable-doc --disable-htmlpages --target-os=linux --arch=$arch \
    --cc=${CC} --ld=${CC} --strip=${STRIP} --sysroot=${OHOS_SDK}/native/sysroot > $buildlog 2>&1
    $MAKE >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $pkgname-$ARCH-build
    $MAKE install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    return 0
}

recoverpkgbuildenv() {
    unset arch
    unset ldflags
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    elif [ $ARCH == "x86_64" ]
    then
        unsetx86_64ENV
    else
        echo "${ARCH} not support"
        return -1
    fi
}

# 清理环境
cleanbuild() {
   rm -rf ${PWD}/${builddir} ${PWD}/$pkgname-arm64-v8a-build ${PWD}/$pkgname-armeabi-v7a-build ${PWD}/$pkgname-x86_64-build #${PWD}/$packagename
}
