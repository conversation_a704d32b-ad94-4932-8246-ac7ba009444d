# Contributor: wuping<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=soundtouch
pkgver=2.3.2
pkgrel=0
pkgdesc="SoundTouch is an open-source audio processing library for changing the Tempo, Pitch and Playback Rates of audio streams or audio files. The library additionally supports estimating stable beats-per-minute rates for audio tracks."
url="https://www.surina.net/soundtouch/download.html"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPLv2.1")
depends=()
makedepends=()
install=
source="https://www.surina.net/$pkgname/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true
builddir=$pkgname
packagename=$builddir-$pkgver.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "Test MUST on OpenHarmony device!"
    # 在OpenHarmony开发板中执行测试用例
    # ./soundstretch  soundtouch.wav out.wav -tempo=+50 -pitch=+20
}

cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
