# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
pkgname=eigen
pkgver=3.4.0
pkgrel=0
pkgdesc="Eigen is a C++ template library for linear algebra: matrices, vectors, numerical solvers, and related algorithms."
url="http://eigen.tuxfamily.org/"
archs=("armeabi-v7a" "arm64-v8a")
license=("MPL2")
depends=()
makedepends=()

source="https://gitlab.com/libeigen/$pkgname/-/archive/$pkgver/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -DCMAKE_Fortran_COMPILER=OFF -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 VERBOSE=1 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir
}
