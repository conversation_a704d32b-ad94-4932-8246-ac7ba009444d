[{"Name": "apr", "License": "Apache License 2.0", "License File": "https://www.apache.org/licenses/LICENSE-2.0", "Version Number": "1.7.4", "Owner": "<EMAIL>", "Upstream URL": "https://dlcdn.apache.org/apr/apr-1.7.4.tar.gz", "Description": "APR is a software library that creates and maintains a set of APIs that map to the underlying operating system."}, {"Name": "libtool", "License": "GPLv2", "License File": "https://www.gnu.org/licenses/old-licenses/gpl-2.0.html", "Version Number": "2.4.6", "Owner": "<EMAIL>", "Upstream URL": "https://ftpmirror.gnu.org/libtool/libtool-2.4.6.tar.gz", "Description": "GNU Libtool is a generic library support script that hides the complexity of using shared libraries behind a consistent, portable interface."}]