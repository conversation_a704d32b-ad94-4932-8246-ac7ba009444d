# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
qrcodefile=${LYCIUM_THIRDPARTY_ROOT}/$pkgname/qrcode.png
outqrcodefile=${LYCIUM_THIRDPARTY_ROOT}/$pkgname/qrcode.png
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd ${builddir}/${ARCH}-build
    ./example/ZXingReader -format QRCode $qrcodefile > ${logfile} 2>&1
    res=$?
    if [ $res -eq 0 ]
    then
        ./example/ZXingWriter -size 200x200 QRCode http://test.com outqrcode.png >> ${logfile} 2>&1
         res=$?
    fi

    cd $OLDPWD

    return $res
}
