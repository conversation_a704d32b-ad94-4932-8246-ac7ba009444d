# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, DongZhengDong <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=cppjieba
pkgver=v5.1.0
pkgrel=0
pkgdesc="The Jieba Chinese Word Segmentation Implemented By C++ ."
url="https://github.com/yanyiwu/$pkgname"
archs=("armeabi-v7a" "arm64-v8a")
license=("The MIT License")
depends=()
makedepends=()
source="https://codeload.github.com/yanyiwu/$pkgname/zip/refs/tags/$pkgver"
buildtools="cmake"

autounpack=false
downloadpackage=false
builddir=$pkgname
cloneFlag=true
patchFlag=false
source envset.sh

prepare() {
    if $cloneFlag
    then
        git clone --branch v5.1.0 https://github.com/yanyiwu/$pkgname.git > $publicbuildlog 2>&1
	ret=$?
	if [ $ret -ne 0 ]; then
	    return $ret
	fi
	cd $builddir
	git submodule init && git submodule update >> $publicbuildlog 2>&1
	ret=$?
	if [ $ret -ne 0 ]; then
	    return $ret
	fi
	cloneFlag=false
        cd $OLDPWD
    fi
	
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > $buildlog 2>&1
    $MAKE -C $ARCH-build > $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    ret=$?
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/deps/include/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/dict
    cp -rf include/* $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp -rf deps/limonp/include/* $LYCIUM_ROOT/usr/$pkgname/$ARCH/deps/include/
    cp -rf dict/* $LYCIUM_ROOT/usr/$pkgname/dict
    cd $OLDPWD
    return $ret
}

check() {
    echo "Test MUST on OpenHarmony device!"
}

cleanbuild(){
    rm -rf ${PWD}/$builddir
}
