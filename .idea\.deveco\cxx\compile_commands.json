[{"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi_init.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_napi.dir\\napi\\ijkplayer_napi_init.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi_init.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_napi.dir\\napi\\ijkplayer_napi.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi_manager.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_napi.dir\\napi\\ijkplayer_napi_manager.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi_manager.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\proxy\\ijkplayer_napi_proxy.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_napi.dir\\proxy\\ijkplayer_napi_proxy.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\proxy\\ijkplayer_napi_proxy.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\hashmap\\data_struct.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_napi.dir\\utils\\hashmap\\data_struct.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\hashmap\\data_struct.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\ffmpeg\\custom_ffmpeg_log.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_napi.dir\\utils\\ffmpeg\\custom_ffmpeg_log.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\ffmpeg\\custom_ffmpeg_log.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\napi\\napi_utils.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_napi.dir\\utils\\napi\\napi_utils.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\napi\\napi_utils.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi_audio_init.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_audio_napi.dir\\napi\\ijkplayer_napi_audio_init.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi_audio_init.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_audio_napi.dir\\napi\\ijkplayer_napi.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\napi\\ijkplayer_napi.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\proxy\\ijkplayer_napi_proxy.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_audio_napi.dir\\proxy\\ijkplayer_napi_proxy.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\proxy\\ijkplayer_napi_proxy.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\hashmap\\data_struct.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_audio_napi.dir\\utils\\hashmap\\data_struct.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\hashmap\\data_struct.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\ffmpeg\\custom_ffmpeg_log.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_audio_napi.dir\\utils\\ffmpeg\\custom_ffmpeg_log.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\ffmpeg\\custom_ffmpeg_log.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\napi\\napi_utils.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/proxy -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/utils/napi -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o CMakeFiles\\ijkplayer_audio_napi.dir\\utils\\napi\\napi_utils.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\napi\\napi_utils.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_aout.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_aout.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_aout.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_audio.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_audio.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_audio.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_egl.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_egl.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_egl.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_error.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_error.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_error.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_mutex.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_mutex.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_mutex.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_stdinc.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_stdinc.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_stdinc.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_thread.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_thread.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_thread.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_timer.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_timer.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_timer.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_vout.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_vout.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_vout.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_extra_log.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ijksdl_extra_log.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ijksdl_extra_log.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\color.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\color.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\color.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\common.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\common.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\common.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\renderer.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\renderer.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\renderer.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\renderer_rgb.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\renderer_rgb.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\renderer_rgb.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\renderer_yuv420p.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\renderer_yuv420p.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\renderer_yuv420p.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\renderer_yuv444p10le.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\renderer_yuv444p10le.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\renderer_yuv444p10le.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\shader.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\shader.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\shader.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\fsh\\rgb.fsh.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\fsh\\rgb.fsh.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\fsh\\rgb.fsh.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\fsh\\yuv420p.fsh.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\fsh\\yuv420p.fsh.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\fsh\\yuv420p.fsh.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\fsh\\yuv444p10le.fsh.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\fsh\\yuv444p10le.fsh.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\fsh\\yuv444p10le.fsh.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\vsh\\mvp.vsh.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\gles2\\vsh\\mvp.vsh.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\gles2\\vsh\\mvp.vsh.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\ijksdl_vout_android_nativewindow.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\ijksdl_vout_android_nativewindow.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\ijksdl_vout_android_nativewindow.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\ijksdl_vout_android_surface.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\video\\ijksdl_vout_android_surface.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\video\\ijksdl_vout_android_surface.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ffmpeg\\ijksdl_vout_overlay_ffmpeg.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ffmpeg\\ijksdl_vout_overlay_ffmpeg.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ffmpeg\\ijksdl_vout_overlay_ffmpeg.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ffmpeg\\abi_all\\image_convert.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\ffmpeg\\abi_all\\image_convert.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\ffmpeg\\abi_all\\image_convert.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\audio\\ijksdl_aout_android_opensles.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\audio\\ijksdl_aout_android_opensles.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\audio\\ijksdl_aout_android_opensles.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\dummy\\ijksdl_vout_dummy.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\dummy\\ijksdl_vout_dummy.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijksdl\\dummy\\ijksdl_vout_dummy.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\ohoslog\\ohos_log.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijksdl_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o ijksdl\\CMakeFiles\\ijksdl.dir\\__\\utils\\ohoslog\\ohos_log.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\utils\\ohoslog\\ohos_log.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ff_cmdutils.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ff_cmdutils.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ff_cmdutils.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ff_ffplay.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ff_ffplay.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ff_ffplay.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ff_ffpipeline.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ff_ffpipeline.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ff_ffpipeline.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ff_ffpipenode.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ff_ffpipenode.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ff_ffpipenode.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkmeta.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkmeta.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkmeta.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkplayer.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkplayer.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkplayer.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkplayer_android.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkplayer_android.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkplayer_android.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\pipeline\\ffpipenode_ffplay_vdec.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\pipeline\\ffpipenode_ffplay_vdec.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\pipeline\\ffpipenode_ffplay_vdec.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\pipeline\\ffpipeline_android.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\pipeline\\ffpipeline_android.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\pipeline\\ffpipeline_android.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\allformats.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\allformats.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\allformats.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijklivehook.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijklivehook.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijklivehook.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkio.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkio.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkio.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkiomanager.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkiomanager.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkiomanager.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkiocache.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkiocache.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkiocache.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkioffio.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkioffio.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkioffio.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkioprotocol.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkioprotocol.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkioprotocol.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkioapplication.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkioapplication.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkioapplication.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkiourlhook.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkiourlhook.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkiourlhook.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkasync.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkasync.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkasync.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkurlhook.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijkurlhook.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijkurlhook.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijklongurl.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijklongurl.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijklongurl.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijksegment.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavformat\\ijksegment.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavformat\\ijksegment.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkdict.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavutil\\ijkdict.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkdict.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkutils.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavutil\\ijkutils.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkutils.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkthreadpool.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavutil\\ijkthreadpool.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkthreadpool.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijktree.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavutil\\ijktree.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijktree.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkfifo.c", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavutil\\ijkfifo.c.o   -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkfifo.c"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkstl.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ijkavutil\\ijkstl.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ijkavutil\\ijkstl.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ohos\\ffpipenode_ohos_mediacodec_vdec.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ohos\\ffpipenode_ohos_mediacodec_vdec.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ohos\\ffpipenode_ohos_mediacodec_vdec.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ohos\\ohos_video_decoder_data.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ohos\\ohos_video_decoder_data.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ohos\\ohos_video_decoder_data.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ohos\\ohos_video_decoder_Info.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ohos\\ohos_video_decoder_Info.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ohos\\ohos_video_decoder_Info.cpp"}, {"file": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ohos\\ohos_video_decoder.cpp", "directory": "D:/new/ohos_ijkplayer-2.0.3/ijkplayer/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -DOHOS_PLATFORM -Dijkplayer_EXPORTS -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include -ID:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC   -o ijkplayer\\CMakeFiles\\ijkplayer.dir\\ohos\\ohos_video_decoder.cpp.o -c D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\cpp\\ijkplayer\\ohos\\ohos_video_decoder.cpp"}]