# shapelib集成到应用hap

本库是在RK3568开发板上基于OpenHarmony3.2 Release版本的镜像验证的，如果是从未使用过RK3568，可以先查看[润和RK3568开发板标准系统快速上手](https://gitee.com/openharmony-sig/knowledge_demo_temp/tree/master/docs/rk3568_helloworld)。

## 开发环境

- [开发环境准备](../../../docs/hap_integrate_environment.md)

## 编译三方库

- 下载本仓库

  ```shell
  git clone https://gitee.com/openharmony-sig/tpc_c_cplusplus.git --depth=1
  ```

- 三方库目录结构

  ```shell
  tpc_c_cplusplus/thirdparty/shapelib      #三方库shapelib的目录结构如下
  ├── docs                             #三方库相关文档的文件夹
  ├── HPKBUILD                         #构建脚本
  ├── HPKCHECK                         #测试脚本
  ├── SHA512SUM                        #三方库校验文件
  ├── README.OpenSource                #说明三方库源码的下载地址，版本、license等信息
  ├── README_zh.md   
  ```
  
- 在lycium目录下编译三方库

  编译环境的搭建参考[准备三方库构建环境](../../../lycium/README.md#1编译环境准备)

  ```shell
  cd lycium
  ./build.sh shapelib
  ```

- 三方库头文件及生成的库

  在lycium目录下会生成usr目录，该目录下存在已编译完成的32位和64位三方库

  ```shell
  shapelib/arm64-v8a   shapelib/armeabi-v7a
  ```
  
- [测试三方库](#测试三方库)

## 应用中使用三方库

- 需要将libshape.so相关目录拷贝到entry/libs目录下
- ![thirdparty_install_dir](pic/libshape_so_usage_ide.png)

- 在IDE的cpp目录下新增thirdparty目录，将编译生成的库拷贝到该目录下，如下图所示
  
  ![thirdparty_install_dir](pic/shapelib_usage_for_ide.png)
  
- 在最外层（cpp目录下）CMakeLists.txt中添加如下语句

  ```cmake
  #将三方库的头文件加入工程中
  target_link_libraries(entry PUBLIC ${CMAKE_SOURCE_DIR}/../../../libs/${OHOS_ARCH}/libshape.so)
  target_include_directories(entry PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/shapelib/${OHOS_ARCH}/include/)
  ```
  

## 测试三方库

  三方库的测试使用原库自带的测试用例来做测试，[准备三方库测试环境]    		(../../../lycium/README.md#3ci环境准备)

  进入到对应版本构建目录下,执行如下命令ctest 

  ![shapelib_ctest](pic/shapelib_ctest.png)

  ## 参考资料

  - [润和RK3568开发板标准系统快速上手](https://gitee.com/openharmony-sig/knowledge_demo_temp/tree/master/docs/rk3568_helloworld)
  - [OpenHarmony三方库地址](https://gitee.com/openharmony-tpc)
  - [OpenHarmony知识体系](https://gitee.com/openharmony-sig/knowledge)
  - [通过DevEco Studio开发一个NAPI工程](https://gitee.com/openharmony-sig/knowledge_demo_temp/blob/master/docs/napi_study/docs/hello_napi.md)
