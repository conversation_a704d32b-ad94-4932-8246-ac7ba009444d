[{"Name": "brotli", "License": "MIT License", "License File": "https://github.com/google/brotli/blob/master/LICENSE", "Version Number": "v1.0.9", "Owner": "hanjin<PERSON><EMAIL>", "Upstream URL": "https://github.com/google/brotli/archive/refs/tags/v1.0.9.tar.gz", "Description": "Brotli is a generic-purpose lossless compression algorithm that compresses data using a combination of a modern variant of the LZ77 algorithm, <PERSON><PERSON><PERSON> coding and 2nd order context modeling, with a compression ratio comparable to the best currently available general-purpose compression methods. It is similar in speed with deflate but offers more dense compression"}]