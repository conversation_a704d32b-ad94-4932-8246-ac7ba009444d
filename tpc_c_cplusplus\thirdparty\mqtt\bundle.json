{"name": "@ohos/mqtt", "description": "This repository contains the source code for the [Eclipse Paho](http://mqtt.org) MQTT C/C++ client library for Embedded platorm.", "version": "1.3.12", "license": "Eclipse Public License v2.0 and Eclipse Distribution License v1.0", "publishAs": "code-segment", "segment": {"destPath": "third_party/mqtt"}, "dirs": {}, "scripts": {}, "readmePath": {"en": "README"}, "component": {"name": "mqtt", "subsystem": "thirdparty", "syscap": [], "features": [], "adapted_system_type": [], "rom": "", "ram": "", "deps": {"components": [], "third_party": []}, "build": {"sub_component": ["//third_party/mqtt:libmqtt"], "inner_kits": [], "test": []}}}