# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
tmppath=

checkprepare() {
    # 添加pulseaudio库的lib/pulseaudio目录下的库路径到环境变量
    tmppath=$LD_LIBRARY_PATH
    export LD_LIBRARY_PATH=${LYCIUM_ROOT}/usr/pulseaudio/${ARCH}/lib/pulseaudio:$tmppath
}

checkunprepare() {
    # 恢复为测试前的环境变量
    export LD_LIBRARY_PATH=$tmppath
    unset tmppath
}

openharmonycheck() {
    res=0
    cd ${LYCIUM_ROOT}/usr/openal-soft/$ARCH/bin
    mkdir ${LYCIUM__MANUAL_CONFIRM_PATH}/${pkgname}

    # 测试项1：openal声卡详细信息
    ./openal-info > ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "openal-info test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi

    # 测试项2：录音
    ./alrecord -c 2 -b 16 -r 44100 -t 5 -o record.wav >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "alrecord test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi
    cp record.wav ${LYCIUM__MANUAL_CONFIRM_PATH}/${pkgname}/

    # 测试项3：播放
    ./alplay record.wav >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "alplay test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi

    # 测试项4：音调发生器测试
    ./altonegen >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "altonegen test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi

    # 测试项5：音频流示例
    ./alstream /system/etc/graphic/bootsound.wav >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "alstream test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi

    # 测试项6：HRTF示例
    ./alhrtf /system/etc/graphic/bootsound.wav >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "alhrtf test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi

    # 测试项7：源延迟示例
    ./allatency /system/etc/graphic/bootsound.wav >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "allatency test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi

    # 测试项8：混响示例
    ./alreverb /system/etc/graphic/bootsound.wav >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "alreverb test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi

    # 测试项9：多区域混响示例
    ./almultireverb /system/etc/graphic/bootsound.wav >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "almultireverb test failed" >> ${logfile} 2>&1
        cd $OLDPWD
        checkunprepare
        return res
    fi

    cp ${logfile} ${LYCIUM__MANUAL_CONFIRM_PATH}/${pkgname}/

    cd $OLDPWD

    checkunprepare

    return $res
}
