{"version": "1.0", "events": [{"head": {"id": "f85b4894-bd89-4282-a182-ab2fbdd27392", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 243273685400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29948199-b8b2-4431-83d7-e5181a2de9b5", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 243277294700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e0f0805-0646-45fa-ad63-5feac77b9d45", "name": "hvigor daemon: Socket will be closed. socketId=gt0FW7WwU_EAwt2lAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 243278189200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da7031b-4665-4f16-87e4-0c77ef83378d", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":15524,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"134da255925979149e11e1ebf84d9ba94ceba1fd\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753107408249,\"createdBy\":\"deveco\",\"sessionId\":\"00000050e04f630e74d96eb7380706821fd6aca9e42b9dae50ee99298458ee3779211466e6884f09f7f740e7327b2ce24f8a679f7ee50cb12fe131e6a802656484dd437d0db74464396149b398c9ab1407bc09df7abf484e2be47283b6918a53\"}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 243278887200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5b94318-689e-403b-92b6-4a3a6aa36f04", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 243289326600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "051d2f84-f3b0-422c-999f-79953564be7c", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 243289680100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef35a58-b807-44da-8b4f-62953da8d158", "name": "hvigor daemon: Socket is connected. socketId=SNqsHpjW_9KWpYljAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245014168300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d17014-9958-470d-91e7-487c2baa4842", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"42809c4110d90f682938597528c2556b5103a105\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\onlyreceiver\\\\editVersion\\\\update11\\\\globalstateUse\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5140,\"state\":\"idle\",\"lastUsedTime\":1752805626314,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"0000005035465bbb6f3d5c2c33fe4747ba2a5898fffda2e8e55ad25942bc459b9580a8b060cccfa52a7bd36d99a0b7e4b75bddd3d7c99c1bde55cbdd6b43f8cbd601fcaff5420efe69552e27c20ca2364d8aa9c3b0c8eea4159258819fbe6195\"},{\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":3716,\"state\":\"stopped\",\"lastUsedTime\":1753097817639,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"},{\"keyId\":\"47188334a2df522b9a2bcfd152364b7cbf9cf7bc\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":2544,\"state\":\"broken\",\"lastUsedTime\":1753106934730,\"info\":\"The process with pid 2544 does not exist.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050b3480f31209fac4945dbcc23751d371427a18721a3a8b3761622bf5db1171262644143e6e8f1e7d26ad2f7e306cf95a627c5b28a27a5fbc37fe3f12c9ce3153b5b4265a7d0f34aa3d2a8c0c7e286c8ad6c526479487ab01b8245d9e3\"},{\"keyId\":\"277ad167b90cc80ce976dbd6afbe358bd99b7f7e\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":2148,\"state\":\"stopped\",\"lastUsedTime\":1753090153399,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050838d1ad01bfd1550ead51f6ed433a227255c04f73eb6d56f22bd2763fe725e2b83fbcf4fd60a26c845a2c602755158018cba8b725381b1441e800bf943ef6f387c5623ba06c8adf19ae16119669eddff1cd39b24422ab96c15d49f8f\"},{\"keyId\":\"893216d240f25a41562b9bcbc7a74ef1fd3fa8ed\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\release\\\\0718\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45003,\"pid\":18640,\"state\":\"stopped\",\"lastUsedTime\":1753085265437,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050cac0116da7771717d59e7bc8faf998fc155484d3cd0d66298ab3323e5f33a8c26ed7d084a225d981022c8ea0220538a0a54723b401e094f07324ce4a503482a754aac107da85885404f88621f172c9c61d553975ef4a47330cc4a6cb\"},{\"keyId\":\"186e185421ac1ecdd6120cfb745ca07b105f6b7a\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":2648,\"state\":\"stopped\",\"lastUsedTime\":1753106945929,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"000000502bae7b53c909cd10edbacff86d04ad1586436a0ee8b16bd3af854c90069429ddef44d977cdfb4891338a398ae99a4fa59670db9da9667b0a9de6c6f9fb5d82b63a8945d775e1fb5323bae310dc63c4988d0b7b31f1a2cc107c7d012a\"},{\"keyId\":\"b9ddb8cea31f249c8b2536ebf131cc68ae6be44d\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":17592,\"state\":\"stopped\",\"lastUsedTime\":1753084933680,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005070f328dae46fd03c7719b268053842e81e407fb9e7f97683a5c3f4144b92f225f5b56019abca2a185e5cb5b31eb96548737440c854b4d374ea335b821a28fa1b5c8f9980b8a71ae27ef25131c90ce745e53355b29e37100e8ec29f49\"},{\"pid\":15524,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"134da255925979149e11e1ebf84d9ba94ceba1fd\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753107408259,\"createdBy\":\"deveco\",\"sessionId\":\"00000050e04f630e74d96eb7380706821fd6aca9e42b9dae50ee99298458ee3779211466e6884f09f7f740e7327b2ce24f8a679f7ee50cb12fe131e6a802656484dd437d0db74464396149b398c9ab1407bc09df7abf484e2be47283b6918a53\"}]", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245015249700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7378a181-067f-4273-b599-cd50039a37b0", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":15524,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"134da255925979149e11e1ebf84d9ba94ceba1fd\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753107408259,\"createdBy\":\"deveco\",\"sessionId\":\"00000050e04f630e74d96eb7380706821fd6aca9e42b9dae50ee99298458ee3779211466e6884f09f7f740e7327b2ce24f8a679f7ee50cb12fe131e6a802656484dd437d0db74464396149b398c9ab1407bc09df7abf484e2be47283b6918a53\"}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245016325200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c65be38d-cec6-4ac1-912d-57968b20694c", "name": "set active socket. socketId=SNqsHpjW_9KWpYljAAAD", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245021129600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "586020ff-3e1c-4ab0-b2d5-9f6718dbbd63", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":15524,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"134da255925979149e11e1ebf84d9ba94ceba1fd\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753107409997,\"createdBy\":\"deveco\",\"sessionId\":\"00000050e04f630e74d96eb7380706821fd6aca9e42b9dae50ee99298458ee3779211466e6884f09f7f740e7327b2ce24f8a679f7ee50cb12fe131e6a802656484dd437d0db74464396149b398c9ab1407bc09df7abf484e2be47283b6918a53\"}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245022044700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0a16bd7-c922-4228-8ce9-122caf3af3a9", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=ijkplayer', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245025441800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf4907e8-b9c3-4fdc-a8d6-a0c91d6f9c32", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245026323500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a20dff-4d98-4605-858c-91890b95333d", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":15524,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"134da255925979149e11e1ebf84d9ba94ceba1fd\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753107410002,\"createdBy\":\"deveco\",\"sessionId\":\"00000050e04f630e74d96eb7380706821fd6aca9e42b9dae50ee99298458ee3779211466e6884f09f7f740e7327b2ce24f8a679f7ee50cb12fe131e6a802656484dd437d0db74464396149b398c9ab1407bc09df7abf484e2be47283b6918a53\"}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245027342200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edeeb2b8-f80a-43a8-8d7e-a1e35a4755bf", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245033172000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20efb448-c2a8-4e31-883d-fbc0a7ae64c7", "name": "Cache service initialization finished in 2 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245035125400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dea7353-fa2b-478b-a12e-7aad83aca4c4", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245041017200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd88dec2-478a-4b1e-8a50-2ee5daacb8f5", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245050217300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba7b2f1e-20be-4e0d-9a8b-96456c84f321", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245050271200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f02b6b0a-d835-448c-9e53-cdc037f7f73c", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245057554100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ef9d769-936c-460a-8cad-492ff581d44e", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245060335000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "073a3e79-a8b8-4a7b-9499-43ade89117a8", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245068587500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daa777d1-c8f9-46ce-8d9a-1b0cccf08d39", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245068642900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa0892ac-f928-4d1b-8028-aa74bf535eaf", "name": "Module entry Collected Dependency: D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245084409700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89a562be-611e-4608-8ae9-949d961bdd58", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245084442400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a863e10f-d34d-4af9-b9bd-cd452752afd4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245088503300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bbc4319-68e5-493b-b5ec-16c5bb5a97c6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245088546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62b42bc5-4f71-492c-98c2-ecba43d1d74f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245088622100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00419c0f-ff17-43fd-b0f4-a7779b7e3c4c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245088667700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "161d8c34-0068-4959-9b0d-c882894d4264", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245088678400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1758010-0134-4680-8db4-168cb95901db", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245088684200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12c1e272-d9e6-4072-bb43-d6d613709c19", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245088709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a54f749-6e5a-4813-bdd4-536c5eb23747", "name": "require SDK: toolchains,ArkTS; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245090348700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9425a838-52f1-4a22-8d01-a2f635a80e35", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245097931700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2c5b491-d9ab-4b3b-aff4-2adf3abda63d", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245097997600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e4e2a85-9505-4141-8213-941306655f5a", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245104266700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983b12c0-60b4-45a0-abbc-91068ebce3cf", "name": "hvigorfile, require result:  { harTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245119022700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc355a8a-94ff-494c-9fd0-f535a52cb236", "name": "hvigorfile, binding system plugins { harTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245119085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "315eb2ab-0d43-40de-a3a3-942479062b1a", "name": "<PERSON><PERSON><PERSON> ijkplayer Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245132261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14d9b933-eaa5-4d10-b8bb-c461760b962a", "name": "<PERSON><PERSON><PERSON> ijkplayer's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245132314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81db8b34-07b7-4714-97dd-4cac4d8e5c08", "name": "Start initialize module-target build option map, moduleName=ijkplayer, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245133779400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc08aee5-8e22-4b8f-ad6f-0738826f991a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245133828500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bdeffc9-e142-4d18-8cef-272eb9af6a85", "name": "Module 'ijkplayer' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245134431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fc78137-96a6-4a54-8519-50f41bc31f1d", "name": "End initialize module-target build option map, moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245134448200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42a1cb4e-cf13-48cb-aa1a-8a2b91fe3cbc", "name": "Module 'ijkplayer' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245134487100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d2d860-5446-4003-9309-351ecf2814b7", "name": "require SDK: toolchains,ArkTS,native; moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245136018200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75fc7ece-013d-42e0-bdb8-e8e9479620a6", "name": "<PERSON><PERSON><PERSON> ijkplayer task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245140437400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de12b11d-5414-48c6-85ee-3a65a830437c", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245141852800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e968e4-a129-4092-8cba-af19b808b7c2", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245141954200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e2c838-6ebc-4193-975a-14f6854ab388", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245142001000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1a987c4-7bce-4309-8cc5-97655a519ab4", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245142053500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "115e0461-a15b-4841-9c0b-d8aa0492f13b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245142067800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06d1bbc0-4761-4e21-a222-fcf2f28fc83a", "name": "<PERSON><PERSON><PERSON>_ijkplayer-2.0.3 Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245142910600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64961aef-0584-4b8a-9426-a8369fd83f6e", "name": "<PERSON><PERSON><PERSON> ohos_ijkplayer-2.0.3's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245142936100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19958a5f-b5a6-49fd-9668-fd7aff1d2199", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245145256200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d04df602-ff59-4c74-8b52-e2c2be66678d", "name": "Sdk init in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245154036100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e9ac093-4f2b-43fb-a02e-3897c449b427", "name": "project has submodules:entry,ijkplayer", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245181808800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81970d5a-9de3-4659-b243-67b8bf21b6ed", "name": "module:ijkplayer no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245184641400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30a6ee29-fb93-47a5-9891-968d12bf135a", "name": "Project task initialization takes 34 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245187661100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edf990eb-6dbe-4862-9143-09c3d1bff3a5", "name": "Sdk init in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245192369700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbca043-8a07-4722-b50e-74318e48200d", "name": "Sdk init in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245203624800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd61235-d0d4-41de-bd9e-ab2c72548da4", "name": "Configuration phase cost:170 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245204558300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "145683ad-a1d2-48c9-867a-56233d4ad3ba", "name": "Configuration task cost before running: 175 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245206524300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a46002a-9802-4cf4-ba05-d226f96ce485", "name": "ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245216529100, "endTime": 245230063100}, "additional": {"children": [], "state": "success", "detailId": "f613249b-b27e-46f5-a9d2-83a5ca81d65b", "logId": "26e4ebf2-96e1-4e73-9708-e27be49038a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f613249b-b27e-46f5-a9d2-83a5ca81d65b", "name": "create ijkplayer:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245208837000}, "additional": {"logType": "detail", "children": [], "durationId": "3a46002a-9802-4cf4-ba05-d226f96ce485"}}, {"head": {"id": "6dd7baf4-f54f-44ed-996d-195dd923ac69", "name": "ijkplayer : default@PreBuild start {\n  rss: 190926848,\n  heapTotal: 127025152,\n  heapUsed: 102323072,\n  external: 1233059,\n  arrayBuffers: 267703\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245216490500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4b44ee8-341f-4f13-a294-721da055dc4d", "name": "Executing task :ijkplayer:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245216550700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "769a8f17-e022-43f0-b26f-8abcec56a4bb", "name": "Incremental task ijkplayer:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245229754500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a276535-09a5-4ac9-9fff-4fbe23b934dc", "name": "ijkplayer : default@PreBuild end {\n  rss: 193032192,\n  heapTotal: 127025152,\n  heapUsed: 102448296,\n  external: 1233059,\n  arrayBuffers: 267703\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245229914600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e4ebf2-96e1-4e73-9708-e27be49038a4", "name": "UP-TO-DATE :ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245216529100, "endTime": 245230063100}, "additional": {"logType": "info", "children": [], "durationId": "3a46002a-9802-4cf4-ba05-d226f96ce485"}}, {"head": {"id": "8b662e7e-4e76-4575-b338-6323874a5898", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245234894300, "endTime": 247831846900}, "additional": {"children": ["cfd09e8f-7d20-41fc-aa6a-c85b208cca2d", "cd065c0f-a086-4eaa-88cb-9ba8a67339ab"], "state": "success", "detailId": "f095e3c9-8860-49fb-89b9-bd6992d656f9", "logId": "caaa16fc-ee04-4251-927b-b78a1b9add4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f095e3c9-8860-49fb-89b9-bd6992d656f9", "name": "create ijkplayer:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245233594700}, "additional": {"logType": "detail", "children": [], "durationId": "8b662e7e-4e76-4575-b338-6323874a5898"}}, {"head": {"id": "a076cfec-1f1f-44da-9dc1-f38db346c7c3", "name": "ijkplayer : default@BuildNativeWithCmake start {\n  rss: 193347584,\n  heapTotal: 127287296,\n  heapUsed: 102924248,\n  external: 1233059,\n  arrayBuffers: 267703\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245234858600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13988a97-3044-4c7d-be68-8c4a5ec30004", "name": "Executing task :ijkplayer:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245234942000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ba1f1be-f9d9-4486-9e46-a192d559f5d9", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245243901100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88935d79-0e53-4fe2-8f2a-41b47eb949d6", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245246250800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfd09e8f-7d20-41fc-aa6a-c85b208cca2d", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 15524, "tid": "Worker0", "startTime": 245469812200, "endTime": 247228753000}, "additional": {"children": [], "state": "success", "parent": "8b662e7e-4e76-4575-b338-6323874a5898", "logId": "ae9a1b83-bba1-41eb-a4b9-6f1a99afa6f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "9a2276ca-42ed-4de2-b805-ad7eb6a8721b", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245247461700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35fb8c74-5655-41d3-a85d-9e4071661495", "name": "default@BuildNativeWithCmake work[0] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245247549600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1c799ef-6a1f-4148-964e-e11908aced3e", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245251052200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "453a4da0-cbb2-456e-aa81-d043a5d968e8", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245252606500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd065c0f-a086-4eaa-88cb-9ba8a67339ab", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 15524, "tid": "Worker1", "startTime": 246147994000, "endTime": 247831649500}, "additional": {"children": [], "state": "success", "parent": "8b662e7e-4e76-4575-b338-6323874a5898", "logId": "f3d67faa-49e5-4097-8dfc-7165d23e08fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f8c952e2-8ba3-48e1-87f7-28f406f7c6c4", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245253345900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a08a83b-c171-4897-9e0c-2fd9b594bc41", "name": "default@BuildNativeWithCmake work[1] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245253396300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02da74ad-980b-4487-8d7e-6aa284306b09", "name": "ijkplayer : default@BuildNativeWithCmake end {\n  rss: 194596864,\n  heapTotal: 127287296,\n  heapUsed: 103364136,\n  external: 1233059,\n  arrayBuffers: 267703\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245253523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ab84186-5c0d-42d3-b8d2-f5b7a5c974f2", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245469916600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43bbc5a4-fef8-4ca5-8423-b48291c22798", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 246147684900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9102728-7b9d-4865-a3e8-b23778d611e9", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 246148017200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9e75d3-7142-42c4-9c2d-2f076b2f60c4", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247229264600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9a1b83-bba1-41eb-a4b9-6f1a99afa6f9", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 15524, "tid": "Worker0", "startTime": 245469812200, "endTime": 247228753000}, "additional": {"logType": "info", "children": [], "durationId": "cfd09e8f-7d20-41fc-aa6a-c85b208cca2d", "parent": "caaa16fc-ee04-4251-927b-b78a1b9add4a"}}, {"head": {"id": "4368e8fe-7105-4462-98e7-388779751fd6", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247768990800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "679b66b9-b87e-4162-b8a2-23b1724375c6", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247831710400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3d67faa-49e5-4097-8dfc-7165d23e08fd", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 15524, "tid": "Worker1", "startTime": 246147994000, "endTime": 247831649500}, "additional": {"logType": "info", "children": [], "durationId": "cd065c0f-a086-4eaa-88cb-9ba8a67339ab", "parent": "caaa16fc-ee04-4251-927b-b78a1b9add4a"}}, {"head": {"id": "caaa16fc-ee04-4251-927b-b78a1b9add4a", "name": "Finished :ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245234894300, "endTime": 247831846900}, "additional": {"logType": "info", "children": ["ae9a1b83-bba1-41eb-a4b9-6f1a99afa6f9", "f3d67faa-49e5-4097-8dfc-7165d23e08fd"], "durationId": "8b662e7e-4e76-4575-b338-6323874a5898"}}, {"head": {"id": "2caf4551-43ac-4017-8732-a10d036c12d9", "name": "ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247834694000, "endTime": 247834843100}, "additional": {"children": [], "state": "success", "detailId": "de2b4749-0a6c-46e7-9a4f-ff98f3f00f7b", "logId": "897ed8d1-e283-4c1f-946a-c3c609700875"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "de2b4749-0a6c-46e7-9a4f-ff98f3f00f7b", "name": "create ijkplayer:compileNative task", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247834536300}, "additional": {"logType": "detail", "children": [], "durationId": "2caf4551-43ac-4017-8732-a10d036c12d9"}}, {"head": {"id": "8edc113b-ce3e-49bc-b09e-712a7fd6b364", "name": "ijkplayer : compileNative start {\n  rss: 293421056,\n  heapTotal: 127287296,\n  heapUsed: 103557744,\n  external: 1233059,\n  arrayBuffers: 267703\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247834665300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a60a688-2723-4621-9741-0a5178f604a7", "name": "Executing task :ijkplayer:compileNative", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247834712600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2481a8ec-15bf-4a50-81eb-4af9ac32ba5d", "name": "ijkplayer : compileNative end {\n  rss: 293421056,\n  heapTotal: 127287296,\n  heapUsed: 103566952,\n  external: 1233059,\n  arrayBuffers: 267703\n}", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247834823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897ed8d1-e283-4c1f-946a-c3c609700875", "name": "Finished :ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247834694000, "endTime": 247834843100}, "additional": {"logType": "info", "children": [], "durationId": "2caf4551-43ac-4017-8732-a10d036c12d9"}}, {"head": {"id": "e9c17dfb-115c-4cb3-9bf9-015b6bab569a", "name": "BUILD SUCCESSFUL in 2 s 804 ms ", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247835243700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d4d220a3-9039-4afe-a93c-eb1ef94dce4c", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 245031800800, "endTime": 247835693600}, "additional": {"time": {"year": 2025, "month": 7, "day": 21, "hour": 22, "minute": 16}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "52ffaf7b-d383-4c6b-a35f-9ea01f5191cf", "name": "There is no need to refresh cache, since the incremental task ijkplayer:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 15524, "tid": "Main Thread", "startTime": 247835945600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}