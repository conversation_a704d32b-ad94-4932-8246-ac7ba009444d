# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
openharmonycheck() {
    res=0
    cd ${builddir}/${ARCH}-build
    ./diff_match_patch_test_string > ${logfile} 2>&1
    res=$?
    if [ $res -eq 0 ]
    then
        ./diff_match_patch_test_wstring > ${logfile} 2>&1
        res=$?
    fi

    cd $OLDPWD

    return $res
}
