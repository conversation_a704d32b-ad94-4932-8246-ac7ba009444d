# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=soxr
pkgver=0.1.3
pkgrel=0
pkgdesc="The SoX Resampler library 'libsoxr' performs one-dimensional sample-rate conversion—it may be used, for example, to resample PCM-encoded audio."
url="https://sourceforge.net/projects/soxr/"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL v2.1 or later")
depends=()
makedepends=()

source="https://sourceforge.net/projects/$pkgname/files/$pkgname-$pkgver-Source.tar.xz"

autounpack=true
downloadpackage=true
buildtools="cmake"
builddir=$pkgname-$pkgver-Source
packagename=$builddir.tar.xz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    # 交叉编译不支持ctest，故关闭tests打开examples，使用examples进行测试
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DBUILD_EXAMPLES=1 -DBUILD_TESTS=0 -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make VERBOSE=1 -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test
    # export LD_LIBRARY_PATH=$LYCIUM_ROOT/main/soxr/$builddir/$ARCH-build/src
    # cd $ARCH-build/examples
    # ./1-single-block 44100 48000
    # ./1a-lsr 44100 48000
    # cat ../../../bootsound_1ch.raw | ./2-stream 44100 48000 > test2_dst.raw
    # cat ../../../bootsound_2ch.raw | ./3-options-input-fn 44100 48000 2 > test3_dst.raw
    # cat ../../../bootsound_2ch.raw | ./4-split-channels 44100 48000 2 0 4 > test4_dst.raw
    # ./5-variable-rate 0 > test5_dst.raw
}

cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
