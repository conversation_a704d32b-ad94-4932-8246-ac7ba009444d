# Contributor: wuping<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=djvulibre
pkgver=3.5.27.1-7
pkgrel=0
pkgdesc="DjVu (pronounced \"déjà vu\") a set of compression technologies, a file format,and a software platform for the delivery over the Web of digital documents,scanned documents, and high resolution images."
url="https://github.com/barak/djvulibre"
archs=("armeabi-v7a" "arm64-v8a")
license=("GPLv2")
depends=()
makedepends=()
install=
source="https://github.com/barak/djvulibre/archive/refs/tags/debian/$pkgver+deb9u1.tar.gz"

autounpack=true
downloadpackage=true
patchflag=true
buildtools="configure"
builddir=$pkgname-"debian"-$pkgver-"deb9u1"
packagename=$builddir.tar.gz
source envset.sh
host=

prepare() {
    if $patchflag
    then
        cd $builddir
        # 由于编译找不到这几个函数：pthread_setcancelstate、pthread_setcanceltype、pthread_cancel因此打补丁
        patch -p1 < `pwd`/../djvulibre_oh_pkg.patch
        patchflag=false
        cd $OLDPWD
    fi
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
        export LDFLAGS="${OHOS_SDK}/native/llvm/lib/clang/${CLANG_VERSION}/lib/arm-linux-ohos/a7_hard_neon-vfpv4/libclang_rt.builtins.a ${LDFLAGS}"
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
        export LDFLAGS="${OHOS_SDK}/native/llvm/lib/clang/${CLANG_VERSION}/lib/aarch64-linux-ohos/libclang_rt.builtins.a ${LDFLAGS}"
    fi
    cp -rf $builddir $pkgname-$ARCH-build
    cd $pkgname-$ARCH-build
    ./autogen.sh > `pwd`/build.log 2>&1
    cd $OLDPWD
}

build() {
    cd $pkgname-$ARCH-build
    ./configure "$@" --host=$host --enable-static >> `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $pkgname-$ARCH-build
    make install >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    unset host
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 在OpenHarmony开发板中执行用例
    # ./c44 a.pbm a.djuv
    # ./c44 b.pbm b.djuv
    # ./djvm -c merge.djuv a.djuv b.djuv
}

cleanbuild(){
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build #${PWD}/$packagename
}
