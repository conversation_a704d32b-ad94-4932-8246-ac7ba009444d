diff -Naur libxls-1.6.2/configure.ac libxls-1.6.2-patch/configure.ac
--- libxls-1.6.2/configure.ac	2021-01-04 12:31:47.000000000 +0800
+++ libxls-1.6.2-patch/configure.ac	2023-04-21 14:23:05.765474123 +0800
@@ -37,7 +37,7 @@
 AC_PROG_CC
 AC_PROG_CC_C99
 AC_PROG_CXX
-AX_CXX_COMPILE_STDCXX_11([], [optional])
+#AX_CXX_COMPILE_STDCXX_11([], [optional])
 
 AS_IF([test "x$HAVE_CXX11" != x1], [
     AC_MSG_NOTICE([---])
@@ -48,8 +48,8 @@
 
 AC_CHECK_FUNCS([strdup wcstombs_l])
 AC_CHECK_HEADERS([wchar.h xlocale.h])
-AC_FUNC_MALLOC
-AC_FUNC_REALLOC
+#AC_FUNC_MALLOC
+#AC_FUNC_REALLOC
 AC_TYPE_SIZE_T
 
 AC_ARG_ENABLE([fuzz-testing], AS_HELP_STRING([--enable-fuzz-testing], ["Enable fuzz testing (requires Clang 6 or later)"]), [
