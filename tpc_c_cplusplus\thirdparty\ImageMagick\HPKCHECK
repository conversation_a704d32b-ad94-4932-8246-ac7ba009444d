# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the ImageMagick License (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
# 
#     https://imagemagick.org/script/license.php
# 
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd $builddir-$ARCH-build
    make check-TESTS > ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        mkdir -p ${LYCIUM_FAULT_PATH}/${pkgname}
        cp test-suite.log ${LYCIUM_FAULT_PATH}/${pkgname}/
    fi
    cd $OLDPWD
    return $res
}
