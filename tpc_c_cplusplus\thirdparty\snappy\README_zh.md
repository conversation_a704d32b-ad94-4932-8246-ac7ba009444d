# snappy 三方库说明
## 功能简介
Snappy是一个压缩/解压缩库。它的目标不是最大化 压缩，或与任何其他压缩库兼容;相反 它的目标是非常高的速度和合理的压缩。例如 与 zlib 的最快模式相比，Snappy 快了一个数量级 对于大多数输入，但生成的压缩文件从 20% 到 100%更大
## 使用约束
- IDE版本：DevEco Studio 3.1 Release
- SDK版本：ohos_sdk_public 4.0.8.1 (API Version 10 Release)
- 三方库版本：1.1.10
- Snappy是一个压缩/解压缩库。它的目标不是最大化 压缩，或与任何其他压缩库兼容;相反 它的目标是非常高的速度和合理的压缩。例如 与 zlib 的最快模式相比，Snappy 快了一个数量级 对于大多数输入，但生成的压缩文件从 20% 到 100%更大

## 集成方式
+ [应用hap包集成](docs/hap_integrate.md)
