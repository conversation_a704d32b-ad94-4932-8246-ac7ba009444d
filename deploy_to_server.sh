#!/bin/bash

# 服务器部署脚本 - 在本地Windows/MSYS2中运行

echo "========================================"
echo "OpenHarmony ijkplayer 服务器部署脚本"
echo "========================================"

# 配置变量
SERVER_IP=""
SERVER_USER="root"
SERVER_PATH="/root/ohos_ijkplayer-2.0.3"
LOCAL_PROJECT_PATH="/d/new/ohos_ijkplayer-2.0.3"
LOCAL_SDK_PATH="/d/harmonyFor/openSDK/11"

# 获取服务器信息
if [ -z "$SERVER_IP" ]; then
    read -p "请输入服务器IP地址: " SERVER_IP
fi

if [ -z "$SERVER_IP" ]; then
    echo "错误: 必须提供服务器IP地址"
    exit 1
fi

echo "服务器IP: $SERVER_IP"
echo "服务器用户: $SERVER_USER"
echo "服务器路径: $SERVER_PATH"

# 测试服务器连接
echo ""
echo "测试服务器连接..."
if ssh -o ConnectTimeout=10 "$SERVER_USER@$SERVER_IP" "echo '连接成功'"; then
    echo "✓ 服务器连接正常"
else
    echo "✗ 服务器连接失败，请检查IP地址和SSH配置"
    exit 1
fi

# 在服务器上准备环境
echo ""
echo "========================================"
echo "1. 准备服务器环境"
echo "========================================"

ssh "$SERVER_USER@$SERVER_IP" << 'EOF'
echo "更新系统包..."
apt update

echo "安装编译工具..."
apt install -y build-essential gcc g++ make cmake autoconf automake libtool \
    pkg-config git wget curl unzip zip tar gzip bzip2 xz-utils python3 \
    python3-pip ninja-build meson yasm nasm patch flex bison

echo "创建项目目录..."
mkdir -p /root/ohos_ijkplayer-2.0.3
mkdir -p /root/ohos-sdk

echo "服务器环境准备完成"
EOF

if [ $? -eq 0 ]; then
    echo "✓ 服务器环境准备完成"
else
    echo "✗ 服务器环境准备失败"
    exit 1
fi

# 上传项目文件
echo ""
echo "========================================"
echo "2. 上传项目文件"
echo "========================================"

echo "上传项目源码..."
rsync -avz --progress "$LOCAL_PROJECT_PATH/" "$SERVER_USER@$SERVER_IP:$SERVER_PATH/"

if [ $? -eq 0 ]; then
    echo "✓ 项目文件上传完成"
else
    echo "✗ 项目文件上传失败"
    exit 1
fi

# 上传SDK
echo ""
echo "上传OpenHarmony SDK..."
if [ -d "$LOCAL_SDK_PATH" ]; then
    rsync -avz --progress "$LOCAL_SDK_PATH/" "$SERVER_USER@$SERVER_IP:/root/ohos-sdk/11/"
    if [ $? -eq 0 ]; then
        echo "✓ SDK上传完成"
    else
        echo "✗ SDK上传失败"
        exit 1
    fi
else
    echo "⚠ 本地SDK目录不存在: $LOCAL_SDK_PATH"
    echo "请确保SDK路径正确，或在服务器上手动设置SDK"
fi

# 在服务器上设置环境变量和权限
echo ""
echo "========================================"
echo "3. 配置服务器环境"
echo "========================================"

ssh "$SERVER_USER@$SERVER_IP" << EOF
cd $SERVER_PATH

# 设置环境变量
echo 'export OHOS_SDK="/root/ohos-sdk/11"' >> ~/.bashrc
export OHOS_SDK="/root/ohos-sdk/11"

# 设置脚本权限
chmod +x prebuild.sh
chmod +x build_msys2.sh
chmod +x *.sh

# 检查环境
echo "检查编译工具..."
gcc --version | head -1
make --version | head -1
cmake --version | head -1

echo "检查SDK..."
if [ -d "/root/ohos-sdk/11" ]; then
    echo "✓ SDK目录存在"
    ls -la /root/ohos-sdk/11/ | head -5
else
    echo "⚠ SDK目录不存在"
fi

echo "服务器配置完成"
EOF

if [ $? -eq 0 ]; then
    echo "✓ 服务器配置完成"
else
    echo "✗ 服务器配置失败"
    exit 1
fi

# 提供下一步指令
echo ""
echo "========================================"
echo "部署完成！"
echo "========================================"
echo ""
echo "下一步操作："
echo "1. SSH连接到服务器:"
echo "   ssh $SERVER_USER@$SERVER_IP"
echo ""
echo "2. 进入项目目录:"
echo "   cd $SERVER_PATH"
echo ""
echo "3. 运行编译脚本:"
echo "   ./prebuild.sh"
echo ""
echo "4. 编译完成后，下载结果:"
echo "   scp -r $SERVER_USER@$SERVER_IP:$SERVER_PATH/ijkplayer/src/main/cpp/third_party/ ./compiled_libs/"
echo ""

# 提供一键编译选项
echo "========================================"
echo "一键编译选项"
echo "========================================"
read -p "是否现在就在服务器上开始编译？(y/N): " start_compile

if [[ "$start_compile" =~ ^[Yy]$ ]]; then
    echo ""
    echo "开始远程编译..."
    ssh "$SERVER_USER@$SERVER_IP" << EOF
cd $SERVER_PATH
export OHOS_SDK="/root/ohos-sdk/11"
echo "开始编译依赖库..."
./prebuild.sh
EOF
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 编译完成！"
        echo ""
        echo "下载编译结果:"
        mkdir -p ./compiled_libs
        scp -r "$SERVER_USER@$SERVER_IP:$SERVER_PATH/ijkplayer/src/main/cpp/third_party/" ./compiled_libs/
        echo ""
        echo "编译结果已下载到 ./compiled_libs/ 目录"
    else
        echo ""
        echo "❌ 编译失败，请检查服务器上的错误信息"
        echo "可以SSH到服务器查看详细日志"
    fi
fi

echo ""
echo "部署脚本执行完成！"
