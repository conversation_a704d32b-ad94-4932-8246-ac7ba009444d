diff -Naur libtess2-1.0.2-old/Example/example.c libtess2-1.0.2-diff/Example/example.c
--- libtess2-1.0.2-old/Example/example.c	2023-05-09 09:05:10.003166275 +0800
+++ libtess2-1.0.2-diff/Example/example.c	2023-05-09 09:51:02.808161326 +0800
@@ -3,7 +3,7 @@
 #include <stdlib.h>
 #include <string.h>
 #include <math.h>
-#include <GLFW/glfw3.h>
+//#include <GLFW/glfw3.h>
 #include "nanosvg.h"
 #include "tesselator.h"
 
@@ -52,28 +52,28 @@
 
 
 // Undefine this to see non-interactive heap allocator version.
-#define USE_POOL 1
+#define USE_POOL 0
 
 
 int run = 1;
 int cdt = 0;
 
-static void key(GLFWwindow* window, int key, int scancode, int action, int mods)
-{
-	TESS_NOTUSED(scancode);
-	TESS_NOTUSED(mods);
-	if (key == GLFW_KEY_ESCAPE && action == GLFW_PRESS)
-		glfwSetWindowShouldClose(window, GL_TRUE);
-	if (key == GLFW_KEY_SPACE && action == GLFW_PRESS)
-		run = !run;
-	if (key == GLFW_KEY_C && action == GLFW_PRESS)
-		cdt = !cdt;
-}
+//static void key(GLFWwindow* window, int key, int scancode, int action, int mods)
+//{
+//	TESS_NOTUSED(scancode);
+//	TESS_NOTUSED(mods);
+//	if (key == GLFW_KEY_ESCAPE && action == GLFW_PRESS)
+//		glfwSetWindowShouldClose(window, GL_TRUE);
+//	if (key == GLFW_KEY_SPACE && action == GLFW_PRESS)
+//		run = !run;
+//	if (key == GLFW_KEY_C && action == GLFW_PRESS)
+//		cdt = !cdt;
+//}
 
 int main(int argc, char *argv[])
 {
-	GLFWwindow* window;
-	const GLFWvidmode* mode;
+//	GLFWwindow* window;
+//	const GLFWvidmode* mode;
 	int width,height,i,j;
 	struct SVGPath* bg;
 	struct SVGPath* fg;
@@ -84,21 +84,21 @@
 	TESStesselator* tess = 0;
 	const int nvp = 3;
 	unsigned char* vflags = 0;
-#ifdef USE_POOL
-	struct MemPool pool;
-	unsigned char mem[1024*1024];
-	int nvflags = 0;
-#else
+//#ifdef USE_POOL
+//	struct MemPool pool;
+//	unsigned char mem[1024*1024];
+//	int nvflags = 0;
+//#else
 	int allocated = 0;
 	double t0 = 0, t1 = 0;
-#endif
-	TESS_NOTUSED(argc);
-	TESS_NOTUSED(argv);
-
-	if (!glfwInit()) {
-		printf("Failed to init GLFW.");
-		return -1;
-	}
+//#endif
+    TESS_NOTUSED(argc);
+    TESS_NOTUSED(argv);
+
+//	if (!glfwInit()) {
+//		printf("Failed to init GLFW.");
+//		return -1;
+//	}
 
 	printf("loading...\n");
 	// Load assets
@@ -159,20 +159,20 @@
 		}
 	}
 
-#ifdef USE_POOL
-
-	pool.size = 0;
-	pool.cap = sizeof(mem);
-	pool.buf = mem;
-	memset(&ma, 0, sizeof(ma));
-	ma.memalloc = poolAlloc;
-	ma.memfree = poolFree;
-	ma.userData = (void*)&pool;
-	ma.extraVertices = 256; // realloc not provided, allow 256 extra vertices.
+//#ifdef USE_POOL
+//
+//	pool.size = 0;
+//	pool.cap = sizeof(mem);
+//	pool.buf = mem;
+//	memset(&ma, 0, sizeof(ma));
+//	ma.memalloc = poolAlloc;
+//	ma.memfree = poolFree;
+//	ma.userData = (void*)&pool;
+//	ma.extraVertices = 256; // realloc not provided, allow 256 extra vertices.
+//
+//#else
 
-#else
-
-	t0 = glfwGetTime();
+//	t0 = glfwGetTime();
 
 	memset(&ma, 0, sizeof(ma));
 	ma.memalloc = stdAlloc;
@@ -206,26 +206,26 @@
 	if (!tessTesselate(tess, TESS_WINDING_POSITIVE, TESS_POLYGONS, nvp, 2, 0))
 		return -1;
 
-	t1 = glfwGetTime();
-
-	printf("Time: %.3f ms\n", (t1 - t0) * 1000.0f);
-	printf("Memory used: %.1f kB\n", allocated/1024.0f);
+//	t1 = glfwGetTime();
+//
+//	printf("Time: %.3f ms\n", (t1 - t0) * 1000.0f);
+//	printf("Memory used: %.1f kB\n", allocated/1024.0f);
+
+//#endif
+
+//	mode = glfwGetVideoMode(glfwGetPrimaryMonitor());
+//	width = mode->width - 40;
+//	height = mode->height - 80;
+//    window = glfwCreateWindow(width, height, "Libtess2 Demo", NULL, NULL);
+//	if (!window) {
+//		glfwTerminate();
+//		return -1;
+//	}
 
-#endif
+//	glfwSetKeyCallback(window, key);
+//    glfwMakeContextCurrent(window);
 
-	mode = glfwGetVideoMode(glfwGetPrimaryMonitor());
-	width = mode->width - 40;
-	height = mode->height - 80;
-    window = glfwCreateWindow(width, height, "Libtess2 Demo", NULL, NULL);
-	if (!window) {
-		glfwTerminate();
-		return -1;
-	}
-
-	glfwSetKeyCallback(window, key);
-	glfwMakeContextCurrent(window);
-
-	// Adjust bounds so that we get nice view of the bg.
+    // Adjust bounds so that we get nice view of the bg.
 	cx = (bounds[0]+bounds[2])/2;
 	cy = (bounds[3]+bounds[1])/2;
 	w = (bounds[2]-bounds[0])/2;
@@ -233,176 +233,178 @@
 	view[2] = cx + w*1.2f;
 	view[1] = cy - w*1.2f*(float)height/(float)width;
 	view[3] = cy + w*1.2f*(float)height/(float)width;
+    
+	printf("view0: %f\n", view[0]);
+	printf("view1: %f\n", view[2]);
+//	glfwSetTime(0);
+//
+//	while (!glfwWindowShouldClose(window))
+//	{
+//		int winWidth, winHeight;
+//		int fbWidth, fbHeight;
+//		float pxr, ct;
+//
+//		glfwGetWindowSize(window, &winWidth, &winHeight);
+//		glfwGetFramebufferSize(window, &fbWidth, &fbHeight);
+//
+//		// Calculate pixel ration for hi-dpi devices.
+//		pxr = (float)fbWidth / (float)winWidth;
+//
+//		ct = (float)glfwGetTime();
+//		if (run) t += ct - pt;
+//		pt = ct;
+//
+//		// Update and render
+//		glViewport(0, 0, fbWidth, fbHeight);
+//		glClearColor(0.3f, 0.3f, 0.32f, 1.0f);
+//		glClear(GL_COLOR_BUFFER_BIT|GL_DEPTH_BUFFER_BIT);
+//		glEnable(GL_BLEND);
+//		glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
+//		glDisable(GL_TEXTURE_2D);
+//		glMatrixMode(GL_PROJECTION);
+//		glLoadIdentity();
+//		glOrtho(view[0],view[2],view[1],view[3],-1,1);
+//		glMatrixMode(GL_MODELVIEW);
+//		glLoadIdentity();
+//		glDisable(GL_DEPTH_TEST);
+//		glEnable(GL_BLEND);
+//		glBlendFunc(GL_SRC_ALPHA,GL_ONE_MINUS_SRC_ALPHA);
+//
+//#ifdef USE_POOL
+//		pool.size = 0; // reset pool
+//		tess = tessNewTess(&ma);
+//		if (tess)
+//		{
+//			tessSetOption(tess, TESS_CONSTRAINED_DELAUNAY_TRIANGULATION, cdt);
+//
+//			offx = (view[2]+view[0])/2 + sinf(t) * (view[2]-view[0])/2;
+//			offy = (view[3]+view[1])/2 + cosf(t*3.13f) * (view[3]-view[1])/6;
+//
+//			for (it = fg; it != NULL; it = it->next)
+//			{
+//				for (i = 0; i < it->npts; ++i)
+//				{
+//					it->pts[i*2] += offx;
+//					it->pts[i*2+1] += offy;
+//				}
+//			}
+//
+//			for (it = bg; it != NULL; it = it->next)
+//				tessAddContour(tess, 2, it->pts, sizeof(float)*2, it->npts);
+//			for (it = fg; it != NULL; it = it->next)
+//				tessAddContour(tess, 2, it->pts, sizeof(float)*2, it->npts);
+//
+//			for (it = fg; it != NULL; it = it->next)
+//			{
+//				for (i = 0; i < it->npts; ++i)
+//				{
+//					it->pts[i*2] -= offx;
+//					it->pts[i*2+1] -= offy;
+//				}
+//			}
+//
+//			// First combine contours and then triangulate, this removes unnecessary inner vertices.
+//			if (tessTesselate(tess, TESS_WINDING_POSITIVE, TESS_BOUNDARY_CONTOURS, 0, 0, 0))
+//			{
+//				const float* verts = tessGetVertices(tess);
+//				const int* vinds = tessGetVertexIndices(tess);
+//				const int nverts = tessGetVertexCount(tess);
+//				const int* elems = tessGetElements(tess);
+//				const int nelems = tessGetElementCount(tess);
+//
+//				if (nverts > nvflags)
+//				{
+//					if (vflags)
+//						free(vflags);
+//					nvflags = nverts;
+//					vflags = (unsigned char*)malloc(sizeof(unsigned char)*nvflags);
+//				}
+//
+//				if (vflags)
+//				{
+//					// Vertex indices describe the order the indices were added and can be used
+//					// to map the tesselator output to input. Vertices marked as TESS_UNDEF
+//					// are the ones that were created at the intersection of segments.
+//					// That is, if vflags is set it means that the vertex comes from intersegment.
+//					for (i = 0; i < nverts; ++i)
+//						vflags[i] = vinds[i] == TESS_UNDEF ? 1 : 0;
+//				}
+//
+//				for (i = 0; i < nelems; ++i)
+//				{
+//					int b = elems[i*2];
+//					int n = elems[i*2+1];
+//					tessAddContour(tess, 2, &verts[b*2], sizeof(float)*2, n);
+//				}
+//				if (!tessTesselate(tess, TESS_WINDING_POSITIVE, TESS_POLYGONS, nvp, 2, 0))
+//					tess = 0;
+//			}
+//			else
+//				tess = 0;
+//		}
+//#endif
+//
+//		// Draw tesselated pieces.
+//		if (tess)
+//		{
+//			const float* verts = tessGetVertices(tess);
+//			const int* vinds = tessGetVertexIndices(tess);
+//			const int* elems = tessGetElements(tess);
+//			const int nverts = tessGetVertexCount(tess);
+//			const int nelems = tessGetElementCount(tess);
+//
+//			// Draw polygons.
+//			glColor4ub(255,255,255,128);
+//			for (i = 0; i < nelems; ++i)
+//			{
+//				const int* p = &elems[i*nvp];
+//				glBegin(GL_TRIANGLE_FAN);
+//				for (j = 0; j < nvp && p[j] != TESS_UNDEF; ++j)
+//					glVertex2f(verts[p[j]*2], verts[p[j]*2+1]);
+//				glEnd();
+//			}
+//
+//			glLineWidth(1.0f * pxr);
+//			glPointSize(3.0f * pxr);
+//
+//			glColor4ub(0,0,0,16);
+//			for (i = 0; i < nelems; ++i)
+//			{
+//				const int* p = &elems[i*nvp];
+//				glBegin(GL_LINE_LOOP);
+//				for (j = 0; j < nvp && p[j] != TESS_UNDEF; ++j)
+//					glVertex2f(verts[p[j]*2], verts[p[j]*2+1]);
+//				glEnd();
+//			}
+//
+//			glColor4ub(0,0,0,128);
+//			glBegin(GL_POINTS);
+//			for (i = 0; i < nverts; ++i)
+//			{
+//				if (vflags && vflags[vinds[i]])
+//					glColor4ub(255,0,0,192);
+//				else
+//					glColor4ub(0,0,0,128);
+//				glVertex2f(verts[i*2], verts[i*2+1]);
+//			}
+//			glEnd();
+//
+//			glPointSize(1.0f);
+//		}
+//
+//		glEnable(GL_DEPTH_TEST);
+//		glfwSwapBuffers(window);
+//		glfwPollEvents();
+//	}
+//
+//	if (tess) tessDeleteTess(tess);
+//
+//	if (vflags)
+//		free(vflags);
+//
+//	svgDelete(bg);
+//	svgDelete(fg);
 
-	glfwSetTime(0);
-
-	while (!glfwWindowShouldClose(window))
-	{
-		int winWidth, winHeight;
-		int fbWidth, fbHeight;
-		float pxr, ct;
-
-		glfwGetWindowSize(window, &winWidth, &winHeight);
-		glfwGetFramebufferSize(window, &fbWidth, &fbHeight);
-
-		// Calculate pixel ration for hi-dpi devices.
-		pxr = (float)fbWidth / (float)winWidth;
-
-		ct = (float)glfwGetTime();
-		if (run) t += ct - pt;
-		pt = ct;
-
-		// Update and render
-		glViewport(0, 0, fbWidth, fbHeight);
-		glClearColor(0.3f, 0.3f, 0.32f, 1.0f);
-		glClear(GL_COLOR_BUFFER_BIT|GL_DEPTH_BUFFER_BIT);
-		glEnable(GL_BLEND);
-		glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
-		glDisable(GL_TEXTURE_2D);
-		glMatrixMode(GL_PROJECTION);
-		glLoadIdentity();
-		glOrtho(view[0],view[2],view[1],view[3],-1,1);
-		glMatrixMode(GL_MODELVIEW);
-		glLoadIdentity();
-		glDisable(GL_DEPTH_TEST);
-		glEnable(GL_BLEND);
-		glBlendFunc(GL_SRC_ALPHA,GL_ONE_MINUS_SRC_ALPHA);
-
-#ifdef USE_POOL
-		pool.size = 0; // reset pool
-		tess = tessNewTess(&ma);
-		if (tess)
-		{
-			tessSetOption(tess, TESS_CONSTRAINED_DELAUNAY_TRIANGULATION, cdt);
-
-			offx = (view[2]+view[0])/2 + sinf(t) * (view[2]-view[0])/2;
-			offy = (view[3]+view[1])/2 + cosf(t*3.13f) * (view[3]-view[1])/6;
-
-			for (it = fg; it != NULL; it = it->next)
-			{
-				for (i = 0; i < it->npts; ++i)
-				{
-					it->pts[i*2] += offx;
-					it->pts[i*2+1] += offy;
-				}
-			}
-
-			for (it = bg; it != NULL; it = it->next)
-				tessAddContour(tess, 2, it->pts, sizeof(float)*2, it->npts);
-			for (it = fg; it != NULL; it = it->next)
-				tessAddContour(tess, 2, it->pts, sizeof(float)*2, it->npts);
-
-			for (it = fg; it != NULL; it = it->next)
-			{
-				for (i = 0; i < it->npts; ++i)
-				{
-					it->pts[i*2] -= offx;
-					it->pts[i*2+1] -= offy;
-				}
-			}
-
-			// First combine contours and then triangulate, this removes unnecessary inner vertices.
-			if (tessTesselate(tess, TESS_WINDING_POSITIVE, TESS_BOUNDARY_CONTOURS, 0, 0, 0))
-			{
-				const float* verts = tessGetVertices(tess);
-				const int* vinds = tessGetVertexIndices(tess);
-				const int nverts = tessGetVertexCount(tess);
-				const int* elems = tessGetElements(tess);
-				const int nelems = tessGetElementCount(tess);
-
-				if (nverts > nvflags)
-				{
-					if (vflags)
-						free(vflags);
-					nvflags = nverts;
-					vflags = (unsigned char*)malloc(sizeof(unsigned char)*nvflags);
-				}
-
-				if (vflags)
-				{
-					// Vertex indices describe the order the indices were added and can be used
-					// to map the tesselator output to input. Vertices marked as TESS_UNDEF
-					// are the ones that were created at the intersection of segments.
-					// That is, if vflags is set it means that the vertex comes from intersegment.
-					for (i = 0; i < nverts; ++i)
-						vflags[i] = vinds[i] == TESS_UNDEF ? 1 : 0;
-				}
-
-				for (i = 0; i < nelems; ++i)
-				{
-					int b = elems[i*2];
-					int n = elems[i*2+1];
-					tessAddContour(tess, 2, &verts[b*2], sizeof(float)*2, n);
-				}
-				if (!tessTesselate(tess, TESS_WINDING_POSITIVE, TESS_POLYGONS, nvp, 2, 0))
-					tess = 0;
-			}
-			else
-				tess = 0;
-		}
-#endif
-
-		// Draw tesselated pieces.
-		if (tess)
-		{
-			const float* verts = tessGetVertices(tess);
-			const int* vinds = tessGetVertexIndices(tess);
-			const int* elems = tessGetElements(tess);
-			const int nverts = tessGetVertexCount(tess);
-			const int nelems = tessGetElementCount(tess);
-
-			// Draw polygons.
-			glColor4ub(255,255,255,128);
-			for (i = 0; i < nelems; ++i)
-			{
-				const int* p = &elems[i*nvp];
-				glBegin(GL_TRIANGLE_FAN);
-				for (j = 0; j < nvp && p[j] != TESS_UNDEF; ++j)
-					glVertex2f(verts[p[j]*2], verts[p[j]*2+1]);
-				glEnd();
-			}
-
-			glLineWidth(1.0f * pxr);
-			glPointSize(3.0f * pxr);
-
-			glColor4ub(0,0,0,16);
-			for (i = 0; i < nelems; ++i)
-			{
-				const int* p = &elems[i*nvp];
-				glBegin(GL_LINE_LOOP);
-				for (j = 0; j < nvp && p[j] != TESS_UNDEF; ++j)
-					glVertex2f(verts[p[j]*2], verts[p[j]*2+1]);
-				glEnd();
-			}
-
-			glColor4ub(0,0,0,128);
-			glBegin(GL_POINTS);
-			for (i = 0; i < nverts; ++i)
-			{
-				if (vflags && vflags[vinds[i]])
-					glColor4ub(255,0,0,192);
-				else
-					glColor4ub(0,0,0,128);
-				glVertex2f(verts[i*2], verts[i*2+1]);
-			}
-			glEnd();
-
-			glPointSize(1.0f);
-		}
-
-		glEnable(GL_DEPTH_TEST);
-		glfwSwapBuffers(window);
-		glfwPollEvents();
-	}
-
-	if (tess) tessDeleteTess(tess);
-
-	if (vflags)
-		free(vflags);
-
-	svgDelete(bg);
-	svgDelete(fg);
-
-	glfwTerminate();
+//	glfwTerminate();
 	return 0;
 }
diff -Naur libtess2-1.0.2-old/premake4.lua libtess2-1.0.2-diff/premake4.lua
--- libtess2-1.0.2-old/premake4.lua	2023-05-09 09:05:10.003166275 +0800
+++ libtess2-1.0.2-diff/premake4.lua	2023-05-09 09:17:09.481460783 +0800
@@ -32,9 +32,7 @@
 		targetdir("Build")
 	 
 		configuration { "linux" }
-			 linkoptions { "`pkg-config --libs glfw3`" }
-			 links { "GL", "GLU", "m", "GLEW" }
-			 defines { "NANOVG_GLEW" }
+			 links {"m"}
 
 		configuration { "windows" }
 			 links { "glfw3", "gdi32", "winmm", "user32", "GLEW", "glu32","opengl32" }
