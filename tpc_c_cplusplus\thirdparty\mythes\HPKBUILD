# Contributor: wuping<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=mythes
pkgver=v1.2.5
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL")
depends=("hunspell")
makedepends=()

source="https://github.com/hunspell/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"
builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz
source envset.sh
host=

prepare() {
    cp -r $builddir $pkgname-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
        export LDFLAGS="${OHOS_SDK}/native/llvm/lib/clang/$CLANG_VERSION/lib/arm-linux-ohos/a7_hard_neon-vfpv4/libclang_rt.builtins.a ${LDFLAGS}"
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
        export LDFLAGS="${OHOS_SDK}/native/llvm/lib/clang/$CLANG_VERSION/lib/aarch64-linux-ohos/libclang_rt.builtins.a ${LDFLAGS}"
    fi
    cd $pkgname-$ARCH-build/
    ./autogen.sh > `pwd`/build.log 2>&1
    cd $OLDPWD
}

build() {
    cd $pkgname-$ARCH-build
    PKG_CONFIG_PATH="${pkgconfigpath}" ./configure "$@" --host=$host >> `pwd`/build.log 2>&1
    make V=1 -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $pkgname-$ARCH-build
    make install >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    # 生成测试可执行文件
    cd $pkgname-$ARCH-build
    make check HUNSPELL_LIBS=$LYCIUM_ROOT/usr/hunspell/$ARCH/lib/libhunspell-1.7.so  >> `pwd`/build.log 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    unset host
    echo "The test must be on an OpenHarmony device!"
    # 在OpenHarmony开发板中执行用例
    # ./example th_en_US_new.idx th_en_US_new.dat checkme.lst
    # ./example morph.idx morph.dat morph.lst morph.aff morph.dic
}

cleanbuild(){
    rm -rf ${PWD}/$builddir $pkgname-armeabi-v7a-build  $pkgname-arm64-v8a-build #${PWD}/$packagename
}
