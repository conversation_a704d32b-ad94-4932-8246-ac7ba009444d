# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL> luozhu <<EMAIL>>
# <AUTHOR> <EMAIL> 

pkgname=libexif
pkgver=v0.6.24
pkgrel=0
pkgdesc="A library for parsing, editing, and saving EXIF data"
url="https://libexif.github.io/"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPLv2.1")
depends=()
makedepends=()
source="https://github.com/libexif/$pkgname/releases/download/$pkgver/$pkgname-${pkgver:1}.tar.bz2"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.bz2

source envset.sh
host=
prepare() {
    cp -arf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    else
        echo "${ARCH} not support"
	return -1
    fi
}

build() {
    cd $builddir-$ARCH-build
    ./configure "$@" --host=$host > $buildlog 2>&1
    $MAKE V=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    $MAKE install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    cd $builddir-$ARCH-build/test
    $MAKE test-extract test-fuzzer test-gps test-integers test-mem test-mnote test-null test-parse \
        test-parse-from-data test-sorted test-tagtable test-value >> $buildlog 2>&1
    cd $OLDPWD
    echo "The test must be on an OpenHarmony device!"
    # cd $builddir-${ARCH}-build/test
    # 逐个执行测试用例
}

recoverpkgbuildenv(){
    unset host
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    else
        echo "${ARCH} not support"
	return -1
    fi
}

cleanbuild() {
    rm -rf ${PWD}/$builddir ${PWD}/$builddir-armeabi-v7a-build ${PWD}/$builddir-arm64-v8a-build #${PWD}/$packagename
}
