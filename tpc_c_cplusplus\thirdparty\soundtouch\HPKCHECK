# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
linuxresult=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/soundout.wav   # 在linux执行生成的结果
testfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/soundtest.wav
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
openharmonycheck() {
    res=0
    cd ${builddir}/${ARCH}-build
    ./soundstretch $testfile out.wav -tempo=+50 -pitch=+20 > ${logfile} 2>&1
    res=$?
    cd $OLDPWD

    return $res
}
