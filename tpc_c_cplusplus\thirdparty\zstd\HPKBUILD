# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>> luozhu <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=zstd
giteepkgname=facebook-$pkgname
pkgver=v1.5.4
pkgrel=0
pkgdesc="Zstandard, or zstd as short version, is a fast lossless compression algorithm, targeting real-time compression scenarios at zlib-level and better compression ratios"
url="https://github.com/facebook/zstd"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD or GPLv2")
depends=()
makedepends=()

# 官方下载地址: https://github.com/facebook/$pkgname/releases/download/$pkgver/$pkgname-1.5.4.tar.gz，因网络原因采用gitee mirrors
source="https://gitee.com/mirrors/$giteepkgname/repository/archive/$pkgver.zip"

autounpack=true
downloadpackage=true

builddir=$giteepkgname-${pkgver}
packagename=$builddir.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    # fix this OHOS_SDK 中的cmake版本过低，无法识别zstd项目中使用的cmake函数，因此替换为系统安装的cmake
    # 此方法编译的zstd没有soname和软连接，被其他项目依赖时，将全路径依赖
    # 规避上面问题的方法添加OHOS系统识别到开发机的cmake目录，具体具体如下
    # cp $OHOS_SDK/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/OHOS.cmake xxx(代表你开发机安装的cmake的路径)/cmake-3.26.3-linux-x86_64/share/cmake-3.26/Modules/Platform
    cmake "$@" -B$ARCH-build -S./build/cmake -DZSTD_BUILD_TESTS=ON > $buildlog 2>&1
    $MAKE -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
