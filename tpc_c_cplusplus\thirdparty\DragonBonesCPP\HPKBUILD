# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=DragonBonesCPP
pkgver=5.0
pkgrel=0
pkgdesc="DragonBones is a set of skeletal animation toolsets, first developed using Flash and ActionScript 3.0 languages, mainly used in Flash games, and is now widely used in web and mobile game projects"
url="http://www.dragonbones.com//"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD")
depends=()
makedepends=()
# 官方下载地址 https://github.com/DragonBones/${pkgname}.git 受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/lycium_pkg_mirror/${pkgname}.git"

downloadpackage=false
autounpack=false
buildtools="cmake"

builddir=${pkgname}-${pkgver}
packagename=$builddir.tar.gz

prepare_flag="false"
prepare() {
    if [ $prepare_flag == "true" ] 
    then 
        return 0
    fi

    git clone ${source} $builddir
    ret=$?
    if [ $ret -ne 0 ]
    then
        echo "fail $ARCH:git clone ${source} ret=" $ret
        return 1
    fi

    cd $builddir
    git checkout -b b5.0 origin/5.0
    # 打补丁添加编译测试代码
    patch < ../DragonBonesCPP_oh_pkg.patch
    prepare_flag="true"
    cd $OLDPWD
}

build() {

    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > $ARCH-build.log 2>&1
    make VERBOSE=1 -j4 -C $ARCH-build >> ${ARCH}-build.log 2>&1
    # 对最关键一步的退出码进行判断
    ret=$?
    cd $OLDPWD
    return $ret
}

# 打包安装
package() {
    cd $builddir
    make -C $ARCH-build install >> $ARCH-build.log 2>&1
    cd $OLDPWD
}

# 进行测试的准备和说明
check() {
    echo "The test must be on an OpenHarmony device!"
    # 测试方式
    # 进入构建目录
    # 执行: ./DragonBones_test
}

# 清理环境fmt
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
