# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=libfuse
pkgver=fuse-3.16.2
pkgrel=0
pkgdesc="The reference implementation of the Linux FUSE (Filesystem in Userspace) interface"
url="https://github.com/libfuse/libfuse"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL")
depends=()
makedepends=("meson")

source="https://github.com/libfuse/$pkgname/archive/refs/tags/$pkgver.tar.gz"

buildtools="meson"

builddir=$pkgname-$pkgver
packagename=$builddir".tar.gz"

patchflag=true

prepare() {
    if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../libfuse_oh_pkg.patch > $publicbuildlog 2>&1
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    cp $ARCH-cross-file.txt $builddir
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ohos_sdk_path=${OHOS_SDK//\//\\\/}
    sed -i 's/ohos_sdk/'"$ohos_sdk_path"'/g' $ARCH-cross-file.txt
    meson $ARCH-build --cross-file $ARCH-cross-file.txt --prefix=$LYCIUM_ROOT/usr/$pkgname/$ARCH > $buildlog 2>&1
    $Ninja -v -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $Ninja -v -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #$packagename
}
