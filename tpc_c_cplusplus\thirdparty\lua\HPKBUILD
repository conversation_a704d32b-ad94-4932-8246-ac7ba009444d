# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=lua
pkgver=v5.4.6
pkgrel=0
pkgdesc="Lua is a powerful, efficient, lightweight, embeddable scripting language."
url="https://www.lua.org"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT License")
depends=()
makedepends=()
source="https://github.com/lua/$pkgname/archive/refs/tags/$pkgver.tar.gz"

downloadpackage=true
autounpack=true
buildtools="make"

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

source envset.sh

cc=
ar=
ld=
prepare() {
    cp -rf $builddir $builddir-$ARCH-build/
    if [ $ARCH == "armeabi-v7a" ];then
        setarm32ENV
        cc=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang
    elif [ $ARCH == "arm64-v8a" ];then
        setarm64ENV
        cc=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang
    else
        echo "$ARCH not support"
        return -1
    fi

    ld=${OHOS_SDK}/native/llvm/bin/ld.lld
    ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
}

build() {
    $MAKE CC=${cc} LD=${ld} CFLAGS=-DLUA_USE_LINUX MYLIBS=-ldl -j4 -C $builddir-$ARCH-build VERBOSE=1 > \
	    $builddir-$ARCH-build/build.log 2>&1
    ret=$?
    return $ret
}

package() {
    # lua 5.4.6不支持make install，故需要copy文件
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin
    find ./$builddir-$ARCH-build -name '*.so' -exec cp -af {} $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/ \;
    find ./$builddir-$ARCH-build -name 'liblua.a' -exec cp -af {} $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/ \;
    find ./$builddir-$ARCH-build -name 'lua' -exec cp -af {} $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin/ \;
    find ./$builddir-$ARCH-build -name 'luac' -exec cp -af {} $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin/ \;
    find ./$builddir-$ARCH-build -name '*.h' -exec cp -af {} $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/ \;
}

recoverpkgbuildenv() {
    unset cc ar ld
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf $builddir-$ARCH-build
    rm -rf $builddir # $packagename
}
