# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>
pkgname=json-c
pkgver=0.16-20220414
pkgrel=0
pkgdesc="JSON-C implements a reference counting object model that allows you to easily construct JSON objects in C."
url="https://github.com/json-c/json-c"
archs=("armeabi-v7a" "arm64-v8a")
license=()
depends=()
makedepends=()

# 官方下载地址https://github.com/json-c/$pkgname/archive/refs/tags/$pkgname-$pkgver.tar.gz受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgname-$pkgver.zip"

autounpack=true
downloadpackage=true

builddir=$pkgname-$pkgname-${pkgver}
packagename=$builddir.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DDISABLE_WERROR=ON -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
