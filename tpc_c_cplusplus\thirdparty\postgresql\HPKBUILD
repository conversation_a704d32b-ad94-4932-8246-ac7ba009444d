# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the ImageMagick License (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
# 
#     http://www.apache.org/licenses/LICENSE-2.0
# 
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=postgresql
pkgver=16.3
pkgrel=0
pkgdesc="PostgreSQL is a powerful open-source object-relational database system."
url="https://www.postgresql.org/"
archs=("armeabi-v7a" "arm64-v8a")
license=("PostgreSQL License")
depends=("zlib" "icu" "tzdb")
makedepends=()

source="https://ftp.postgresql.org/pub/source/v${pkgver}/${pkgname}-${pkgver}.tar.gz"

downloadpackage=true

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz
buildtools="configure"

source envset.sh
host=

prepare() {
    cp -arf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    else
        echo "${ARCH} not support"
        return -1
    fi
}

build() {
    cd $builddir-$ARCH-build
    export LDFLAGS_EX_BE="-Wl,--export-dynamic" #源库交叉编译默认关闭server服务，需手动开启
    export ICU_CFLAGS="-I${LYCIUM_ROOT}/usr/icu/${ARCH}/include"
    export ICU_LIBS="-L${LYCIUM_ROOT}/usr/icu/${ARCH}/lib -licuuc -licui18n -licudata"
    ./configure --without-readline --enable-debug "$@" --host=$host > $buildlog 2>&1
    $MAKE > $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    $MAKE install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    #make check
}


# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir 
    rm -rf ${PWD}/$builddir-arm64-v8a-build 
    rm -rf ${PWD}/$builddir-armeabi-v7a-build 
}

