# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, wen fan <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=JRTPLIB
pkgver=v3.11.2
pkgrel=
pkgdesc="The library offers support for the Real-time Transport Protocol (RTP), defined in RFC 3550."
url=https://github.com/j0r1/JRTPLIB
archs=(armeabi-v7a arm64-v8a)
license=("MIT License")
depends=()
makedepends=()

source=https://github.com/j0r1/${pkgname}/archive/refs/tags/${pkgver}.tar.gz

autounpack=true
downloadpackage=true
buildtools=cmake
patchflag=true

builddir=${pkgname}-${pkgver:1}
packagename=${builddir}.tar.gz

prepare() {
    mkdir -p ${builddir}/${ARCH}-build
    if ${patchflag}
    then
        cd ${builddir}
        patch -p1 < ../${pkgname}_oh_pkg.patch || return -1
        echo "patching success"
        patchflag=false
        cd ${OLDPWD}
    fi

    return 0
}

build() {
    cd ${builddir}
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -B${ARCH}-build \
    -DJRTPLIB_COMPILE_TESTS=ON -S./ -L > ${buildlog} 2>&1 || return -1

    ${MAKE} -C ${ARCH}-build >> ${buildlog} 2>&1 || return -1
    cd ${OLDPWD}
    return 0
}

package() {
    cd ${builddir}/${ARCH}-build
    ${MAKE} install >> ${buildlog} 2>&1 || return -1
    cd ${OLDPWD}
    return 0
}

check() {
    echo "The test must be on an OpenHarmony device!"
    return 0
}

cleanbuild() {
    rm -rf ${PWD}/${builddir}
    return 0
}
