# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>
pkgname=gmp
pkgver=6.2.1
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("gplv2" "gplv3")
depends=()
makedepends=()

source="https://gmplib.org/download/$pkgname/$pkgname-$pkgver.tar.xz"

downloadpackage=true
autounpack=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.xz

source envset.sh
host=

prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
}

build() {
    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host > `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    unset host
}

check() {
    cd $builddir/$ARCH-build
    make libtests.la t-bswap t-constants t-count_zeros t-hightomask t-modlinv t-popc t-parity t-sub -C tests/ >> `pwd`/build.log 2>&1
    cd $OLDPWD    
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi

    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # make check-TESTS VERBOSE=1 -C tests/
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
