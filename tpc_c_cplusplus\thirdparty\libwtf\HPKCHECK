# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
openharmonycheck() {
    res=0
    cd ${builddir}/${ARCH}-build
    # 拷贝自动化测试需要的so文件
    mkdir -p /usr/local/lib/libwtf
    cp ${LYCIUM_ROOT}/../lycium/usr/icu/arm64-v8a/lib/* /usr/local/lib/libwtf/
    cp ./lib/libgtest.so /usr/local/lib/libwtf/
	export LD_LIBRARY_PATH=/usr/local/lib/libwtf:$LD_LIBRARY_PATH
    # 测试前注意时区改为GMT-07:00,否则会夏令时检测会不通过
    ctest > ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        mkdir -p ${LYCIUM_FAULT_PATH}/${pkgname}
        cp Testing/Temporary/LastTest.log ${LYCIUM_FAULT_PATH}/${pkgname}/
    fi
    rm -rf /usr/local/lib/libwtf
    cd $OLDPWD
    return $res
}
