{"version": "1.0", "events": [{"head": {"id": "40e36d80-e567-4486-b0b0-957af476f2ae", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 122590566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42b329e2-1263-472c-8c97-f38dedbc415e", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 122605391900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d550b69b-d13a-496d-af3c-3d90df55b7c8", "name": "hvigor daemon: Socket will be closed. socketId=xgfaXs3UFJmE8JZNAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 122606634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e47e356d-d1f1-43cc-b0dd-124bbea1dc06", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2544,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"47188334a2df522b9a2bcfd152364b7cbf9cf7bc\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753106925552,\"createdBy\":\"deveco\",\"sessionId\":\"00000050b3480f31209fac4945dbcc23751d371427a18721a3a8b3761622bf5db1171262644143e6e8f1e7d26ad2f7e306cf95a627c5b28a27a5fbc37fe3f12c9ce3153b5b4265a7d0f34aa3d2a8c0c7e286c8ad6c526479487ab01b8245d9e3\"}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 122607710900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01be9bbd-0dd8-4d9a-97a4-cba2b44bde2c", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 122614450900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8165110-f092-47fa-b69f-e1f3db299fb9", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 122614846800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95adc9d3-1ca7-4e25-88ab-4b56d3b9e05e", "name": "hvigor daemon: Socket is connected. socketId=G6cRGmJeoXcgVx5JAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 125837158600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf3f917d-6a76-40ac-9969-a3aeee9af4cd", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"42809c4110d90f682938597528c2556b5103a105\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\onlyreceiver\\\\editVersion\\\\update11\\\\globalstateUse\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5140,\"state\":\"idle\",\"lastUsedTime\":1752805626314,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"0000005035465bbb6f3d5c2c33fe4747ba2a5898fffda2e8e55ad25942bc459b9580a8b060cccfa52a7bd36d99a0b7e4b75bddd3d7c99c1bde55cbdd6b43f8cbd601fcaff5420efe69552e27c20ca2364d8aa9c3b0c8eea4159258819fbe6195\"},{\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":3716,\"state\":\"stopped\",\"lastUsedTime\":1753097817639,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"},{\"keyId\":\"277ad167b90cc80ce976dbd6afbe358bd99b7f7e\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":2148,\"state\":\"stopped\",\"lastUsedTime\":1753090153399,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050838d1ad01bfd1550ead51f6ed433a227255c04f73eb6d56f22bd2763fe725e2b83fbcf4fd60a26c845a2c602755158018cba8b725381b1441e800bf943ef6f387c5623ba06c8adf19ae16119669eddff1cd39b24422ab96c15d49f8f\"},{\"keyId\":\"893216d240f25a41562b9bcbc7a74ef1fd3fa8ed\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\release\\\\0718\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45003,\"pid\":18640,\"state\":\"stopped\",\"lastUsedTime\":1753085265437,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050cac0116da7771717d59e7bc8faf998fc155484d3cd0d66298ab3323e5f33a8c26ed7d084a225d981022c8ea0220538a0a54723b401e094f07324ce4a503482a754aac107da85885404f88621f172c9c61d553975ef4a47330cc4a6cb\"},{\"keyId\":\"b9ddb8cea31f249c8b2536ebf131cc68ae6be44d\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":17592,\"state\":\"stopped\",\"lastUsedTime\":1753084933680,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005070f328dae46fd03c7719b268053842e81e407fb9e7f97683a5c3f4144b92f225f5b56019abca2a185e5cb5b31eb96548737440c854b4d374ea335b821a28fa1b5c8f9980b8a71ae27ef25131c90ce745e53355b29e37100e8ec29f49\"},{\"pid\":2544,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"47188334a2df522b9a2bcfd152364b7cbf9cf7bc\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753106925589,\"createdBy\":\"deveco\",\"sessionId\":\"00000050b3480f31209fac4945dbcc23751d371427a18721a3a8b3761622bf5db1171262644143e6e8f1e7d26ad2f7e306cf95a627c5b28a27a5fbc37fe3f12c9ce3153b5b4265a7d0f34aa3d2a8c0c7e286c8ad6c526479487ab01b8245d9e3\"},{\"pid\":2648,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"186e185421ac1ecdd6120cfb745ca07b105f6b7a\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1753106925627,\"createdBy\":\"deveco\",\"sessionId\":\"000000502bae7b53c909cd10edbacff86d04ad1586436a0ee8b16bd3af854c90069429ddef44d977cdfb4891338a398ae99a4fa59670db9da9667b0a9de6c6f9fb5d82b63a8945d775e1fb5323bae310dc63c4988d0b7b31f1a2cc107c7d012a\"}]", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 125838222000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "094a235e-18da-4415-908c-f103fb10f018", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2544,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"47188334a2df522b9a2bcfd152364b7cbf9cf7bc\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753106925589,\"createdBy\":\"deveco\",\"sessionId\":\"00000050b3480f31209fac4945dbcc23751d371427a18721a3a8b3761622bf5db1171262644143e6e8f1e7d26ad2f7e306cf95a627c5b28a27a5fbc37fe3f12c9ce3153b5b4265a7d0f34aa3d2a8c0c7e286c8ad6c526479487ab01b8245d9e3\"}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 125839097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d38555b-f9d2-4a8c-a094-83648215cb33", "name": "set active socket. socketId=G6cRGmJeoXcgVx5JAAAD", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126001717400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3759bd38-3d89-48a6-ab76-46bdd6fcd6fb", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2544,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"47188334a2df522b9a2bcfd152364b7cbf9cf7bc\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753106928820,\"createdBy\":\"deveco\",\"sessionId\":\"00000050b3480f31209fac4945dbcc23751d371427a18721a3a8b3761622bf5db1171262644143e6e8f1e7d26ad2f7e306cf95a627c5b28a27a5fbc37fe3f12c9ce3153b5b4265a7d0f34aa3d2a8c0c7e286c8ad6c526479487ab01b8245d9e3\"}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126003149500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c968c4f-bd48-484a-91ad-e37d3f1d9c29", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=ijkplayer', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126007473200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71a6370a-38f5-4de4-8bc8-5470f0c429fd", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126008311400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc1a16af-ccfc-4f64-86e4-983d879ff6e0", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2544,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"47188334a2df522b9a2bcfd152364b7cbf9cf7bc\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753106928985,\"createdBy\":\"deveco\",\"sessionId\":\"00000050b3480f31209fac4945dbcc23751d371427a18721a3a8b3761622bf5db1171262644143e6e8f1e7d26ad2f7e306cf95a627c5b28a27a5fbc37fe3f12c9ce3153b5b4265a7d0f34aa3d2a8c0c7e286c8ad6c526479487ab01b8245d9e3\"}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126009421000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebaa8436-bccf-4eca-9c2b-39334dd67422", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126020395900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59703727-791a-46ef-b7bb-7802571db29a", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126024373200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2dfcb6-ff01-4f0c-ac32-4ddadc2c2733", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126032690900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91d47010-ad19-4113-971d-e94b965cdabc", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126046009800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6966d99f-cfd9-4bfa-95fe-9831f8589061", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126046079900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a2a4d82-289b-4580-bf99-72e34e3949f0", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126058169000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1568a98d-fe54-4725-ac99-65b18913bca7", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126061393500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdc6af52-c6e7-4695-a0fc-9f841dd1a777", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126082452900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31006448-ad4f-4982-a93a-0485166e4a27", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126082528300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "639c0db7-71bc-422b-9987-fe26c6321933", "name": "Module entry Collected Dependency: D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126107210900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b04455-b7b6-48bc-820b-1e7e9e611b78", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126107247300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0629d87b-0bf4-490f-aa52-3e45718aa853", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126111231800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a88b522d-2d2b-4471-a576-4215632fabe3", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126111317300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0799cda0-27b9-4e0e-a2ee-000ff9577ca9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126111398900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7daa7cea-ab00-45f7-8311-d9b0305ed564", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126111445100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4da3e25-26f5-47b2-849f-2728c0b0b4ad", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126111457400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f77e8d0f-eae0-48d6-87b4-7e6e536ea7f3", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126111464300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f0c715e-8141-47fe-b609-723ea81474dd", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126111494700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "448521d6-8603-49be-97fe-87a7f1cbee24", "name": "require SDK: toolchains,ArkTS; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126113135000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6ae6ce7-8849-4d91-899e-8d167bc14fea", "name": "Module entry task initialization takes 4 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126125213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20d8a09e-01e8-4641-82f3-63c4bcbfc724", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126125268500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea7c4c59-dfbc-4b3c-8f01-380922de1654", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126129202000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4aab28f-9597-4548-9615-a30e3831727d", "name": "hvigorfile, require result:  { harTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126159020300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f4a7287-cb37-465f-90ac-2fee79a5a706", "name": "hvigorfile, binding system plugins { harTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126159065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a132b4f4-e2f3-49f2-8f2e-7f1357e6ef8d", "name": "<PERSON><PERSON><PERSON> ijkplayer Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126174237600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dec8212-d092-49f4-84f2-b1bce927695a", "name": "<PERSON><PERSON><PERSON> ijkplayer's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126174296000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "469e68b3-a7b6-4ae2-94a9-390569dbef83", "name": "Start initialize module-target build option map, moduleName=ijkplayer, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126175743200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93b9e7a-7800-48ec-99fa-9c0e97b017c2", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126175785000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69594396-dd87-4eba-8676-f3e6c685f8db", "name": "Module 'ijkplayer' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126176447500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4bdb62-9e9d-4962-914c-4d91edb7ad12", "name": "End initialize module-target build option map, moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126176463700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c45b68b-caf9-45c5-9888-cd0d3c8315ad", "name": "Module 'ijkplayer' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126176494100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eed8ea77-3b71-47c8-82b3-d6b7b90d7b8a", "name": "require SDK: toolchains,ArkTS,native; moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126178091000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3249707-5430-4a7e-8e7e-4edf4ebbd669", "name": "<PERSON><PERSON><PERSON> ijkplayer task initialization takes 10 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126189831200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d0ebec9-a876-48ff-9c4e-0b5514c0429c", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126190456300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e873aa4-1625-491d-8392-a76a4cb5efb9", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126190647800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c7bfe6-7eac-4c17-96aa-66e58f4c9582", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126190680200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90c29204-af01-4a1a-a052-75fcbd77127f", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126190729500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "407cad45-8944-463f-9ca2-88d118b13827", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126190742700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47680077-4650-44cf-abfc-e94ee8415d44", "name": "<PERSON><PERSON><PERSON>_ijkplayer-2.0.3 Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126191545800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b24fd235-8cd9-4f76-a350-96dd77423b36", "name": "<PERSON><PERSON><PERSON> ohos_ijkplayer-2.0.3's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126191570700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b0381e-facb-47ea-994e-2860d76270ca", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126193714100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f4616d6-fda5-4cb3-88c4-91c2125e5eb0", "name": "Sdk init in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126211619800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b2c496-0959-4dd6-8f51-e40e2d9d82fa", "name": "project has submodules:entry,ijkplayer", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126268754600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ce1251a-d5d0-4c3f-bbcc-c70deac7024d", "name": "module:ijkplayer no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126272566000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3109cb5b-dbaa-4f9f-947f-486863abce85", "name": "Project task initialization takes 65 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126275810800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342f628e-be74-4e8b-a211-0f8be0bfa33d", "name": "Sdk init in 11 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126286494700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f8c3465-1393-470c-945a-875d01157114", "name": "Sdk init in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126293759200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3a9c722-cd47-4505-baeb-5e7e35629404", "name": "Configuration phase cost:271 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126294696200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19f26b8d-5341-4d0e-9478-606a44a837e5", "name": "Configuration task cost before running: 283 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126296358000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21f59f0-4526-4240-9cd5-90b94a35f90b", "name": "ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126309594300, "endTime": 126326726800}, "additional": {"children": [], "state": "success", "detailId": "96263bcb-cdc4-42df-a573-8c4e6623a095", "logId": "564df8cd-cf20-49d3-82c0-5486fb47b5b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "96263bcb-cdc4-42df-a573-8c4e6623a095", "name": "create ijkplayer:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126302431200}, "additional": {"logType": "detail", "children": [], "durationId": "b21f59f0-4526-4240-9cd5-90b94a35f90b"}}, {"head": {"id": "95a9b83a-73c7-4402-bb57-4d8349f3cf1d", "name": "ijkplayer : default@PreBuild start {\n  rss: 196878336,\n  heapTotal: 127025152,\n  heapUsed: 102065128,\n  external: 1228040,\n  arrayBuffers: 262684\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126309510700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8c42b7e-d19c-4b50-ab84-8fc42dfa5678", "name": "Executing task :ijkplayer:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126309642000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c2fbc00-a399-4905-a71c-ace16e79a2c7", "name": "Incremental task ijkplayer:default@PreBuild pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126326406300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01185700-398c-4e8d-be76-6862ac61511c", "name": "ijkplayer : default@PreBuild end {\n  rss: 197701632,\n  heapTotal: 127287296,\n  heapUsed: 102445784,\n  external: 1228040,\n  arrayBuffers: 262684\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126326568600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "564df8cd-cf20-49d3-82c0-5486fb47b5b1", "name": "UP-TO-DATE :ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126309594300, "endTime": 126326726800}, "additional": {"logType": "info", "children": [], "durationId": "b21f59f0-4526-4240-9cd5-90b94a35f90b"}}, {"head": {"id": "f8978ce3-5a93-44e9-bb51-f9cc8c378548", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126331496300, "endTime": 130796441300}, "additional": {"children": ["fa7516b5-3ef6-43c0-84b9-5acc2714d717", "a962934b-20a1-45ba-a818-c69178c6a565"], "state": "success", "detailId": "376e6cf3-4ecd-4809-9443-68cacbef3d53", "logId": "49b5b296-829e-49dc-9d6a-a46989bafffb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "376e6cf3-4ecd-4809-9443-68cacbef3d53", "name": "create ijkplayer:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126330245700}, "additional": {"logType": "detail", "children": [], "durationId": "f8978ce3-5a93-44e9-bb51-f9cc8c378548"}}, {"head": {"id": "10623d70-d14a-4aa1-91b9-747411cd3ec8", "name": "ijkplayer : default@BuildNativeWithCmake start {\n  rss: 198004736,\n  heapTotal: 127287296,\n  heapUsed: 102676056,\n  external: 1228040,\n  arrayBuffers: 262684\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126331435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f151c8-93f6-4915-a1d7-c963e5fa605f", "name": "Executing task :ijkplayer:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126331532700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e41575-98d8-40bc-bbb0-a8e85d5ddf60", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126342042400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52a65bb3-b0f1-459e-8c14-7262f6b0b32d", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126344812400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa7516b5-3ef6-43c0-84b9-5acc2714d717", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2544, "tid": "Worker0", "startTime": 126586233900, "endTime": 130337725200}, "additional": {"children": [], "state": "success", "parent": "f8978ce3-5a93-44e9-bb51-f9cc8c378548", "logId": "423d2d70-6beb-4a67-8076-4597abce7a4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "df1c5d79-4b83-44b7-a9d7-0cb35a8796c3", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126346574900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f96929-bf87-4ce2-ba9c-dee3ecdb0591", "name": "default@BuildNativeWithCmake work[0] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126346686300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d19402-95f5-45ad-b4a4-2afed022632f", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126349186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9837bedc-75fc-44e0-87d9-0361e898f51c", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126352482700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a962934b-20a1-45ba-a818-c69178c6a565", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2544, "tid": "Worker1", "startTime": 127336578300, "endTime": 130793146700}, "additional": {"children": [], "state": "success", "parent": "f8978ce3-5a93-44e9-bb51-f9cc8c378548", "logId": "11f4f02f-abaa-42ab-944c-69f618775806"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "7388fd52-2a39-42e9-82e3-01c10dfccfcd", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126353262500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd73137-5b05-46e5-a6a1-504bc93d1e2b", "name": "default@BuildNativeWithCmake work[1] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126353313200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac4b36bf-d266-4ba0-a2fd-a1343db82f93", "name": "ijkplayer : default@BuildNativeWithCmake end {\n  rss: 198193152,\n  heapTotal: 127287296,\n  heapUsed: 103303760,\n  external: 1228040,\n  arrayBuffers: 262684\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126353440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daae2367-45de-43cd-a271-a6e38c836156", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126586346900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f565f05-2098-4828-9a8e-f0e07fa59c19", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 127336379800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d73d9e0-afd8-4ccf-b31c-cdff5264ecb4", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 127336684700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e3968c1-fdaf-4a73-8d8e-9356a91f2d3c", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130337928900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "423d2d70-6beb-4a67-8076-4597abce7a4e", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2544, "tid": "Worker0", "startTime": 126586233900, "endTime": 130337725200}, "additional": {"logType": "info", "children": [], "durationId": "fa7516b5-3ef6-43c0-84b9-5acc2714d717", "parent": "49b5b296-829e-49dc-9d6a-a46989bafffb"}}, {"head": {"id": "8800b8e6-804f-49a8-aff5-01064558957a", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130793526500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11f4f02f-abaa-42ab-944c-69f618775806", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2544, "tid": "Worker1", "startTime": 127336578300, "endTime": 130793146700}, "additional": {"logType": "info", "children": [], "durationId": "a962934b-20a1-45ba-a818-c69178c6a565", "parent": "49b5b296-829e-49dc-9d6a-a46989bafffb"}}, {"head": {"id": "49b5b296-829e-49dc-9d6a-a46989bafffb", "name": "Finished :ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126331496300, "endTime": 130796441300}, "additional": {"logType": "info", "children": ["423d2d70-6beb-4a67-8076-4597abce7a4e", "11f4f02f-abaa-42ab-944c-69f618775806"], "durationId": "f8978ce3-5a93-44e9-bb51-f9cc8c378548"}}, {"head": {"id": "32624d5f-a70c-4a59-8186-8ff2ddf28618", "name": "ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130806662700, "endTime": 130806971500}, "additional": {"children": [], "state": "success", "detailId": "f50b3b47-a188-4d74-86c8-09525ca6b176", "logId": "ddcbf865-43ff-482a-b246-5c179632c921"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f50b3b47-a188-4d74-86c8-09525ca6b176", "name": "create ijkplayer:compileNative task", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130806396100}, "additional": {"logType": "detail", "children": [], "durationId": "32624d5f-a70c-4a59-8186-8ff2ddf28618"}}, {"head": {"id": "803d22ae-9b48-4c82-95b1-1aa3fc3ff735", "name": "ijkplayer : compileNative start {\n  rss: 294436864,\n  heapTotal: 127287296,\n  heapUsed: 103494216,\n  external: 1228040,\n  arrayBuffers: 262684\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130806608400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "505354ce-059e-4067-b00e-2ccb308adc75", "name": "Executing task :ijkplayer:compileNative", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130806695000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "153be62e-fc59-46b8-970d-5b4f64f648ec", "name": "ijkplayer : compileNative end {\n  rss: 294436864,\n  heapTotal: 127287296,\n  heapUsed: 103503776,\n  external: 1228040,\n  arrayBuffers: 262684\n}", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130806942400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddcbf865-43ff-482a-b246-5c179632c921", "name": "Finished :ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130806662700, "endTime": 130806971500}, "additional": {"logType": "info", "children": [], "durationId": "32624d5f-a70c-4a59-8186-8ff2ddf28618"}}, {"head": {"id": "8cb888bb-e495-4e3e-9256-7459fdd7ba9a", "name": "BUILD SUCCESSFUL in 4 s 794 ms ", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130807982800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "fb9c4175-1b20-48f9-9c2c-0c01309976b7", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 126013852800, "endTime": 130808944700}, "additional": {"time": {"year": 2025, "month": 7, "day": 21, "hour": 22, "minute": 8}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "7b11343d-eaab-4422-aa27-6bae81750dc1", "name": "There is no need to refresh cache, since the incremental task ijkplayer:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2544, "tid": "Main Thread", "startTime": 130811567500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}