# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>
pkgname=libuuid
pkgver=1.0.3
pkgrel=0
pkgdesc="The libuuid library is used to generate unique identifiers for objects that may be accessible beyond the local system. "
url="https://sourceforge.net/projects/libuuid/"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD")
depends=()
makedepends=()

source="https://sourceforge.net/projects/$pkgname/files/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

source envset.sh
host=

prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
}

build() {
    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host > `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    unset host
}

check() {
    cd $builddir/$ARCH-build
    make check >> `pwd`/build.log 2>&1
    cd $OLDPWD    
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi

    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # ./test_uuid
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
