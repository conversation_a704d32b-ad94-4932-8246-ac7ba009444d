# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
OLD_LD_LIBRARY_PATH=$LD_LIBRARY_PATH

checkprepare() {
    export LD_LIBRARY_PATH=`pwd`/$builddir-$ARCH-build/cmake:$LD_LIBRARY_PATH
}

openharmonycheck() {
    res=0
    cd $builddir-$ARCH-build/cmake
    ./lite-arena-test > ${logfile} 2>&1
    res=$?
    if [ $res -eq 0 ]
    then
        ./lite-test >> ${logfile} 2>&1
        res=$?
    fi
    if [ $res -eq 0 ]
    then
        ./tests >> ${logfile} 2>&1
        res=$?
    fi

    export LD_LIBRARY_PATH=$OLD_LD_LIBRARY_PATH
    unset OLD_LD_LIBRARY_PATH
    cd $OLDPWD
    return $res
}
