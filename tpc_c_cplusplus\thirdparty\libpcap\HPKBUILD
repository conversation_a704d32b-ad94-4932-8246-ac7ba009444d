# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=libpcap 
pkgver=libpcap-1.10.3 
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD")
depends=()
makedepends=("flex" "bison")
install= 
source="https://github.com/the-tcpdump-group/$pkgname/archive/refs/tags/$pkgver.tar.gz"
builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz
buildtools="configure"

source envset.sh
host=
prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
          setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
          setarm64ENV
          host=aarch64-linux
    fi
    cd $builddir
    cd $OLDPWD
}

build() {

    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host > `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
          unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
          unsetarm64ENV
    fi
    unset host
}

check() {
    cd $builddir/$ARCH-build
    make testprogs >> `pwd`/build.log 2>&1
    cd $OLDPWD
    echo "The test must be on an OpenHarmony device!"
    # 保证设备联网
    # 在构建目录的testprogs目录下，执行./capturetest 进行抓包测试即可
}

cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/packagename
}
