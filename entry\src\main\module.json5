{"module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["default", "tablet"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "metadata": [{"name": "ArkTSPartialUpdate", "value": "true"}], "requestPermissions": [{"name": "ohos.permission.INTERNET", "reason": "$string:NET_REQUEST_PERMISSION"}], "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ts", "description": "$string:EntryAbility_desc", "icon": "$media:icon", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:icon", "startWindowBackground": "$color:start_window_background", "visible": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}]}]}}