# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
pkgname=openal-soft
pkgver=1.23.1
pkgrel=0
pkgdesc="OpenAL Soft is an LGPL-licensed, cross-platform, software implementation of the OpenAL 3D audio API."
url="https://openal-soft.org"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPLv2")
depends=("pulseaudio" "libsndfile")
makedepends=()

source="https://github.com/kcat/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
patchflag=true

builddir=${pkgname}-${pkgver}
packagename=$builddir.tar.gz

prepare() {
    if $patchflag
    then
        cd $builddir
        # 添加控制动态库的符号可见性开关
        patch -p1 < `pwd`/../openal-soft_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DSNDFILE_FOUND=TRUE -DALSOFT_INSTALL_EXAMPLES=ON \
        -DALSOFT_REQUIRE_PULSEAUDIO=ON -DVISIBILITY_PRESET_HIDDEN=OFF -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make VERBOSE=1 -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    if [ $ARCH == "armeabi-v7a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/arm-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    elif [ $ARCH == "arm64-v8a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/aarch64-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    else
        echo "${ARCH} not support"
        return -1
    fi
    echo "The test must be on an OpenHarmony device!"
    # real test cmd
    # ./openal-info
    # ./alrecord -c 2 -b 16 -r 44100 -t 5 -o record.wav
    # ./alplay record.wav
    # ./altonegen
    # ./alstream /system/etc/graphic/bootsound.wav
    # ./alhrtf /system/etc/graphic/bootsound.wav
    # ./allatency /system/etc/graphic/bootsound.wav
    # ./alreverb /system/etc/graphic/bootsound.wav
    # ./almultireverb /system/etc/graphic/bootsound.wav
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}

