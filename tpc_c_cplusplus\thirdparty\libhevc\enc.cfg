######################################################################################
# ITTIAM HEVC ENCODER CONFIGURATION FILE (c) ITTIAM SYSTEMS                          #
# The following rules / restrictions apply to this config file                       #
# All the parameters are recognized using the keywords at the start of each line     #
# Value which is present after the = sign is considered as the valid value           #
# Every configuration parameter must be assigned a valid value.                      #
# The encoder does not assign default values for any missing parameter               #
# The order of parameters can be changed                                             #
# Each entry must be present only once                                               #
######################################################################################

######################################################################################
# Component Name : HEVC Encoder on x86                                               #
######################################################################################

######################################################################################
# File I/O Parameters                                                                #
######################################################################################

--input                     720p_basketballdrive.yuv     /* Input yuv file {mandatory} */
--output                    out.265                      /* Output bitstream file (mandatory) */
--num_frames_to_encode      -1
--log_dump_level            1                            /* 0- [No log/prints] 1- [Bits Generated, POC, Qp, Pic-type] 2- [1 + PSNR + Seq Summary] 3- [2 + SSIM + Frame Summary] */

######################################################################################
# Source Parameters                                                                  #
######################################################################################

--src_width              1280   /* Input Source Width {mandatory}[320:4096] */
--src_height             720    /* Input Source Height {mandatory}[128:2304] */
--src_frame_rate_num     30000  /* Frame rate numerator {30000}[7500:120000] */
--src_frame_rate_denom   1000   /* Frame rate denominator {1000}[1000,1001] */
--input_chroma_format    1      /* 1- YUV_420P,11- YUV_420SP; {1, 11} */

######################################################################################
# Target Parameters  (for all the layers of multi-resolution encoding)               #
######################################################################################

--codec_level                   156        /* Coded Level multiplied by 30 */
--tgt_bitrate                   4000000    /* Target bitrates in bps{5000000} */
--frame_qp                      32         /* Initial QP values {32} */

######################################################################################
#  GOP structure Parameters                                                          #
######################################################################################

--max_closed_gop_period            0    /* Max IDR Pic distance- Closed GOP {0} */
--min_closed_gop_period            0    /* Min IDR Pic distance- Closed GOP {0} */
--max_cra_open_gop_period          60   /* Max CRA Pic distance- Open GOP {60} */
--max_i_open_gop_period            60   /* Max I (non CRA, non IDR) Pic distance {0} */
--max_temporal_layers              0    /* B pyramid layers {3}[0:3] */

######################################################################################
#  Coding tools Parameters                                                           #
######################################################################################

--quality_preset                    5    /* 0->P0(Best Quality), 2->P2, 3->P3, 4->P4, 5->P5, 6->P6(Best Speed) {5} */
--deblocking_type                   0    /* Debocking 0- enabled, 1- disabled {0} */
--use_default_sc_mtx                0    /* 0- disabled, 1- enabled {0} */
--enable_entropy_sync               0    /* Entropy sync 1- enabled, 0- disabled {0} */
--max_tr_tree_depth_I               1    /* Max transform tree depth for intra {3}[1,2,3] */
--max_tr_tree_depth_nI              3    /* Max transform tree depth for inter {3}[1,2,3,4] */
--max_search_range_horz           512    /* Horizontal search range {512}[64:512] */
--max_search_range_vert           256    /* Vertical search range {256}[32:256] */
--archType                          0    /* 0 => Automatic, 4 => No Neon */

######################################################################################
# Multi Core parameters                                                              #
######################################################################################

--num_cores                         4    /* [1:4] */

######################################################################################
#  Rate Control parameters                                                           #
######################################################################################

--rate_control_mode      2        /* 2- VBR 3- CQP, 5- CBR {2} */
--cu_level_rc            1        /* CU QP Modulation  0-disable, 1-spatial qp modulation {1} */
--max_frame_qp           51       /* Max frame Qp for I frame {51}[51]*/
--min_frame_qp           1        /* Min frame Qp for I frame. {1}[1] */

######################################################################################
# Look Ahead Processing Parameters                                                   #
######################################################################################

--rc_look_ahead_pics           0 /* RC look ahead window {0}[0:120] */

######################################################################################
# Output stream Parameters                                                           #
######################################################################################

--codec_type                0 /* 0- HEVC {0} */
--codec_profile             1 /* 1- Main */
--codec_tier                0 /* 0- Main 1- High {1} */
--sps_at_cdr_enable         0 /* 1- enable, 0- disable {1} */

######################################################################################
# SEI and VUI parameters                                                             #
######################################################################################

--sei_enable_flags                        0                    /* 1- enable, 0- disable {0} */
--sei_buffer_period_flags                 0                    /* 1- enable, 0- disable {0} */
--sei_pic_timing_flags                    0                    /* 1- enable, 0- disable {0} */
--sei_recovery_point_flags                0                    /* 1- enable, 0- disable {0} */
--sei_hash_flags                          0                    /* 3- Checksum, 2- CRC,  0- disable {0} */
--sei_mastering_disp_colour_vol_flags     0                    /* 1: enable, 0: disable {0} */
--display_primaries_x                     0,0,0                /* X-Primaries: comma separated R,G,B values {}[0:50000] */
--display_primaries_y                     0,0,0                /* Y-Primaries: comma separated R,G,B values {}[0:50000] */
--white_point_x                           0                    /* X White point value {}[0:50000] */
--white_point_y                           0                    /* Y White point value {}[0:50000] */
--max_display_mastering_luminance         1                    /* Max mastering Luminance. In units  of  0.0001  Candelas/sqmtr {} */
--min_display_mastering_luminance         0                    /* Min mastering Luminance. In units  of  0.0001  Candelas/sqmtr {} */
--sei_content_light_level_info            0                    /* 0-disable,1-enable */
--max_content_light_level                 20                   /* 16bit unsigned number indicating max pixel intensity*/
--max_frame_average_light_level           10                   /* 16bit unsigned number indicating max avg pixel intensity*/

######################################################################################
# VUI Parameters                                                                     #
######################################################################################

--vui_enable                                       0 /* 1- enable, 0- disable {0} */
--aspect_ratio_info_present_flag                   0 /* Aspect Ratio 1-enable 0-diable {0} */
--aspect_ratio_idc                                 3 /* Aspect Ration IDC {255}[0:255] */
--sar_width                                        4 /* SAR Width {4}[0:65535] */
--sar_height                                       3 /* SAR Height {3}[0:65535]*/
--overscan_info_present_flag                       0 /* Overscan Info. 1-enable 0-disable {0} */
--overscan_appropriate_flag                        1 /* Overscan Appropriate 1-enable 0-disable {0} */
--video_signal_type_present_flag                   0 /* Video Signal Type Present. 1-enable 0-diable {1} */
--video_format                                     5 /* Video Format {5}[0:5] */
--video_full_range_flag                            0 /* Video Full Range. 1-enable 0-diable {1} */
--colour_description_present_flag                  0 /* Colour description.1-enable 0-diable {0} */
--colour_primaries                                 2 /* Colour Primaries {2}[0:255] */
--transfer_characteristics                         2 /* Transfer Characteristic {2}[0:255] */
--matrix_coefficients                              2 /* Matrix Coefficients {2}[0:255] */
--chroma_loc_info_present_flag                     0 /* Presence of chroma_sample_loc_type_top_field and chroma_sample_loc_type_bottom_field.1-enable 0-diable {0} */
--chroma_sample_loc_type_top_field                 0 /* Location of Chroma samples for Top field.{0}[0,1] */
--chroma_sample_loc_type_bottom_field              0 /* Location of Chroma samples for Bottom field..{0}[0,1] */
--timing_info_present_flag                         1 /* Timing info.1-enable 0-diable {0} */
--vui_hrd_parameters_present_flag                  1 /* HRD parameters.1-enable 0-diable {0} */
--nal_hrd_parameters_present_flag                  1 /* NAL HRD parameters.1-enable 0-diable {0} */
