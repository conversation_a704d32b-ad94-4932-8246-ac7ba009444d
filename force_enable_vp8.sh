#!/bin/bash

# 强制启用 VP8 支持的脚本
# 在 tpc_c_cplusplus 下载后立即运行

echo "=== 强制启用 VP8 支持脚本 ==="

FFMPEG_HPKBUILD="tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD"

if [ ! -f "$FFMPEG_HPKBUILD" ]; then
    echo "❌ FFmpeg 配置文件不存在: $FFMPEG_HPKBUILD"
    exit 1
fi

echo "✅ 找到 FFmpeg 配置文件"

# 备份原文件
cp "$FFMPEG_HPKBUILD" "$FFMPEG_HPKBUILD.backup"
echo "✅ 已备份原配置文件"

# 显示原始配置
echo "=== 原始配置 ==="
echo "configure 行:"
grep -n "configure" "$FFMPEG_HPKBUILD"
echo ""

# 方法1: 在所有 configure 行后添加 VP8 支持
echo "=== 方法1: 在 configure 行添加 VP8 支持 ==="
sed -i 's|PKG_CONFIG_LIBDIR="${pkgconfigpath}" ./configure|PKG_CONFIG_LIBDIR="${pkgconfigpath}" ./configure --enable-decoder=vp8 --enable-parser=vp8 --enable-decoder=vp9 --enable-parser=vp9|g' "$FFMPEG_HPKBUILD"

# 方法2: 如果上面没成功，在 --sysroot 前添加
echo "=== 方法2: 在 sysroot 前添加 VP8 支持 ==="
sed -i 's|--sysroot=${OHOS_SDK}/native/sysroot|--enable-decoder=vp8 --enable-parser=vp8 --enable-decoder=vp9 --enable-parser=vp9 --sysroot=${OHOS_SDK}/native/sysroot|g' "$FFMPEG_HPKBUILD"

# 方法3: 在 > $buildlog 前添加
echo "=== 方法3: 在 buildlog 前添加 VP8 支持 ==="
sed -i 's| > \$buildlog| --enable-decoder=vp8 --enable-parser=vp8 --enable-decoder=vp9 --enable-parser=vp9 > \$buildlog|g' "$FFMPEG_HPKBUILD"

# 显示修改后的配置
echo "=== 修改后的配置 ==="
echo "configure 行:"
grep -n "configure" "$FFMPEG_HPKBUILD"
echo ""

# 验证是否包含 VP8
if grep -q "enable-decoder=vp8" "$FFMPEG_HPKBUILD"; then
    echo "✅ VP8 解码器已添加到配置中"
else
    echo "❌ VP8 解码器添加失败"
    
    # 最后的手段：直接替换整个 configure 行
    echo "=== 最后手段：直接替换 configure 行 ==="
    sed -i 's|PKG_CONFIG_LIBDIR="${pkgconfigpath}" ./configure.*|PKG_CONFIG_LIBDIR="${pkgconfigpath}" ./configure "$@" --enable-neon --enable-asm --enable-network --enable-cross-compile --disable-x86asm --enable-openssl --enable-protocols --enable-libopenh264 --enable-decoder=vp8 --enable-parser=vp8 --enable-decoder=vp9 --enable-parser=vp9 --disable-programs --enable-static --disable-shared --disable-doc --disable-htmlpages --target-os=linux --arch=$arch --cc=${CC} --ld=${CC} --strip=${STRIP} --sysroot=${OHOS_SDK}/native/sysroot > $buildlog 2>\&1|g' "$FFMPEG_HPKBUILD"
fi

# 最终验证
echo "=== 最终验证 ==="
if grep -q "enable-decoder=vp8" "$FFMPEG_HPKBUILD"; then
    echo "✅ VP8 支持已成功添加"
    echo "包含的 VP8 相关选项:"
    grep -o "enable-[^[:space:]]*vp[89][^[:space:]]*" "$FFMPEG_HPKBUILD"
else
    echo "❌ VP8 支持添加失败"
fi

echo "=== 完整的修改后文件内容 ==="
cat "$FFMPEG_HPKBUILD"
