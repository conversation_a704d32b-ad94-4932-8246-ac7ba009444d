# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, wen fan <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname="libmobi"
pkgver="v0.11"
pkgrel="0"
pkgdesc="C library for handling Mobipocket/Kindle (MOBI) ebook format documents"
url="https://github.com/bfabis<PERSON><PERSON>/${pkgname}"
archs=("armeabi-v7a" "arm64-v8a")
license=("GNU Lesser General Public License v3.0")
depends=()
makedepends=()

source="https://github.com/bfabiszewski/${pkgname}/archive/refs/tags/${pkgver}.tar.gz"

autounpack=true
downloadpackage=true
builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DUSE_LIBXML2=OFF -B$ARCH-build -S./ -L > $buildlog 2>&1
    ${MAKE} -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    install_dir=${LYCIUM_ROOT}/usr/${pkgname}/${ARCH}
    mkdir -p ${install_dir}/lib
    cd $builddir/$ARCH-build
    cp -f src/libmobi.* ${install_dir}/lib/
	mkdir -p ${install_dir}/include
    cp -f ../src/*.h ${install_dir}/include
    cd ${OLDPWD}
    return 0
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

cleanbuild(){
    rm -rf ${PWD}/$builddir
}
