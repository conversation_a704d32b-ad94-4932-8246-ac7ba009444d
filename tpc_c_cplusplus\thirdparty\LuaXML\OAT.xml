<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2021 Huawei Device Co., Ltd.

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.

    This is the configuration file template for OpenHarmony OSS Audit Tool, please copy it to your project root dir and modify it refer to OpenHarmony/tools_oat/README.

-->

<configuration>
    <oatconfig>
        <licensefile></licensefile>
        <policylist>
            <policy name="projectPolicy" desc="">
                <!--policyitem type="compatibility" name="GPL-2.0+" path="abc/.*" desc="Process that runs independently, invoked by the X process."/-->
                <!--policyitem type="license" name="LGPL" path="abc/.*" desc="Dynamically linked by module X"/-->
                <!--policyitem type="copyright" name="xxx" path="abc/.*" rule="may" group="defaultGroup" filefilter="copyrightPolicyFilter" desc="Developed by X Company"/-->
            </policy>
        </policylist>
        <filefilterlist>
            <filefilter name="defaultFilter" desc="Files not to check">
		    <filteritem type="filename" name="*.iml|*.json|*.txt|*.png|*.jpg|*.zip|*.tar.gz" desc="desc files"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6/.*" desc="三方库代码"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6-arm64-v8a-build/.*" desc="三方库代码"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6-armeabi-v7a-build/.*" desc="三方库代码"/>
            </filefilter>
            <filefilter name="defaultPolicyFilter" desc="Filters for compatibility，license header policies">
                <filteritem type="fileitem" name="HPKBUILD" desc="自己编写的shell脚本,不涉及版权问题"/>
                <filteritem type="fileitem" name="HPKCHECK" desc="自己编写的shell脚本,不涉及版权问题"/>
                <filteritem type="fileitem" name="SHA512SUM" desc="自己编写的shell脚本,不涉及版权问题"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6/.*" desc="三方库代码"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6-arm64-v8a-build/.*" desc="三方库代码"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6-armeabi-v7a-build/.*" desc="三方库代码"/>
            </filefilter>
            <filefilter name="copyrightPolicyFilter" desc="Filters for copyright header policies">
                <filteritem type="fileitem" name="HPKBUILD" desc="自己编写的shell脚本,不涉及版权问题"/>
                <filteritem type="fileitem" name="HPKCHECK" desc="自己编写的shell脚本,不涉及版权问题"/>
                <filteritem type="fileitem" name="SHA512SUM" desc="自己编写的shell脚本,不涉及版权问题"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6/.*" desc="三方库代码"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6-arm64-v8a-build/.*" desc="三方库代码"/>
                <filteritem type="filepath" name="LuaXML-7cd4a7ab5db85222edb4c955d53e5674afd170b6-armeabi-v7a-build/.*" desc="三方库代码"/>
            </filefilter>
            <filefilter name="licenseFileNamePolicyFilter" desc="Filters for LICENSE file policies">
                <!--filteritem type="filename" name="*.uvwxyz" desc="Describe the reason for filtering scan results"/-->
                <!--filteritem type="filepath" name="abcdefg/.*.uvwxyz" desc="Describe the reason for filtering scan results"/-->
                <!--filteritem type="filepath" name="projectroot/[a-zA-Z0-9]{20,}.sh" desc="Temp files"/-->
            </filefilter>
            <filefilter name="readmeFileNamePolicyFilter" desc="Filters for README file policies">
                <!--filteritem type="filename" name="*.uvwxyz" desc="Describe the reason for filtering scan results"/-->
                <!--filteritem type="filepath" name="abcdefg/.*.uvwxyz" desc="Describe the reason for filtering scan results"/-->
                <!--filteritem type="filepath" name="projectroot/[a-zA-Z0-9]{20,}.sh" desc="Temp files"/-->
            </filefilter>
            <filefilter name="readmeOpenSourcefileNamePolicyFilter" desc="Filters for README.OpenSource file policies">
                <!--filteritem type="filename" name="*.uvwxyz" desc="Describe the reason for filtering scan results"/-->
                <!--filteritem type="filepath" name="abcdefg/.*.uvwxyz" desc="Describe the reason for filtering scan results"/-->
                <!--filteritem type="filepath" name="projectroot/[a-zA-Z0-9]{20,}.sh" desc="Temp files"/-->
            </filefilter>
            <filefilter name="binaryFileTypePolicyFilter" desc="Filters for binary file policies">
                <!--filteritem type="filename" name="*.uvwxyz" desc="Describe the reason for filtering scan results"/-->
                <!--filteritem type="filepath" name="abcdefg/.*.uvwxyz" desc="Describe the reason for filtering scan results"/-->
                <!--filteritem type="filepath" name="projectroot/[a-zA-Z0-9]{20,}.sh" desc="Temp files"/-->
            </filefilter>

        </filefilterlist>
        <licensematcherlist>
            <!--licensematcher name="uvwxyz License" desc="If the scanning result is InvalidLicense, you can define matching rules here. Note that quotation marks must be escaped.">
                <licensetext name="
                    uvwxyz license textA xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
				 " desc=""/>
                <licensetext name="
                    uvwxyz license textB xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
				 " desc=""/>
            </licensematcher-->
        </licensematcherlist>
    </oatconfig>
</configuration>
