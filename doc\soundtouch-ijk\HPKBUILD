# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=soundtouch
pkgver=ijk-r0.1.2-dev
pkgrel=0
pkgdesc="SoundTouch is an open-source audio processing library that allows changing the sound tempo, pitch and playback rate parameters independently from each other"
url="https://github.com/bilibili/soundtouch/branches"
archs=("armeabi-v7a" "arm64-v8a" "x86_64")
license=("LGPL-2.1")
depends=()
makedepends=()
install=
source="https://codeload.github.com/bilibili/$pkgname/zip/refs/heads/$pkgver"
autounpack=ture
downloadpackage=ture
builddir=$pkgname
packagename=$builddir-$pkgver.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
    cp -rf ./$builddir-$pkgver/* ./$builddir/
    cp soundtouch_config.h ./$builddir/include/
    cp CMakeLists.txt $builddir/
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L
    ${MAKE} -C $ARCH-build
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir	
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib
    cp -rf *.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp -rf include/* $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp -rf $ARCH-build/*.a $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "Test MUST on OpenHarmony device!"
}

cleanbuild(){
    rm -rf ${PWD}/$builddir ${PWD}/$builddir-$pkgver ${PWD}/$pkgname
}
