# Contributor: x<PERSON><PERSON> <<EMAIL>>
# Maintainer: xuz<PERSON> <<EMAIL>>
pkgname=libsndfile
pkgver=1.2.0
pkgrel=0
pkgdesc="libsndfile is a C library for reading and writing files containing sampled audio data."
url="https://github.com/libsndfile/libsndfile"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPLv2.1") 
depends=()
makedepends=()

# 官方下载地址https://github.com/libsndfile/$pkgname/archive/refs/tags/$pkgver.tar.gz受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"
autounpack=true
downloadpackage=true
buildtools="cmake"
builddir=$pkgname-${pkgver}
packagename=$builddir.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH  -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make VERBOSE=1 -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # TODO
}

cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}

