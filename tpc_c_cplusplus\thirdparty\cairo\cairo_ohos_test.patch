diff -Nura cairo-1.17.8/test/meson.build cairo-patch/test/meson.build
--- cairo-1.17.8/test/meson.build	2023-02-01 23:37:29.000000000 -0800
+++ cairo-patch/test/meson.build	2023-11-14 01:28:03.667852263 -0800
@@ -1,7 +1,7 @@
 test_sources = [
-  'a1-bug.c',
+  #'a1-bug.c',
   'a1-clip.c',
-  'a1-fill.c',
+  #'a1-fill.c',
   'a1-image-sample.c',
   'a1-mask.c',
   'a1-mask-sample.c',
@@ -10,427 +10,427 @@
   'a1-rasterisation.c',
   'a8-clear.c',
   'a8-mask.c',
-  'aliasing.c',
+  #'aliasing.c',
   'alpha-similar.c',
-  'arc-direction.c',
+  #'arc-direction.c',
   'arc-infinite-loop.c',
-  'arc-looping-dash.c',
+  #'arc-looping-dash.c',
   'api-special-cases.c',
-  'big-line.c',
-  'big-empty-box.c',
-  'big-empty-triangle.c',
-  'big-little-box.c',
+  #'big-line.c',
+  #'big-empty-box.c',
+  #'big-empty-triangle.c',
+  #'big-little-box.c',
   'big-little-triangle.c',
-  'bug-spline.c',
+  #'bug-spline.c',
   'big-trap.c',
-  'bilevel-image.c',
+  #'bilevel-image.c',
   'bug-277.c',
-  'bug-361.c',
+  #'bug-361.c',
   'bug-40410.c',
-  'bug-431.c',
+  #'bug-431.c',
   'bug-448.c',
   'bug-535.c',
   'bug-51910.c',
   'bug-75705.c',
-  'bug-84115.c',
+  #'bug-84115.c',
   'bug-bo-rectangular.c',
   'bug-bo-collins.c',
-  'bug-bo-ricotz.c',
-  'bug-source-cu.c',
-  'bug-extents.c',
-  'bug-image-compositor.c',  
-  'bug-seams.c',
-  'caps.c',
+  #'bug-bo-ricotz.c',
+  #'bug-source-cu.c',
+  #'bug-extents.c',
+  #'bug-image-compositor.c',  
+  #'bug-seams.c',
+  #'caps.c',
   'checkerboard.c',
-  'caps-joins.c',
-  'caps-joins-alpha.c',
-  'caps-joins-curve.c',
-  'caps-tails-curve.c',
+  #'caps-joins.c',
+  #'caps-joins-alpha.c',
+  #'caps-joins-curve.c',
+  #'caps-tails-curve.c',
   'caps-sub-paths.c',
-  'clear.c',
-  'clear-source.c',
+  #'clear.c',
+  #'clear-source.c',
   'clip-all.c',
   'clip-complex-bug61592.c',
   'clip-complex-shape.c',
   'clip-contexts.c',
-  'clip-disjoint.c',
+  #'clip-disjoint.c',
   'clip-disjoint-hatching.c',
-  'clip-disjoint-quad.c',
-  'clip-device-offset.c',
+  #'clip-disjoint-quad.c',
+  #'clip-device-offset.c',
   'clip-double-free.c',
-  'clip-draw-unbounded.c',
+  #'clip-draw-unbounded.c',
   'clip-empty.c',
   'clip-empty-group.c',
   'clip-empty-save.c',
-  'clip-fill.c',
+  #'clip-fill.c',
   'clip-fill-no-op.c',
-  'clip-fill-rule.c',
-  'clip-fill-rule-pixel-aligned.c',
+  #'clip-fill-rule.c',
+  #'clip-fill-rule-pixel-aligned.c',
   'clip-group-shapes.c',
-  'clip-image.c',
-  'clip-intersect.c',
+  #'clip-image.c',
+  #'clip-intersect.c',
   'clip-mixed-antialias.c',
   'clip-nesting.c',
-  'clip-operator.c',
-  'clip-push-group.c',
+  #'clip-operator.c',
+  #'clip-push-group.c',
   'clip-polygons.c',
   'clip-rectilinear.c',
-  'clip-shape.c',
-  'clip-stroke.c',
+  #'clip-shape.c',
+  #'clip-stroke.c',
   'clip-stroke-no-op.c',
-  'clip-text.c',
-  'clip-twice.c',
+  #'clip-text.c',
+  #'clip-twice.c',
   'clip-twice-rectangle.c',
   'clip-unbounded.c',
   'clip-zero.c',
   'clipped-group.c',
   'clipped-surface.c',
   'close-path.c',
-  'close-path-current-point.c',
+  #'close-path-current-point.c',
   'composite-integer-translate-source.c',
   'composite-integer-translate-over.c',
-  'composite-integer-translate-over-repeat.c',
+  #'composite-integer-translate-over-repeat.c',
   'copy-disjoint.c',
-  'copy-path.c',
-  'coverage.c',
+  #'copy-path.c',
+  #'coverage.c',
   'create-for-stream.c',
   'create-from-broken-png-stream.c',
   'create-from-png.c',
   'create-from-png-16bit.c',
   'create-from-png-stream.c',
-  'culled-glyphs.c',
+  #'culled-glyphs.c',
   'curve-to-as-line-to.c',
-  'dash-caps-joins.c',
-  'dash-curve.c',
+  #'dash-caps-joins.c',
+  #'dash-curve.c',
   'dash-infinite-loop.c',
   'dash-no-dash.c',
   'dash-offset.c',
-  'dash-offset-negative.c',
-  'dash-scale.c',
-  'dash-state.c',
-  'dash-zero-length.c',
-  'degenerate-arc.c',
+  #'dash-offset-negative.c',
+  #'dash-scale.c',
+  #'dash-state.c',
+  #'dash-zero-length.c',
+  #'degenerate-arc.c',
   'degenerate-arcs.c',
-  'degenerate-curve-to.c',
+  #'degenerate-curve-to.c',
   'degenerate-dash.c',
   'degenerate-linear-gradient.c',
-  'degenerate-path.c',
-  'degenerate-pen.c',
+  #'degenerate-path.c',
+  #'degenerate-pen.c',
   'degenerate-radial-gradient.c',
-  'degenerate-rel-curve-to.c',
+  #'degenerate-rel-curve-to.c',
   'degenerate-solid-dash.c',
-  'drunkard-tails.c',
-  'device-offset.c',
+  #'drunkard-tails.c',
+  #'device-offset.c',
   'device-offset-fractional.c',
-  'device-offset-positive.c',
-  'device-offset-scale.c',
+  #'device-offset-positive.c',
+  #'device-offset-scale.c',
   'error-setters.c',
   'extend-pad.c',
-  'extend-pad-border.c',
+  #'extend-pad-border.c',
   'extend-pad-similar.c',
   'extend-reflect.c',
   'extend-reflect-similar.c',
   'extend-repeat.c',
   'extend-repeat-similar.c',
-  'extended-blend.c',
-  'fallback.c',
-  'fill-alpha.c',
-  'fill-alpha-pattern.c',
-  'fill-and-stroke.c',
-  'fill-and-stroke-alpha.c',
-  'fill-and-stroke-alpha-add.c',
-  'fill-degenerate-sort-order.c',
+  #'extended-blend.c',
+  #'fallback.c',
+  #'fill-alpha.c',
+  #'fill-alpha-pattern.c',
+  #'fill-and-stroke.c',
+  #'fill-and-stroke-alpha.c',
+  #'fill-and-stroke-alpha-add.c',
+  #'fill-degenerate-sort-order.c',
   'fill-disjoint.c',
-  'fill-empty.c',
-  'fill-image.c',
-  'fill-missed-stop.c',
-  'fill-rule.c',
-  'filter-bilinear-extents.c',
+  #'fill-empty.c',
+  #'fill-image.c',
+  #'fill-missed-stop.c',
+  #'fill-rule.c',
+  #'filter-bilinear-extents.c',
   'filter-nearest-offset.c',
   'filter-nearest-transformed.c',
-  'finer-grained-fallbacks.c',
+  #'finer-grained-fallbacks.c',
   'font-face-get-type.c',
-  'font-matrix-translation.c',
+  #'font-matrix-translation.c',
   'font-options.c',
-  'glyph-cache-pressure.c',
+  #'glyph-cache-pressure.c',
   'get-and-set.c',
   'get-clip.c',
   'get-group-target.c',
-  'get-path-extents.c',
-  'gradient-alpha.c',
-  'gradient-constant-alpha.c',
-  'gradient-zero-stops.c',
-  'gradient-zero-stops-mask.c',
+  #'get-path-extents.c',
+  #'gradient-alpha.c',
+  #'gradient-constant-alpha.c',
+  #'gradient-zero-stops.c',
+  #'gradient-zero-stops-mask.c',
   'group-clip.c',
   'group-paint.c',
   'group-state.c',
-  'group-unaligned.c',
+  #'group-unaligned.c',
   'hairline.c',
   'half-coverage.c',
-  'halo.c',
-  'hatchings.c',
+  #'halo.c',
+  #'hatchings.c',
   'horizontal-clip.c',
-  'huge-linear.c',
-  'huge-radial.c',
-  'image-surface-source.c',
+  #'huge-linear.c',
+  #'huge-radial.c',
+  #'image-surface-source.c',
   'image-bug-710072.c',
   'implicit-close.c',
   'infinite-join.c',
-  'in-fill-empty-trapezoid.c',
-  'in-fill-trapezoid.c',
+  #'in-fill-empty-trapezoid.c',
+  #'in-fill-trapezoid.c',
   'invalid-matrix.c',
-  'inverse-text.c',
+  #'inverse-text.c',
   'inverted-clip.c',
-  'joins.c',
-  'joins-loop.c',
-  'joins-star.c',
-  'joins-retrace.c',
+  #'joins.c',
+  #'joins-loop.c',
+  #'joins-star.c',
+  #'joins-retrace.c',
   'large-clip.c',
-  'large-font.c',
-  'large-source.c',
+  #'large-font.c',
+  #'large-source.c',
   'large-source-roi.c',
-  'large-twin-antialias-mixed.c',
+  #'large-twin-antialias-mixed.c',
   'leaky-dash.c',
-  'leaky-dashed-rectangle.c',
-  'leaky-dashed-stroke.c',
+  #'leaky-dashed-rectangle.c',
+  #'leaky-dashed-stroke.c',
   'leaky-polygon.c',
   'line-width.c',
   'line-width-large-overlap.c',
-  'line-width-overlap.c',
-  'line-width-scale.c',
+  #'line-width-overlap.c',
+  #'line-width-scale.c',
   'line-width-tolerance.c',
   'line-width-zero.c',
-  'linear-gradient.c',
+  #'linear-gradient.c',
   'linear-gradient-extend.c',
   'linear-gradient-large.c',
-  'linear-gradient-one-stop.c',
-  'linear-gradient-reflect.c',
-  'linear-gradient-subset.c',
-  'linear-step-function.c',
+  #'linear-gradient-one-stop.c',
+  #'linear-gradient-reflect.c',
+  #'linear-gradient-subset.c',
+  #'linear-step-function.c',
   'linear-uniform.c',
-  'long-dashed-lines.c',
+  #'long-dashed-lines.c',
   'long-lines.c',
-  'map-to-image.c',
-  'mask.c',
-  'mask-alpha.c',
-  'mask-ctm.c',
+  #'map-to-image.c',
+  #'mask.c',
+  #'mask-alpha.c',
+  #'mask-ctm.c',
   'mask-glyphs.c',
-  'mask-surface-ctm.c',
-  'mask-transformed-image.c',
-  'mask-transformed-similar.c',
-  'mesh-pattern.c',
+  #'mask-surface-ctm.c',
+  #'mask-transformed-image.c',
+  #'mask-transformed-similar.c',
+  #'mesh-pattern.c',
   'mesh-pattern-accuracy.c',
-  'mesh-pattern-conical.c',
-  'mesh-pattern-control-points.c',
-  'mesh-pattern-fold.c',
-  'mesh-pattern-overlap.c',
-  'mesh-pattern-transformed.c',
+  #'mesh-pattern-conical.c',
+  #'mesh-pattern-control-points.c',
+  #'mesh-pattern-fold.c',
+  #'mesh-pattern-overlap.c',
+  #'mesh-pattern-transformed.c',
   'mime-data.c',
   'mime-surface-api.c',
   'miter-precision.c',
   'move-to-show-surface.c',
-  'negative-stride-image.c',
-  'new-sub-path.c',
-  'nil-surface.c',
+  #'negative-stride-image.c',
+  #'new-sub-path.c',
+  #'nil-surface.c',
   'operator.c',
   'operator-alpha.c',
   'operator-alpha-alpha.c',
-  'operator-clear.c',
-  'operator-source.c',
-  'operator-www.c',
+  #'operator-clear.c',
+  #'operator-source.c',
+  #'operator-www.c',
   'outline-tolerance.c',
   'overflow.c',
   'over-above-source.c',
-  'over-around-source.c',
+  #'over-around-source.c',
   'over-below-source.c',
   'over-between-source.c',
-  'overlapping-boxes.c',
-  'overlapping-glyphs.c',
-  'overlapping-dash-caps.c',
+  #'overlapping-boxes.c',
+  #'overlapping-glyphs.c',
+  #'overlapping-dash-caps.c',
   'paint.c',
   'paint-clip-fill.c',
   'paint-repeat.c',
-  'paint-source-alpha.c',
-  'paint-with-alpha.c',
-  'paint-with-alpha-group-clip.c',
-  'partial-clip-text.c',
-  'partial-coverage.c',
-  'pass-through.c',
-  'path-append.c',
+  #'paint-source-alpha.c',
+  #'paint-with-alpha.c',
+  #'paint-with-alpha-group-clip.c',
+  #'partial-clip-text.c',
+  #'partial-coverage.c',
+  #'pass-through.c',
+  #'path-append.c',
   'path-currentpoint.c',
-  'path-stroke-twice.c',
+  #'path-stroke-twice.c',
   'path-precision.c',
   'pattern-get-type.c',
   'pattern-getters.c',
-  'pdf-isolated-group.c',
-  'pixman-downscale.c',
-  'pixman-rotate.c',
+  #'pdf-isolated-group.c',
+  #'pixman-downscale.c',
+  #'pixman-rotate.c',
   'png.c',
-  'push-group.c',
-  'push-group-color.c',
+  #'push-group.c',
+  #'push-group-color.c',
   'push-group-path-offset.c',
-  'radial-gradient.c',
+  #'radial-gradient.c',
   'radial-gradient-extend.c',
-  'radial-outer-focus.c',
-  'random-clips.c',
-  'random-intersections-eo.c',
-  'random-intersections-nonzero.c',
-  'random-intersections-curves-eo.c',
-  'random-intersections-curves-nz.c',
-  'raster-source.c',
-  'record.c',
-  'record1414x.c',
-  'record2x.c',
-  'record90.c',
-  'recordflip.c',
+  #'radial-outer-focus.c',
+  #'random-clips.c',
+  #'random-intersections-eo.c',
+  #'random-intersections-nonzero.c',
+  #'random-intersections-curves-eo.c',
+  #'random-intersections-curves-nz.c',
+  #'raster-source.c',
+  #'record.c',
+  #'record1414x.c',
+  #'record2x.c',
+  #'record90.c',
+  #'recordflip.c',
   'record-extend.c',
-  'record-neg-extents.c',
-  'record-mesh.c',
-  'record-replay-extend.c',
+  #'record-neg-extents.c',
+  #'record-mesh.c',
+  #'record-replay-extend.c',
   'record-transform-paint.c',
   'record-write-png.c',
   'recording-ink-extents.c',
-  'recording-surface-pattern.c',
-  'recording-surface-extend.c',
+  #'recording-surface-pattern.c',
+  #'recording-surface-extend.c',
   'rectangle-rounding-error.c',
   'rectilinear-fill.c',
   'rectilinear-grid.c',
   'rectilinear-miter-limit.c',
-  'rectilinear-dash.c',
-  'rectilinear-dash-scale.c',
-  'rectilinear-stroke.c',
-  'reflected-stroke.c',
-  'rel-path.c',
+  #'rectilinear-dash.c',
+  #'rectilinear-dash-scale.c',
+  #'rectilinear-stroke.c',
+  #'reflected-stroke.c',
+  #'rel-path.c',
   'rgb24-ignore-alpha.c',
-  'rotate-image-surface-paint.c',
+  #'rotate-image-surface-paint.c',
   'rotate-stroke-box.c',
-  'rotated-clip.c',
-  'rounded-rectangle-fill.c',
-  'rounded-rectangle-stroke.c',
+  #'rotated-clip.c',
+  #'rounded-rectangle-fill.c',
+  #'rounded-rectangle-stroke.c',
   'round-join-bug-520.c',
   'sample.c',
   'scale-down-source-surface-paint.c',
-  'scale-offset-image.c',
-  'scale-offset-similar.c',
-  'scale-source-surface-paint.c',
+  #'scale-offset-image.c',
+  #'scale-offset-similar.c',
+  #'scale-source-surface-paint.c',
   'scaled-font-zero-matrix.c',
-  'stroke-ctm-caps.c',
+  #'stroke-ctm-caps.c',
   'stroke-clipped.c',
-  'stroke-image.c',
+  #'stroke-image.c',
   'stroke-open-box.c',
-  'select-font-face.c',
+  #'select-font-face.c',
   'select-font-no-show-text.c',
   'self-copy.c',
-  'self-copy-overlap.c',
-  'self-intersecting.c',
-  'set-source.c',
-  'show-glyphs-advance.c',
+  #'self-copy-overlap.c',
+  #'self-intersecting.c',
+  #'set-source.c',
+  #'show-glyphs-advance.c',
   'show-glyphs-many.c',
-  'show-text-current-point.c',
-  'shape-general-convex.c',
-  'shape-sierpinski.c',
-  'shifted-operator.c',
-  'simple.c',
+  #'show-text-current-point.c',
+  #'shape-general-convex.c',
+  #'shape-sierpinski.c',
+  #'shifted-operator.c',
+  #'simple.c',
   'skew-extreme.c',
-  'smask.c',
-  'smask-fill.c',
+  #'smask.c',
+  #'smask-fill.c',
   'smask-image-mask.c',
-  'smask-mask.c',
-  'smask-paint.c',
-  'smask-stroke.c',
-  'smask-text.c',
-  'smp-glyph.c',
+  #'smask-mask.c',
+  #'smask-paint.c',
+  #'smask-stroke.c',
+  #'smask-text.c',
+  #'smp-glyph.c',
   'solid-pattern-cache-stress.c',
   'source-clip.c',
   'source-clip-scale.c',
-  'source-surface-scale-paint.c',
-  'spline-decomposition.c',
-  'stride-12-image.c',
-  'stroke-pattern.c',
-  'subsurface.c',
+  #'source-surface-scale-paint.c',
+  #'spline-decomposition.c',
+  #'stride-12-image.c',
+  #'stroke-pattern.c',
+  #'subsurface.c',
   'subsurface-image-repeat.c',
   'subsurface-repeat.c',
   'subsurface-reflect.c',
   'subsurface-pad.c',
   'subsurface-modify-child.c',
   'subsurface-modify-parent.c',
-  'subsurface-outside-target.c',
-  'subsurface-scale.c',
+  #'subsurface-outside-target.c',
+  #'subsurface-scale.c',
   'subsurface-similar-repeat.c',
   'surface-finish-twice.c',
-  'surface-pattern.c',
+  #'surface-pattern.c',
   'surface-pattern-big-scale-down.c',
-  'surface-pattern-operator.c',
-  'surface-pattern-scale-down.c',
+  #'surface-pattern-operator.c',
+  #'surface-pattern-scale-down.c',
   'surface-pattern-scale-down-extend.c',
-  'surface-pattern-scale-up.c',
-  'text-antialias.c',
-  'text-antialias-subpixel.c',
+  #'surface-pattern-scale-up.c',
+  #'text-antialias.c',
+  #'text-antialias-subpixel.c',
   'text-cache-crash.c',
-  'text-glyph-range.c',
-  'text-pattern.c',
-  'text-rotate.c',
-  'text-subpixel.c',
-  'text-transform.c',
-  'text-unhinted-metrics.c',
+  #'text-glyph-range.c',
+  #'text-pattern.c',
+  #'text-rotate.c',
+  #'text-subpixel.c',
+  #'text-transform.c',
+  #'text-unhinted-metrics.c',
   'text-zero-len.c',
-  'thin-lines.c',
-  'tighten-bounds.c',
-  'tiger.c',
+  #'thin-lines.c',
+  #'tighten-bounds.c',
+  #'tiger.c',
   'toy-font-face.c',
-  'transforms.c',
+  #'transforms.c',
   'translate-show-surface.c',
-  'trap-clip.c',
-  'twin.c',
-  'twin-antialias-gray.c',
-  'twin-antialias-mixed.c',
-  'twin-antialias-none.c',
-  'twin-antialias-subpixel.c',
+  #'trap-clip.c',
+  #'twin.c',
+  #'twin-antialias-gray.c',
+  #'twin-antialias-mixed.c',
+  #'twin-antialias-none.c',
+  #'twin-antialias-subpixel.c',
   'unaligned-box.c',
   'unantialiased-shapes.c',
-  'unbounded-operator.c',
-  'unclosed-strokes.c',
+  #'unbounded-operator.c',
+  #'unclosed-strokes.c',
   'user-data.c',
-  'user-font.c',
-  'user-font-color.c',
-  'user-font-mask.c',
-  'user-font-proxy.c',
-  'user-font-rescale.c',
+  #'user-font.c',
+  #'user-font-color.c',
+  #'user-font-mask.c',
+  #'user-font-proxy.c',
+  #'user-font-rescale.c',
   'user-font-subpixel.c',
-  'world-map.c',
+  #'world-map.c',
   'white-in-noop.c',
-  'xcb-huge-image-shm.c',
-  'xcb-huge-subimage.c',
-  'xcb-stress-cache.c',
-  'xcb-snapshot-assert.c',
-  'xcomposite-projection.c',
-  'xlib-expose-event.c',
+  #'xcb-huge-image-shm.c',
+  #'xcb-huge-subimage.c',
+  #'xcb-stress-cache.c',
+  #'xcb-snapshot-assert.c',
+  #'xcomposite-projection.c',
+  #'xlib-expose-event.c',
   'zero-alpha.c',
-  'zero-mask.c',
+  #'zero-mask.c',
 ]
 
 test_pthread_sources = [
-  'pthread-same-source.c',
-  'pthread-show-text.c',
+  #'pthread-same-source.c',
+  #'pthread-show-text.c',
   'pthread-similar.c',
 ]
 
 # Only font-variations.c is ft-specific according to Makefile.sources, the other
 # depend on fontconfig
 test_ft_font_sources = [
-  'font-variations.c',
-  'bitmap-font.c',
-  'ft-color-font.c',
-  'ft-font-create-for-ft-face.c',
-  'ft-show-glyphs-positioning.c',
-  'ft-show-glyphs-table.c',
-  'ft-text-vertical-layout-type1.c',
-  'ft-text-vertical-layout-type3.c',
-  'ft-text-antialias-none.c',
+  #'font-variations.c',
+  #'bitmap-font.c',
+  #'ft-color-font.c',
+  #'ft-font-create-for-ft-face.c',
+  #'ft-show-glyphs-positioning.c',
+  #'ft-show-glyphs-table.c',
+  #'ft-text-vertical-layout-type1.c',
+  #'ft-text-vertical-layout-type3.c',
+  #'ft-text-antialias-none.c',
 ]
 
 test_ft_svg_font_sources = [
-  'ft-svg-color-font.c',
+  #'ft-svg-color-font.c',
 ]
 
 test_ft_svg_ttx_font_sources = [
@@ -447,21 +447,21 @@
 test_pdf_sources = [
   'pdf-features.c',
   'pdf-mime-data.c',
-  'pdf-operators-text.c',
-  'pdf-surface-source.c',
+  #'pdf-operators-text.c',
+  #'pdf-surface-source.c',
   'pdf-tagged-text.c',
 ]
 
 test_ps_sources = [
   'ps-eps.c',
   'ps-features.c',
-  'ps-surface-source.c',
+  #'ps-surface-source.c',
 ]
 
 test_svg_sources = [
   'svg-surface.c',
   'svg-clip.c',
-  'svg-surface-source.c',
+  #'svg-surface-source.c',
 ]
 
 test_xcb_sources = [
