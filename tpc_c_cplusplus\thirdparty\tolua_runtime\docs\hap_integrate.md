# tolua_runtime 集成到应用hap

本库是在RK3568开发板上基于OpenHarmony3.2 Release版本的镜像验证的，如果是从未使用过RK3568，可以先查看[润和RK3568开发板标准系统快速上手](https://gitee.com/openharmony-sig/knowledge_demo_temp/tree/master/docs/rk3568_helloworld)。

## 开发环境

- [开发环境准备](../../../docs/hap_integrate_environment.md)

## 编译三方库

- 下载本仓库

  ```shell
  git clone https://gitee.com/openharmony-sig/tpc_c_cplusplus.git --depth=1
  ```

- 三方库目录结构

  ```shell
  tpc_c_cplusplus/thirdparty/tolua_runtime       #三方库tolua_runtime的目录结构如下
  ├── docs                                       #三方库相关文档的文件夹
  ├── HPKBUILD                                   #构建脚本
  ├── HPKCHECK                                   #测试脚本
  ├── SHA512SUM                                  #三方库校验文件 
  ├── tolua_runtime_ohos_pkg.patch               #原生库的补丁文件
  ├── OAT.xml                                    #OAT开源审查文本文件
  ├── README.OpenSource                          #说明三方库源码的下载地址，版本，license等信息
  ├── README_zh.md                               #三方库简介
  ```
  
- 在lycium目录下编译三方库

  编译环境的搭建参考[准备三方库构建环境](../../../lycium/README.md#1编译环境准备)

  ```shell
  cd lycium
  ./build.sh tolua_runtime
  ```

- 三方库头文件及生成的库

  在lycium目录下会生成usr目录，该目录下存在已编译完成的32位和64位三方库

  ```shell
  tolua_runtime/arm64-v8a   tolua_runtime/armeabi-v7a
  ```

- [测试三方库](#测试三方库)

## 应用中使用三方库

- 在IDE的cpp目录新建一个thirdparty目录，将生成的二进制文件以及头文件拷贝到该目录下，每种架构目录下包含了该库的头文件(include)、二进制文件(lib)，如下图所示：
  &nbsp;

  &nbsp;![thirdparty_install_dir](pic/tolua_runtime_install_dir.png)

- 在最外层（cpp目录下）CMakeLists.txt中添加如下语句

  ```shell
  #将三方库加入工程中
  target_link_libraries(entry PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/tolua_runtime/${OHOS_ARCH}/lib/libtolua.so)
 
  #将三方库的头文件加入工程中
  target_include_directories(entry PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/tolua_runtime/${OHOS_ARCH}/include)
  target_include_directories(entry PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/tolua_runtime/${OHOS_ARCH}/include/cjson)
  target_include_directories(entry PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/tolua_runtime/${OHOS_ARCH}/include/luasocket)
  ```

## 测试三方库

三方库的测试原库未自带的测试用例，开发自添加测试demo进行验证

进入到源码目录下执行
```shell
  ./tolua_runtime-1.0.8.584-armeabi-v7a-build/demo_ohos                                 #32位设备执行的可执行程序
  ./tolua_runtime-1.0.8.584-arm64-v8a-build/demo_ohos                                   #64位设备执行的可执行程序
```
&nbsp;![tolua_runtime_32_test](pic/tolua_runtime_32_test.png)
&nbsp;![tolua_runtime_64_test](pic/tolua_runtime_64_test.png)

## 参考资料

- [润和RK3568开发板标准系统快速上手](https://gitee.com/openharmony-sig/knowledge_demo_temp/tree/master/docs/rk3568_helloworld)
- [OpenHarmony三方库地址](https://gitee.com/openharmony-tpc)
- [OpenHarmony知识体系](https://gitee.com/openharmony-sig/knowledge)
- [通过DevEco Studio开发一个NAPI工程](https://gitee.com/openharmony-sig/knowledge_demo_temp/blob/master/docs/napi_study/docs/hello_napi.md)