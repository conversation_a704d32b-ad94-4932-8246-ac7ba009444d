# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=proj.4
pkgver=4.9.2
pkgrel=0
pkgdesc="Proj.4 is the most famous map projection library of open source GIS, which provides the function of coordinate conversion between multiple coordinate systems"
url="https://github.com/OrdnanceSurvey/proj.4"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT")
depends=()

source="https://github.com/OrdnanceSurvey/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="cmake"
buildlinux=true

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

prepare() {
    #编译linux版本
    if $buildlinux
    then
        mkdir -p $builddir/linux-build
        cd $builddir/linux-build
        cmake ..
        make
        buildlinux=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build 
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH \
    -B$ARCH-build -S./ -L >  $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >>  $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}