# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <hanjin<PERSON><EMAIL>>
pkgname=unixODBC
pkgver=2.3.11
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL-2.1")
depends=()
makedepends=()

source="https://github.com/lurcher/$pkgname/releases/download/$pkgver/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

source envset.sh
host=

prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
}

build() {
    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host --enable-static --disable-shared > `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    unset host
}

check() {
    cd $builddir/$ARCH-build
    sed -i '/.*all-am: Makefile $(LTLIBRARIES)/c\all-am: #Makefile $(LTLIBRARIES)' extras/Makefile
    sed -i '/.*all-am: Makefile $(LTLIBRARIES)/c\all-am: #Makefile $(LTLIBRARIES)' log/Makefile
    sed -i '/.*all-am: Makefile $(LTLIBRARIES)/c\all-am: #Makefile $(LTLIBRARIES)' lst/Makefile
    sed -i '/.*all-am: Makefile $(LTLIBRARIES)/c\all-am: #Makefile $(LTLIBRARIES)' ini/Makefile
    sed -i '/.*all-am: Makefile $(LTLIBRARIES)/c\all-am: #Makefile $(LTLIBRARIES)' libltdl/Makefile
    sed -i '/.*all-am: Makefile $(LTLIBRARIES)/c\all-am: #Makefile $(LTLIBRARIES)' odbcinst/Makefile
    sed -i '/.*all-am: Makefile $(LTLIBRARIES)/c\all-am: #Makefile $(LTLIBRARIES)' DriverManager/Makefile
    sed -i '/.*all-am: Makefile $(PROGRAMS)/c\all-am: #Makefile $(PROGRAMS)' exe/Makefile
    sed -i '/.*all-am: Makefile $(LTLIBRARIES)/c\all-am: #Makefile $(LTLIBRARIES)' cur/Makefile
    cd $OLDPWD
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # make check
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
