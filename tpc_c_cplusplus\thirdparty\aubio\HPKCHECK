# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>,li-santian <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd $builddir-${ARCH}-build/build/examples
    ./aubiomfcc -i ./../../../sourceFile.wav > ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        return -1
    fi
    echo "test 1: pass" >> ${logfile}
    
    ./aubionotes -i ./../../../sourceFile.wav >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        return -1
    fi
    echo "test 2: pass" >> ${logfile}
    
    ./aubioonset -i ./../../../sourceFile.wav >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        return -1
    fi
    echo "test 3: pass" >> ${logfile}
    
    ./aubiopitch -i ./../../../sourceFile.wav >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        return -1
    fi
    echo "test 4: pass" >> ${logfile}
    
    ./aubioquiet -i ./../../../sourceFile.wav >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        return -1
    fi
    echo "test 5: pass" >> ${logfile}
    
    ./aubiotrack -i ./../../../sourceFile.wav >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        return -1
    fi
    echo "test 6: pass" >> ${logfile}
    
    echo "Total test: 6  pass: 6"
    res=$?
    cd $OLDPWD
    return $res
}