# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
qrcodefile=${LYCIUM_THIRDPARTY_ROOT}/$pkgname/qrcode.png
barcodefile=${LYCIUM_THIRDPARTY_ROOT}/$pkgname/barcode.png
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd $pkgname-$ARCH-build
    ./scan_image $qrcodefile > ${logfile} 2>&1
    res=$?
    if [ $res -eq 0 ]
    then
        ./scan_image $barcodefile >> ${logfile} 2>&1
        res=$?
    fi

    cd $OLDPWD

    return $res
}
