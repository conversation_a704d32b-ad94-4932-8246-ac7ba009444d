# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=filesystem 
pkgver=1.5.14
pkgrel=0 
pkgdesc="A C++ library for filesystem"
url="https://github.com/gulrak/filesystem" 
archs=("armeabi-v7a" "arm64-v8a") # cpu 架构
license=("MIT License")
source="https://github.com/gulrak/$pkgname/archive/refs/tags/v$pkgver.tar.gz" 

patchflag=true 
autounpack=true 
downloadpackage=true 

builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz

prepare() {
    if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../filesystem_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}


build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DCMAKE_C_FLAGS=-Wno-unused-command-line-argument -DCMAKE_CXX_FLAGS=-Wno-unused-command-line-argument -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    sed -i 's/filesystem_test\"/filesystem_test\" \"-e\"/g' $ARCH-build/test/CTestTestfile.cmake
    sed -i 's/filesystem_test_cpp17\"/filesystem_test_cpp17\" \"-e\"/g' $ARCH-build/test/CTestTestfile.cmake
    sed -i 's/filesystem_test_cpp20\"/filesystem_test_cpp20\" \"-e\"/g' $ARCH-build/test/CTestTestfile.cmake
    sed -i 's/fwd_impl_test\"/fwd_impl_test\" \"-e\"/g' $ARCH-build/test/CTestTestfile.cmake
    sed -i 's/multifile_test\"/multifile_test\" \"-e\"/g' $ARCH-build/test/CTestTestfile.cmake
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

# 安装打包
package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

# 测试，需要在 ohos 设备上进行
check() {
    echo "The test must be on an OpenHarmony device!"
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}