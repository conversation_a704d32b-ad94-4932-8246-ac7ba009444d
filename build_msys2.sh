#!/bin/bash

# OpenHarmony ijkplayer 依赖库编译脚本 (MSYS2版本)
# 基于原始prebuild.sh脚本，适配MSYS2环境

set -e  # 遇到错误立即退出

echo "========================================"
echo "OpenHarmony ijkplayer 依赖库编译工具"
echo "MSYS2环境版本"
echo "========================================"

# 配置变量 (对应原始prebuild.sh)
ROOT_DIR=$(pwd)
API_VERSION=11
SDK_DIR="$ROOT_DIR/../ohos-sdk-$API_VERSION/windows/$API_VERSION"
LYCIUM_TOOLS_URL="https://gitee.com/openharmony-sig/tpc_c_cplusplus.git"
LYCIUM_ROOT_DIR="$ROOT_DIR/tpc_c_cplusplus"
LYCIUM_TOOLS_DIR="$LYCIUM_ROOT_DIR/lycium"
LYCIUM_THIRDPARTY_DIR="$LYCIUM_ROOT_DIR/thirdparty"
DEPENDS_DIR="$ROOT_DIR/doc"
FFMPEG_NAME="FFmpeg-ff4.0"
LIBYUV_NAME="libyuv-ijk"
SOUNDTOUCH_NAME="soundtouch-ijk"
OPENSSL_NAME="openssl_1_1_1w"

# 包名数组
LIBS_NAME=("FFmpeg-ff4.0" "libyuv-ijk" "soundtouch-ijk" "openssl_1_1_1w")
PACKAGE_NAME=("FFmpeg-ff4.0-ijk0.8.8-20210426-001.tar.gz" "yuv-ijk-r0.2.1-dev.zip" "soundtouch-ijk-r0.1.2-dev.zip" "openssl-OpenSSL_1_1_1w.zip")

echo "当前目录: $ROOT_DIR"
echo "SDK目录: $SDK_DIR"
echo "lycium工具目录: $LYCIUM_TOOLS_DIR"

# 函数：检查必要工具
check_tools() {
    echo "检查必要工具..."
    
    local tools=("git" "wget" "unzip" "tar" "make" "cmake" "gcc" "zip")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo "错误: 缺少以下工具: ${missing_tools[*]}"
        echo "请使用以下命令安装:"
        echo "pacman -S --needed base-devel mingw-w64-x86_64-toolchain"
        echo "pacman -S git wget unzip cmake make autoconf automake libtool"
        return 1
    fi
    
    echo "所有必要工具已安装"
    return 0
}

# 函数：检查SDK
check_sdk() {
    echo "检查OpenHarmony SDK..."
    
    # 检查环境变量
    if [ -n "$OHOS_SDK" ]; then
        SDK_DIR="$OHOS_SDK"
        echo "使用环境变量中的SDK路径: $SDK_DIR"
    fi
    
    if [ ! -d "$SDK_DIR" ]; then
        echo "警告: 未找到OpenHarmony SDK目录: $SDK_DIR"
        echo "请设置OHOS_SDK环境变量或确保SDK安装在正确位置"
        
        # 提示用户输入SDK路径
        read -p "请输入SDK路径 (或按回车跳过): " user_sdk_path
        if [ -n "$user_sdk_path" ]; then
            SDK_DIR="$user_sdk_path"
            export OHOS_SDK="$SDK_DIR"
        else
            echo "跳过SDK检查，继续执行..."
        fi
    else
        echo "找到SDK目录: $SDK_DIR"
        export OHOS_SDK="$SDK_DIR"
    fi
    
    return 0
}

# 函数：准备lycium工具
prepare_lycium() {
    echo "准备lycium编译工具..."
    
    if [ -d "$LYCIUM_ROOT_DIR" ]; then
        echo "清理旧的lycium目录..."
        rm -rf "$LYCIUM_ROOT_DIR"
    fi
    
    echo "下载lycium工具链..."
    git clone "$LYCIUM_TOOLS_URL" -b support_x86 --depth=1 "$LYCIUM_ROOT_DIR"
    if [ $? -ne 0 ]; then
        echo "错误: lycium工具链下载失败"
        return 1
    fi
    
    # 检查是否需要解压工具链
    if [ -f "$LYCIUM_TOOLS_DIR/Buildtools/toolchain.tar.gz" ]; then
        echo "解压工具链..."
        cd "$LYCIUM_TOOLS_DIR/Buildtools"
        tar -zxf toolchain.tar.gz
        if [ $? -ne 0 ]; then
            echo "错误: 工具链解压失败"
            cd "$ROOT_DIR"
            return 1
        fi
        
        # 复制工具链到SDK目录 (如果SDK存在)
        if [ -d "$SDK_DIR/native/llvm/bin" ]; then
            cp toolchain/* "$SDK_DIR/native/llvm/bin/"
            echo "工具链复制到SDK目录完成"
        else
            echo "警告: SDK目录不存在，跳过工具链复制"
        fi
        
        cd "$ROOT_DIR"
    fi
    
    echo "lycium工具链准备完成"
    return 0
}

# 函数：复制依赖配置
copy_depends() {
    local dir="$1"
    local name="$2"
    
    if [ -d "$LYCIUM_THIRDPARTY_DIR/$name" ]; then
        rm -rf "$LYCIUM_THIRDPARTY_DIR/$name"
    fi
    cp -r "$dir/$name" "$LYCIUM_THIRDPARTY_DIR/"
}

prepare_depends() {
    echo "复制依赖配置文件..."
    copy_depends "$DEPENDS_DIR" "$LIBYUV_NAME"
    copy_depends "$DEPENDS_DIR" "$SOUNDTOUCH_NAME"
    echo "依赖配置复制完成"
}

# 函数：下载依赖源码
download_dependencies() {
    echo "下载依赖源码..."

    local temp_dir="$ROOT_DIR/temp_downloads"
    mkdir -p "$temp_dir"
    cd "$temp_dir"

    # 下载FFmpeg - 尝试多个源
    echo "下载FFmpeg源码..."
    if [ ! -d "ffmpeg_src" ]; then
        echo "尝试从GitHub下载FFmpeg..."
        if ! git clone https://github.com/bilibili/FFmpeg.git -b ff4.0--ijk0.8.8--20210426--001 --depth=1 ffmpeg_src; then
            echo "GitHub下载失败，尝试使用Gitee镜像..."
            if ! git clone https://gitee.com/mirrors/FFmpeg.git -b ff4.0--ijk0.8.8--20210426--001 --depth=1 ffmpeg_src; then
                echo "Git下载失败，尝试使用wget下载压缩包..."
                download_ffmpeg_archive
            fi
        fi
    fi

    # 下载soundtouch - 尝试多个源
    echo "下载soundtouch源码..."
    if [ ! -d "soundtouch_src" ]; then
        echo "尝试从GitHub下载soundtouch..."
        if ! git clone https://github.com/bilibili/soundtouch.git -b ijk-r0.1.2-dev --depth=1 soundtouch_src; then
            echo "GitHub下载失败，尝试使用Gitee镜像..."
            git clone https://gitee.com/mirrors/soundtouch.git -b ijk-r0.1.2-dev --depth=1 soundtouch_src || echo "soundtouch下载失败"
        fi
    fi

    # 下载libyuv - 尝试多个源
    echo "下载libyuv源码..."
    if [ ! -d "libyuv_src" ]; then
        echo "尝试从GitHub下载libyuv..."
        if ! git clone https://github.com/bilibili/libyuv.git -b ijk-r0.2.1-dev --depth=1 libyuv_src; then
            echo "GitHub下载失败，尝试使用Gitee镜像..."
            git clone https://gitee.com/mirrors/libyuv.git -b ijk-r0.2.1-dev --depth=1 libyuv_src || echo "libyuv下载失败"
        fi
    fi

    # 下载OpenSSL - 尝试多个源
    echo "下载OpenSSL源码..."
    if [ ! -d "openssl_src" ]; then
        echo "尝试从GitHub下载OpenSSL..."
        if ! git clone https://github.com/openssl/openssl.git -b OpenSSL_1_1_1w --depth=1 openssl_src; then
            echo "GitHub下载失败，尝试使用Gitee镜像..."
            git clone https://gitee.com/mirrors/openssl.git -b OpenSSL_1_1_1w --depth=1 openssl_src || echo "OpenSSL下载失败"
        fi
    fi

    cd "$ROOT_DIR"
    echo "依赖源码下载完成"
}

# 函数：使用wget下载FFmpeg压缩包
download_ffmpeg_archive() {
    echo "使用wget下载FFmpeg压缩包..."
    local archive_url="https://github.com/bilibili/FFmpeg/archive/refs/tags/ff4.0--ijk0.8.8--20210426--001.tar.gz"

    if wget -O ffmpeg.tar.gz "$archive_url"; then
        echo "解压FFmpeg压缩包..."
        tar -xzf ffmpeg.tar.gz
        mv FFmpeg-ff4.0--ijk0.8.8--20210426--001 ffmpeg_src
        rm ffmpeg.tar.gz
        echo "FFmpeg压缩包下载并解压完成"
    else
        echo "FFmpeg下载失败，请检查网络连接"
        return 1
    fi
}

# 函数：创建源码包
create_source_packages() {
    echo "创建源码包..."
    
    local temp_dir="$ROOT_DIR/temp_downloads"
    
    # 确保thirdparty目录存在
    mkdir -p "$LYCIUM_THIRDPARTY_DIR/$FFMPEG_NAME"
    mkdir -p "$LYCIUM_THIRDPARTY_DIR/$SOUNDTOUCH_NAME"
    mkdir -p "$LYCIUM_THIRDPARTY_DIR/$LIBYUV_NAME"
    mkdir -p "$LYCIUM_THIRDPARTY_DIR/$OPENSSL_NAME"
    
    # 创建FFmpeg源码包
    if [ -d "$temp_dir/ffmpeg_src" ]; then
        echo "创建FFmpeg源码包..."
        cd "$temp_dir/ffmpeg_src"
        tar -czf "$LYCIUM_THIRDPARTY_DIR/$FFMPEG_NAME/${PACKAGE_NAME[0]}" .
        cd "$ROOT_DIR"
    fi
    
    # 创建soundtouch源码包
    if [ -d "$temp_dir/soundtouch_src" ]; then
        echo "创建soundtouch源码包..."
        cd "$temp_dir/soundtouch_src"
        zip -r "$LYCIUM_THIRDPARTY_DIR/$SOUNDTOUCH_NAME/${PACKAGE_NAME[2]}" .
        cd "$ROOT_DIR"
    fi
    
    # 创建libyuv源码包
    if [ -d "$temp_dir/libyuv_src" ]; then
        echo "创建libyuv源码包..."
        cd "$temp_dir/libyuv_src"
        zip -r "$LYCIUM_THIRDPARTY_DIR/$LIBYUV_NAME/${PACKAGE_NAME[1]}" .
        cd "$ROOT_DIR"
    fi
    
    # 创建OpenSSL源码包
    if [ -d "$temp_dir/openssl_src" ]; then
        echo "创建OpenSSL源码包..."
        cd "$temp_dir/openssl_src"
        zip -r "$LYCIUM_THIRDPARTY_DIR/$OPENSSL_NAME/${PACKAGE_NAME[3]}" .
        cd "$ROOT_DIR"
    fi
    
    echo "源码包创建完成"
}

# 函数：执行编译
start_build() {
    echo "开始编译依赖库..."
    
    cd "$LYCIUM_TOOLS_DIR"
    if [ $? -ne 0 ]; then
        echo "错误: 无法进入lycium工具目录"
        return 1
    fi
    
    # 确保build.sh有执行权限
    chmod +x build.sh
    
    # 执行编译
    ./build.sh "$FFMPEG_NAME" "$LIBYUV_NAME" "$SOUNDTOUCH_NAME"
    local result=$?
    
    cd "$ROOT_DIR"
    return $result
}

# 函数：安装依赖到项目
install_depends() {
    echo "安装依赖到项目目录..."
    
    local install_dir="$ROOT_DIR/ijkplayer/src/main/cpp/third_party"
    
    # 创建目标目录
    mkdir -p "$install_dir/ffmpeg/ffmpeg"
    mkdir -p "$install_dir/soundtouch"
    mkdir -p "$install_dir/yuv"
    mkdir -p "$install_dir/openssl"
    
    # 复制编译结果
    if [ -d "$LYCIUM_TOOLS_DIR/usr/$FFMPEG_NAME" ]; then
        cp -r "$LYCIUM_TOOLS_DIR/usr/$FFMPEG_NAME"/* "$install_dir/ffmpeg/ffmpeg/"
        echo "FFmpeg安装完成"
    else
        echo "警告: FFmpeg编译结果不存在"
    fi
    
    if [ -d "$LYCIUM_TOOLS_DIR/usr/yuv" ]; then
        cp -r "$LYCIUM_TOOLS_DIR/usr/yuv"/* "$install_dir/yuv/"
        echo "libyuv安装完成"
    else
        echo "警告: libyuv编译结果不存在"
    fi
    
    if [ -d "$LYCIUM_TOOLS_DIR/usr/soundtouch" ]; then
        cp -r "$LYCIUM_TOOLS_DIR/usr/soundtouch"/* "$install_dir/soundtouch/"
        echo "soundtouch安装完成"
    else
        echo "警告: soundtouch编译结果不存在"
    fi
    
    if [ -d "$LYCIUM_TOOLS_DIR/usr/$OPENSSL_NAME" ]; then
        cp -r "$LYCIUM_TOOLS_DIR/usr/$OPENSSL_NAME"/* "$install_dir/openssl/"
        echo "OpenSSL安装完成"
    else
        echo "警告: OpenSSL编译结果不存在"
    fi
    
    return 0
}

# 主函数
main() {
    echo "开始执行依赖库编译流程..."
    
    # 1. 检查工具
    check_tools || exit 1
    
    # 2. 检查SDK
    check_sdk
    
    # 3. 准备lycium工具
    prepare_lycium || exit 1
    
    # 4. 复制依赖配置
    prepare_depends
    
    # 5. 下载依赖源码
    download_dependencies
    
    # 6. 创建源码包
    create_source_packages
    
    # 7. 执行编译
    start_build || {
        echo "错误: 编译失败"
        exit 1
    }
    
    # 8. 安装依赖
    install_depends || {
        echo "错误: 依赖安装失败"
        exit 1
    }
    
    echo "========================================"
    echo "编译完成！"
    echo "========================================"
    echo "依赖库已安装到以下目录："
    echo "- FFmpeg: ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/"
    echo "- soundtouch: ijkplayer/src/main/cpp/third_party/soundtouch/"
    echo "- libyuv: ijkplayer/src/main/cpp/third_party/yuv/"
    echo "- OpenSSL: ijkplayer/src/main/cpp/third_party/openssl/"
    echo ""
    echo "现在可以使用DevEco Studio编译整个项目了！"
    
    return 0
}

# 执行主函数
main "$@"
