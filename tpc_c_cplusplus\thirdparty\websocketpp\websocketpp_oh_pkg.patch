diff -rupN websocketpp-0.8.2/CMakeLists.txt websocketpp/CMakeLists.txt
--- websocketpp-0.8.2/CMakeLists.txt	2020-04-19 18:25:17.000000000 +0000
+++ websocketpp/CMakeLists.txt	2023-03-31 08:53:54.953140034 +0000
@@ -152,7 +152,7 @@ if (BUILD_TESTS OR BUILD_EXAMPLES)
         endif()
         set (WEBSOCKETPP_PLATFORM_TLS_LIBS ssl crypto)
         set (WEBSOCKETPP_BOOST_LIBS system thread)
-        set (CMAKE_CXX_FLAGS ${CMAKE_CXX_FLAGS} "-std=c++0x -stdlib=libc++") # todo: is libc++ really needed here?
+        set (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++0x -stdlib=libc++") # todo: is libc++ really needed here?
         if (NOT APPLE)
             add_definitions (-DNDEBUG -Wall -Wno-padded) # todo: should we use CMAKE_C_FLAGS for these?
         endif ()
