# 在线开发环境使用指南

## 🚀 GitHub Codespaces（推荐）

### 步骤1：准备GitHub仓库
```bash
# 1. 将当前项目推送到GitHub
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/your-username/ohos_ijkplayer-2.0.3.git
git push -u origin main
```

### 步骤2：创建Codespace
1. **访问您的GitHub仓库**
2. **点击绿色的"Code"按钮**
3. **选择"Codespaces"标签**
4. **点击"Create codespace on main"**

### 步骤3：在Codespace中编译
```bash
# 环境会自动设置，然后运行：
chmod +x prebuild.sh
./prebuild.sh
```

## 🌐 Gitpod

### 快速启动
1. **访问**: https://gitpod.io/#https://github.com/your-username/ohos_ijkplayer-2.0.3
2. **自动启动Ubuntu环境**
3. **运行编译脚本**

### 在Gitpod中编译
```bash
# 安装依赖
sudo apt-get update
sudo apt-get install -y build-essential cmake git wget

# 运行编译
chmod +x prebuild.sh
./prebuild.sh
```

## ☁️ 其他在线服务

### 1. Replit
- 访问: https://replit.com
- 选择"Import from GitHub"
- 选择Bash环境

### 2. CodeSandbox
- 访问: https://codesandbox.io
- 导入GitHub仓库
- 使用Container环境

### 3. Gitiles (Google)
- 适合查看和小修改
- 不适合大型编译

## 📋 在线编译的优势

### GitHub Codespaces
- ✅ **免费额度**: 每月60小时
- ✅ **性能**: 2-4核CPU，8GB RAM
- ✅ **存储**: 32GB SSD
- ✅ **网络**: 高速网络连接
- ✅ **集成**: 与GitHub完美集成

### Gitpod
- ✅ **免费额度**: 每月50小时
- ✅ **性能**: 4核CPU，8GB RAM
- ✅ **存储**: 30GB SSD
- ✅ **启动**: 快速启动（<30秒）

## 🔧 解决SDK问题

### 方案1：使用在线SDK
```bash
# 下载OpenHarmony SDK到在线环境
wget https://repo.harmonyos.com/harmonyos/os/sdk/ohos-sdk-linux.tar.gz
tar -xzf ohos-sdk-linux.tar.gz
export OHOS_SDK=/workspaces/ohos-sdk/linux/11
```

### 方案2：跳过SDK检查
```bash
# 修改prebuild.sh，跳过SDK检查
sed -i 's/check_sdk/#check_sdk/' prebuild.sh
```

## 📥 下载编译结果

### 从Codespaces下载
1. **压缩编译结果**:
   ```bash
   tar -czf compiled_libs.tar.gz ijkplayer/src/main/cpp/third_party/
   ```

2. **下载文件**:
   - 在VS Code中右键文件
   - 选择"Download"

### 从Gitpod下载
```bash
# 创建下载包
zip -r compiled_libs.zip ijkplayer/src/main/cpp/third_party/

# 在浏览器中下载
# 文件会出现在左侧文件树中
```

## ⏱️ 时间管理

### 免费额度使用建议
- **GitHub Codespaces**: 60小时/月
- **Gitpod**: 50小时/月

### 节省时间的技巧
1. **预先准备**: 在本地准备好所有源码
2. **批量操作**: 一次性完成所有编译
3. **及时下载**: 编译完成立即下载结果
4. **暂停环境**: 不用时及时停止

## 🎯 完整工作流程

### 1. 准备阶段（本地）
```bash
# 提交代码到GitHub
git add .
git commit -m "Ready for online compilation"
git push
```

### 2. 编译阶段（在线）
```bash
# 在Codespace/Gitpod中
chmod +x prebuild.sh
./prebuild.sh
```

### 3. 下载阶段（在线→本地）
```bash
# 打包编译结果
tar -czf ohos_ijkplayer_libs.tar.gz ijkplayer/src/main/cpp/third_party/
```

### 4. 集成阶段（本地）
```bash
# 解压到本地项目
tar -xzf ohos_ijkplayer_libs.tar.gz
```

## 🔍 故障排除

### 常见问题
1. **网络超时**: 使用国内镜像源
2. **空间不足**: 清理临时文件
3. **权限问题**: 使用sudo

### 解决方案
```bash
# 设置国内镜像
export LYCIUM_TOOLS_URL=https://gitee.com/openharmony-sig/tpc_c_cplusplus.git

# 清理空间
sudo apt-get clean
rm -rf /tmp/*

# 检查空间
df -h
```

## 💡 最佳实践

1. **选择合适的服务**: GitHub Codespaces最稳定
2. **监控使用时间**: 避免超出免费额度
3. **备份重要文件**: 及时下载编译结果
4. **优化编译脚本**: 减少不必要的步骤

## 🎉 开始使用

推荐顺序：
1. **GitHub Codespaces** (最稳定)
2. **Gitpod** (启动最快)
3. **Replit** (最简单)

现在就可以开始在线编译了！
