# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL> luozhu <<EMAIL>>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd $builddir/$ARCH-build

    # 测试项ssl_test
    # 这个测试项因为域名不匹配失败，测试结果与x86 Ubuntu 20.04.6上一致,用--gtest_filter=*-SSLTest.HostMatching屏蔽掉。
    ./ssl/ssl_test --gtest_filter=*-SSLTest.HostMatching > ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        echo "ssl test failed" >> ${logfile} 2>&1
        res=1
    fi

    # 测试项crypto_test
    ./crypto/crypto_test >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        echo "crypto test failed" >> ${logfile} 2>&1
        res=2
    fi

    # 测试项urandom_test
    ./crypto/urandom_test >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        echo "urandom test failed" >> ${logfile} 2>&1
        res=3
    fi

    # 测试项decrepit_test
    ./decrepit/decrepit_test >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        echo "decrepit test failed" >> ${logfile} 2>&1
        res=4
    fi

    cd $OLDPWD
    return $res
}
