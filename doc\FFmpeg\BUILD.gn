# FFmpeg build

import("//build/ohos.gni")

config("ijkffmpeg_config") {
  cflags = [
    "-Wno-sign-compare",
    "-Wno-implicit-function-declaration",
    "-Wno-parentheses",
    "-Wno-string-conversion",
    "-Wno-string-plus-int",
    "-Wno-asm-operand-widths",
    "-Wno-pointer-sign",
    "-Wno-deprecated-declarations",
    "-Wno-implicit-int",
    "-Wno-switch",
    "-Wno-incompatible-pointer-types-discards-qualifiers",
    "-Wno-int-conversion",
    "-Wno-absolute-value",
    "-Wno-unused-function",
    "-Wno-unused-label",
    "-Wno-unused-const-variable",
    "-Wno-visibility",
    "-Wno-incompatible-pointer-types",
    "-Wno-sometimes-uninitialized",
    "-Wno-format",
    "-Wno-tautological-constant-out-of-range-compare",
    "-DHAVE_AV_CONFIG_H",
    "-DCONFIG_H263_ENCODER",
    "-DCONFIG_AC3_DEMUXER",
    "-DCONFIG_EAC3_DEMUXER",
    "-DCONFIG_IMAGE2_DEMUXER",
    "-DCONFIG_RTPDEC",
    "-DTARGET_CONFIG=\"config-arm64.h\"",
    "-Wno-macro-redefined",
    "-DCONFIG_AC3_DEMUXER",
    "-DCONFIG_AC3_MUXER",
    "-DCONFIG_ADX_MUXER",
    "-DCONFIG_AMRNB_DEMUXER",
    "-DCONFIG_AMRWB_DEMUXER",
    "-DCONFIG_AMR_DEMUXER",
    "-DCONFIG_APTX_DEMUXER",
    "-DCONFIG_APTX_HD_DEMUXER",
    "-DCONFIG_APTX_HD_MUXER",
    "-DCONFIG_APTX_MUXER",
    "-DCONFIG_ASF_MUXER",
    "-DCONFIG_ASF_STREAM_MUXER",
    "-DCONFIG_AU_DEMUXER",
    "-DCONFIG_AU_MUXER",
    "-DCONFIG_AVM2_MUXER",
    "-DCONFIG_AVS2_MUXER",
    "-DCONFIG_BINTEXT_DEMUXER",
    "-DCONFIG_ADF_DEMUXER",
    "-DCONFIG_IDF_DEMUXER",
    "-DCONFIG_BIT_DEMUXER",
    "-DCONFIG_BIT_MUXER",
    "-DCONFIG_CAVSVIDEO_MUXER",
    "-DCONFIG_CODEC2RAW_DEMUXER",
    "-DCONFIG_CODEC2RAW_MUXER",
    "-DCONFIG_CODEC2_DEMUXER",
    "-DCONFIG_CODEC2_MUXER",
    "-DCONFIG_DATA_DEMUXER",
    "-DCONFIG_DATA_MUXER",
    "-DCONFIG_DNXHD_MUXER",
    "-DCONFIG_DTS_MUXER",
    "-DCONFIG_EAC3_DEMUXER",
    "-DCONFIG_EAC3_MUXER",
    "-DCONFIG_F4V_MUXER",
    "-DCONFIG_FILE_PROTOCOL",
    "-DCONFIG_FRAMEHASH_MUXER",
    "-DCONFIG_FRAMEHASH_MUXER",
    "-DCONFIG_FRAMEMD5_MUXER",
    "-DCONFIG_FRAMEMD5_MUXER",
    "-DCONFIG_G722_MUXER",
    "-DCONFIG_G723_1_MUXER",
    "-DCONFIG_G726LE_DEMUXER",
    "-DCONFIG_G726LE_MUXER",
    "-DCONFIG_G726_DEMUXER",
    "-DCONFIG_G726_MUXER",
    "-DCONFIG_GSM_MUXER",
    "-DCONFIG_H261_DECODER",
    "-DCONFIG_H261_MUXER",
    "-DCONFIG_H263_DECODER",
    "-DCONFIG_H263_MUXER",
    "-DCONFIG_H264_MUXER",
    "-DCONFIG_HASH_MUXER",
    "-DCONFIG_HASH_MUXER",
    "-DCONFIG_FRAMEHASH_MUXER",
    "-DCONFIG_HASH_MUXER",
    "-DCONFIG_MD5_MUXER",
    "-DCONFIG_HEVC_MUXER",
    "-DCONFIG_HTTPPROXY_PROTOCOL",
    "-DCONFIG_HTTPS_PROTOCOL",
    "-DCONFIG_IMAGE2PIPE_DEMUXER",
    "-DCONFIG_IMAGE2PIPE_MUXER",
    "-DCONFIG_IMAGE2_DEMUXER",
    "-DCONFIG_IPOD_MUXER",
    "-DCONFIG_ISMV_MUXER",
    "-DCONFIG_M4V_MUXER",
    "-DCONFIG_MATROSKA_AUDIO_MUXER",
    "-DCONFIG_MATROSKA_MUXER",
    "-DCONFIG_MBEDTLS",
    "-DCONFIG_MD5_MUXER",
    "-DCONFIG_MD5_MUXER",
    "-DCONFIG_FRAMEMD5_MUXER",
    "-DCONFIG_MJPEG_2000_DEMUXER",
    "-DCONFIG_MJPEG_DEMUXER",
    "-DCONFIG_MJPEG_MUXER",
    "-DCONFIG_MLP_DEMUXER",
    "-DCONFIG_MLP_MUXER",
    "-DCONFIG_MMF_DEMUXER",
    "-DCONFIG_MOV_MUXER",
    "-DCONFIG_MP2_MUXER",
    "-DCONFIG_MP4_MUXER",
    "-DCONFIG_MPEG1SYSTEM_MUXER",
    "-DCONFIG_MPEG1VCD_MUXER",
    "-DCONFIG_MPEG1VIDEO_MUXER",
    "-DCONFIG_MPEG2DVD_MUXER",
    "-DCONFIG_MPEG2SVCD_MUXER",
    "-DCONFIG_MPEG2VIDEO_MUXER",
    "-DCONFIG_MPEG2VOB_MUXER",
    "-DCONFIG_MPEG4_DECODER",
    "-DCONFIG_NETWORK",
    "-DCONFIG_OGA_MUXER",
    "-DCONFIG_OGG_MUXER",
    "-DCONFIG_OGV_MUXER",
    "-DCONFIG_OPUS_MUXER",
    "-DCONFIG_PIPE_PROTOCOL",
    "-DCONFIG_PSP_MUXER",
    "-DCONFIG_RAWVIDEO_MUXER",
    "-DCONFIG_RTP_DEMUXER",
    "-DCONFIG_RTP_MUXER",
    "-DCONFIG_RTSP_DEMUXER",
    "-DCONFIG_RTSP_MUXER",
    "-DCONFIG_SBC_MUXER",
    "-DCONFIG_SDP_DEMUXER",
    "-DCONFIG_SEGMENT_MUXER",
    "-DCONFIG_SINGLEJPEG_MUXER",
    "-DCONFIG_SPX_MUXER",
    "-DCONFIG_STREAM_SEGMENT_MUXER",
    "-DCONFIG_SWF_MUXER",
    "-DCONFIG_TG2_MUXER",
    "-DCONFIG_TGP_MUXER",
    "-DCONFIG_TRUEHD_DEMUXER",
    "-DCONFIG_TRUEHD_MUXER",
    "-DCONFIG_V210X_DEMUXER",
    "-DCONFIG_V210_DEMUXER",
    "-DCONFIG_VC1_MUXER",
    "-DCONFIG_VC1_DEMUXER",
    "-DCONFIG_W64_DEMUXER",
    "-DCONFIG_W64_MUXER",
    "-DCONFIG_WAV_DEMUXER",
    "-DCONFIG_WAV_MUXER",
    "-DCONFIG_WEBM_CHUNK_MUXER",
    "-DCONFIG_WEBM_DASH_MANIFEST_MUXER",
    "-DCONFIG_WEBM_MUXER",
    "-DCONFIG_ZLIB",
    "-DCONFIG_RV30_PARSER",
    "-DCONFIG_DVPROFILE",
    "-Wdeclaration-after-statement",
    "-Wmissing-declarations",
    "-DOPENSSL_ARM_PLATFORM",
  ]
  if (use_musl) {
    cflags += [ "-Wno-bool-operation" ]
  }
}

ohos_source_set("ijkffmpeg_dynamic") {
  sources = [
    "//third_party/ijkffmpeg/libavcodec/aac_ac3_parser.c",
    "//third_party/ijkffmpeg/libavcodec/aac_adtstoasc_bsf.c",
    "//third_party/ijkffmpeg/libavcodec/aac_parser.c",
    "//third_party/ijkffmpeg/libavcodec/aaccoder.c",
    "//third_party/ijkffmpeg/libavcodec/aacdec.c",
    "//third_party/ijkffmpeg/libavcodec/aacenc.c",
    "//third_party/ijkffmpeg/libavcodec/aacenc_is.c",
    "//third_party/ijkffmpeg/libavcodec/aacenc_ltp.c",
    "//third_party/ijkffmpeg/libavcodec/aacenc_pred.c",
    "//third_party/ijkffmpeg/libavcodec/aacenc_tns.c",
    "//third_party/ijkffmpeg/libavcodec/aacenctab.c",
    "//third_party/ijkffmpeg/libavcodec/aacps_float.c",
    "//third_party/ijkffmpeg/libavcodec/aacpsdsp_float.c",
    "//third_party/ijkffmpeg/libavcodec/aacpsy.c",
    "//third_party/ijkffmpeg/libavcodec/aacsbr.c",
    "//third_party/ijkffmpeg/libavcodec/aactab.c",
    "//third_party/ijkffmpeg/libavcodec/aandcttab.c",
    "//third_party/ijkffmpeg/libavcodec/ac3_parser.c",
    "//third_party/ijkffmpeg/libavcodec/ac3tab.c",
    "//third_party/ijkffmpeg/libavcodec/adts_header.c",
    "//third_party/ijkffmpeg/libavcodec/adts_parser.c",
    "//third_party/ijkffmpeg/libavcodec/allcodecs.c",
    "//third_party/ijkffmpeg/libavcodec/apedec.c",
    "//third_party/ijkffmpeg/libavcodec/audio_frame_queue.c",
    "//third_party/ijkffmpeg/libavcodec/avdct.c",
    "//third_party/ijkffmpeg/libavcodec/avfft.c",
    "//third_party/ijkffmpeg/libavcodec/avpacket.c",
    "//third_party/ijkffmpeg/libavcodec/avpicture.c",
    "//third_party/ijkffmpeg/libavcodec/bitstream.c",
    "//third_party/ijkffmpeg/libavcodec/bitstream_filter.c",
    "//third_party/ijkffmpeg/libavcodec/bitstream_filters.c",
    "//third_party/ijkffmpeg/libavcodec/blockdsp.c",
    "//third_party/ijkffmpeg/libavcodec/bsf.c",
    "//third_party/ijkffmpeg/libavcodec/bswapdsp.c",
    "//third_party/ijkffmpeg/libavcodec/cabac.c",
    "//third_party/ijkffmpeg/libavcodec/cbrt_data.c",
    "//third_party/ijkffmpeg/libavcodec/codec_desc.c",
    "//third_party/ijkffmpeg/libavcodec/d3d11va.c",
    "//third_party/ijkffmpeg/libavcodec/dct.c",
    "//third_party/ijkffmpeg/libavcodec/dct32_fixed.c",
    "//third_party/ijkffmpeg/libavcodec/dct32_float.c",
    "//third_party/ijkffmpeg/libavcodec/decode.c",
    "//third_party/ijkffmpeg/libavcodec/dirac.c",
    "//third_party/ijkffmpeg/libavcodec/dv_profile.c",
    "//third_party/ijkffmpeg/libavcodec/encode.c",
    "//third_party/ijkffmpeg/libavcodec/error_resilience.c",
    "//third_party/ijkffmpeg/libavcodec/fdctdsp.c",
    "//third_party/ijkffmpeg/libavcodec/fft_fixed_32.c",
    "//third_party/ijkffmpeg/libavcodec/fft_float.c",
    "//third_party/ijkffmpeg/libavcodec/fft_init_table.c",
    "//third_party/ijkffmpeg/libavcodec/flac.c",
    "//third_party/ijkffmpeg/libavcodec/flac_parser.c",
    "//third_party/ijkffmpeg/libavcodec/flacdata.c",
    "//third_party/ijkffmpeg/libavcodec/flacdec.c",
    "//third_party/ijkffmpeg/libavcodec/flacdsp.c",
    "//third_party/ijkffmpeg/libavcodec/flvdec.c",
    "//third_party/ijkffmpeg/libavcodec/flvenc.c",
    "//third_party/ijkffmpeg/libavcodec/frame_thread_encoder.c",
    "//third_party/ijkffmpeg/libavcodec/golomb.c",
    "//third_party/ijkffmpeg/libavcodec/h263.c",
    "//third_party/ijkffmpeg/libavcodec/h263_parser.c",
    "//third_party/ijkffmpeg/libavcodec/h263data.c",
    "//third_party/ijkffmpeg/libavcodec/h263dec.c",
    "//third_party/ijkffmpeg/libavcodec/h263dsp.c",
    "//third_party/ijkffmpeg/libavcodec/h2645_parse.c",
    "//third_party/ijkffmpeg/libavcodec/h264_cabac.c",
    "//third_party/ijkffmpeg/libavcodec/h264_cavlc.c",
    "//third_party/ijkffmpeg/libavcodec/h264_direct.c",
    "//third_party/ijkffmpeg/libavcodec/h264_loopfilter.c",
    "//third_party/ijkffmpeg/libavcodec/h264_mb.c",
    "//third_party/ijkffmpeg/libavcodec/h264_mp4toannexb_bsf.c",
    "//third_party/ijkffmpeg/libavcodec/h264_parse.c",
    "//third_party/ijkffmpeg/libavcodec/h264_parser.c",
    "//third_party/ijkffmpeg/libavcodec/h264_picture.c",
    "//third_party/ijkffmpeg/libavcodec/h264_ps.c",
    "//third_party/ijkffmpeg/libavcodec/h264_refs.c",
    "//third_party/ijkffmpeg/libavcodec/h264_sei.c",
    "//third_party/ijkffmpeg/libavcodec/h264_slice.c",
    "//third_party/ijkffmpeg/libavcodec/h264chroma.c",
    "//third_party/ijkffmpeg/libavcodec/h264data.c",
    "//third_party/ijkffmpeg/libavcodec/h264dec.c",
    "//third_party/ijkffmpeg/libavcodec/h264dsp.c",
    "//third_party/ijkffmpeg/libavcodec/h264idct.c",
    "//third_party/ijkffmpeg/libavcodec/h264pred.c",
    "//third_party/ijkffmpeg/libavcodec/h264qpel.c",
    "//third_party/ijkffmpeg/libavcodec/hpeldsp.c",
    "//third_party/ijkffmpeg/libavcodec/idctdsp.c",
    "//third_party/ijkffmpeg/libavcodec/iirfilter.c",
    "//third_party/ijkffmpeg/libavcodec/imgconvert.c",
    "//third_party/ijkffmpeg/libavcodec/ituh263dec.c",
    "//third_party/ijkffmpeg/libavcodec/ituh263enc.c",
    "//third_party/ijkffmpeg/libavcodec/jfdctfst.c",
    "//third_party/ijkffmpeg/libavcodec/jfdctint.c",
    "//third_party/ijkffmpeg/libavcodec/jni.c",
    "//third_party/ijkffmpeg/libavcodec/jrevdct.c",
    "//third_party/ijkffmpeg/libavcodec/kbdwin.c",
    "//third_party/ijkffmpeg/libavcodec/latm_parser.c",
    "//third_party/ijkffmpeg/libavcodec/lossless_audiodsp.c",
    "//third_party/ijkffmpeg/libavcodec/lpc.c",
    "//third_party/ijkffmpeg/libavcodec/mathtables.c",
    "//third_party/ijkffmpeg/libavcodec/mdct15.c",
    "//third_party/ijkffmpeg/libavcodec/mdct_fixed_32.c",
    "//third_party/ijkffmpeg/libavcodec/mdct_float.c",
    "//third_party/ijkffmpeg/libavcodec/me_cmp.c",
    "//third_party/ijkffmpeg/libavcodec/mediacodec.c",
    "//third_party/ijkffmpeg/libavcodec/motion_est.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg12.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg12data.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg12dec.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg12framerate.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg4audio.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg4video.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg4video_parser.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg4videodec.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg4videoenc.c",
    "//third_party/ijkffmpeg/libavcodec/mpeg_er.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudio.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudio_parser.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudiodata.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudiodec_fixed.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudiodec_float.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudiodecheader.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudiodsp.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudiodsp_data.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudiodsp_fixed.c",
    "//third_party/ijkffmpeg/libavcodec/mpegaudiodsp_float.c",
    "//third_party/ijkffmpeg/libavcodec/mpegpicture.c",
    "//third_party/ijkffmpeg/libavcodec/mpegutils.c",
    "//third_party/ijkffmpeg/libavcodec/mpegvideo.c",
    "//third_party/ijkffmpeg/libavcodec/mpegvideo_enc.c",
    "//third_party/ijkffmpeg/libavcodec/mpegvideo_motion.c",
    "//third_party/ijkffmpeg/libavcodec/mpegvideodata.c",
    "//third_party/ijkffmpeg/libavcodec/mpegvideodsp.c",
    "//third_party/ijkffmpeg/libavcodec/mpegvideoencdsp.c",
    "//third_party/ijkffmpeg/libavcodec/null_bsf.c",
    "//third_party/ijkffmpeg/libavcodec/options.c",
    "//third_party/ijkffmpeg/libavcodec/opus.c",
    "//third_party/ijkffmpeg/libavcodec/opus_celt.c",
    "//third_party/ijkffmpeg/libavcodec/opus_pvq.c",
    "//third_party/ijkffmpeg/libavcodec/opus_rc.c",
    "//third_party/ijkffmpeg/libavcodec/opus_silk.c",
    "//third_party/ijkffmpeg/libavcodec/opusdec.c",
    "//third_party/ijkffmpeg/libavcodec/opusenc.c",
    "//third_party/ijkffmpeg/libavcodec/opusenc_psy.c",
    "//third_party/ijkffmpeg/libavcodec/opustab.c",
    "//third_party/ijkffmpeg/libavcodec/parser.c",
    "//third_party/ijkffmpeg/libavcodec/pixblockdsp.c",
    "//third_party/ijkffmpeg/libavcodec/profiles.c",
    "//third_party/ijkffmpeg/libavcodec/psymodel.c",
    "//third_party/ijkffmpeg/libavcodec/pthread.c",
    "//third_party/ijkffmpeg/libavcodec/pthread_frame.c",
    "//third_party/ijkffmpeg/libavcodec/pthread_slice.c",
    "//third_party/ijkffmpeg/libavcodec/qpeldsp.c",
    "//third_party/ijkffmpeg/libavcodec/qsv_api.c",
    "//third_party/ijkffmpeg/libavcodec/ratecontrol.c",
    "//third_party/ijkffmpeg/libavcodec/raw.c",  
    "//third_party/ijkffmpeg/libavcodec/rdft.c",
    "//third_party/ijkffmpeg/libavcodec/rl.c",
    "//third_party/ijkffmpeg/libavcodec/sbrdsp.c",
    "//third_party/ijkffmpeg/libavcodec/simple_idct.c",
    "//third_party/ijkffmpeg/libavcodec/sinewin.c",
    "//third_party/ijkffmpeg/libavcodec/startcode.c",
    "//third_party/ijkffmpeg/libavcodec/utils.c",
    "//third_party/ijkffmpeg/libavcodec/videodsp.c",
    "//third_party/ijkffmpeg/libavcodec/vorbis.c",
    "//third_party/ijkffmpeg/libavcodec/vorbis_data.c",
    "//third_party/ijkffmpeg/libavcodec/vorbis_parser.c",
    "//third_party/ijkffmpeg/libavcodec/vorbisdec.c",
    "//third_party/ijkffmpeg/libavcodec/vorbisdsp.c",
    "//third_party/ijkffmpeg/libavcodec/vp56rac.c",
    "//third_party/ijkffmpeg/libavcodec/vp8.c",
    "//third_party/ijkffmpeg/libavcodec/vp8_parser.c",
    "//third_party/ijkffmpeg/libavcodec/vp8dsp.c",
    "//third_party/ijkffmpeg/libavcodec/vp9.c",
    "//third_party/ijkffmpeg/libavcodec/vp9_parser.c",
    "//third_party/ijkffmpeg/libavcodec/vp9_superframe_bsf.c",
    "//third_party/ijkffmpeg/libavcodec/vp9_superframe_split_bsf.c",
    "//third_party/ijkffmpeg/libavcodec/vp9block.c",
    "//third_party/ijkffmpeg/libavcodec/vp9data.c",
    "//third_party/ijkffmpeg/libavcodec/vp9dsp.c",
    "//third_party/ijkffmpeg/libavcodec/vp9dsp_10bpp.c",
    "//third_party/ijkffmpeg/libavcodec/vp9dsp_12bpp.c",
    "//third_party/ijkffmpeg/libavcodec/vp9dsp_8bpp.c",
    "//third_party/ijkffmpeg/libavcodec/vp9lpf.c",
    "//third_party/ijkffmpeg/libavcodec/vp9mvs.c",
    "//third_party/ijkffmpeg/libavcodec/vp9prob.c",
    "//third_party/ijkffmpeg/libavcodec/vp9recon.c",
    "//third_party/ijkffmpeg/libavcodec/xiph.c",
    "//third_party/ijkffmpeg/libavcodec/xvididct.c",
    "//third_party/ijkffmpeg/libavcodec/jpegtables.c",
    "//third_party/ijkffmpeg/libavfilter/allfilters.c",
    "//third_party/ijkffmpeg/libavfilter/audio.c",
    "//third_party/ijkffmpeg/libavfilter/avfilter.c",
    "//third_party/ijkffmpeg/libavfilter/avfiltergraph.c",
    "//third_party/ijkffmpeg/libavfilter/buffersink.c",
    "//third_party/ijkffmpeg/libavfilter/buffersrc.c",
    "//third_party/ijkffmpeg/libavfilter/drawutils.c",
    "//third_party/ijkffmpeg/libavfilter/fifo.c",
    "//third_party/ijkffmpeg/libavfilter/formats.c",
    "//third_party/ijkffmpeg/libavfilter/framepool.c",
    "//third_party/ijkffmpeg/libavfilter/framequeue.c",
    "//third_party/ijkffmpeg/libavfilter/graphdump.c",
    "//third_party/ijkffmpeg/libavfilter/graphparser.c",
    "//third_party/ijkffmpeg/libavfilter/pthread.c",
    "//third_party/ijkffmpeg/libavfilter/transform.c",
    "//third_party/ijkffmpeg/libavfilter/video.c",
    "//third_party/ijkffmpeg/libavformat/aacdec.c",
    "//third_party/ijkffmpeg/libavformat/allformats.c",
    "//third_party/ijkffmpeg/libavformat/ape.c",
    "//third_party/ijkffmpeg/libavformat/apetag.c",
    "//third_party/ijkffmpeg/libavformat/asf.c",
    "//third_party/ijkffmpeg/libavformat/asfcrypt.c",
    "//third_party/ijkffmpeg/libavformat/asfdec_f.c",
    "//third_party/ijkffmpeg/libavformat/async.c",
    "//third_party/ijkffmpeg/libavformat/avc.c",
    "//third_party/ijkffmpeg/libavformat/avio.c",
    "//third_party/ijkffmpeg/libavformat/aviobuf.c",
    "//third_party/ijkffmpeg/libavformat/avlanguage.c",
    "//third_party/ijkffmpeg/libavformat/concatdec.c",
    "//third_party/ijkffmpeg/libavformat/dump.c",
    "//third_party/ijkffmpeg/libavformat/file.c",
    "//third_party/ijkffmpeg/libavformat/flac_picture.c",
    "//third_party/ijkffmpeg/libavformat/flacdec.c",
    "//third_party/ijkffmpeg/libavformat/flvdec.c",
    "//third_party/ijkffmpeg/libavformat/flvenc.c",
    "//third_party/ijkffmpeg/libavformat/format.c",
    "//third_party/ijkffmpeg/libavformat/hevc.c",
     "//third_party/ijkffmpeg/libavformat/hls.c",
     "//third_party/ijkffmpeg/libavformat/hlsenc.c",
     "//third_party/ijkffmpeg/libavformat/hlsplaylist.c",
     "//third_party/ijkffmpeg/libavformat/hlsproto.c",
     "//third_party/ijkffmpeg/libavformat/http.c",
     "//third_party/ijkffmpeg/libavformat/httpauth.c",
    "//third_party/ijkffmpeg/libavformat/id3v1.c",
    "//third_party/ijkffmpeg/libavformat/id3v2.c",
    "//third_party/ijkffmpeg/libavformat/id3v2enc.c",
    "//third_party/ijkffmpeg/libavformat/img2.c",
    "//third_party/ijkffmpeg/libavformat/isom.c",
    "//third_party/ijkffmpeg/libavformat/metadata.c",
   "//third_party/ijkffmpeg/libavformat/mov.c",
   "//third_party/ijkffmpeg/libavformat/mov_chan.c",
    "//third_party/ijkffmpeg/libavformat/mov_esds.c",
   "//third_party/ijkffmpeg/libavformat/movenc.c",
   "//third_party/ijkffmpeg/libavformat/movenccenc.c",
   "//third_party/ijkffmpeg/libavformat/movenchint.c",
    "//third_party/ijkffmpeg/libavformat/mp3dec.c",
    "//third_party/ijkffmpeg/libavformat/mp3enc.c",
    "//third_party/ijkffmpeg/libavformat/mpegts.c",
    "//third_party/ijkffmpeg/libavformat/mux.c",
    "//third_party/ijkffmpeg/libavformat/network.c",
    "//third_party/ijkffmpeg/libavformat/oggdec.c",
    "//third_party/ijkffmpeg/libavformat/oggparsecelt.c",
    "//third_party/ijkffmpeg/libavformat/oggparsedirac.c",
    "//third_party/ijkffmpeg/libavformat/oggparseflac.c",
    "//third_party/ijkffmpeg/libavformat/oggparseogm.c",
    "//third_party/ijkffmpeg/libavformat/oggparseopus.c",
    "//third_party/ijkffmpeg/libavformat/oggparseskeleton.c",
    "//third_party/ijkffmpeg/libavformat/oggparsespeex.c",
    "//third_party/ijkffmpeg/libavformat/oggparsetheora.c",
    "//third_party/ijkffmpeg/libavformat/oggparsevorbis.c",
    "//third_party/ijkffmpeg/libavformat/oggparsevp8.c",
    "//third_party/ijkffmpeg/libavformat/options.c",
    "//third_party/ijkffmpeg/libavformat/os_support.c",
    "//third_party/ijkffmpeg/libavformat/pcm.c",
    "//third_party/ijkffmpeg/libavformat/protocols.c",
    "//third_party/ijkffmpeg/libavformat/qtpalette.c",
    "//third_party/ijkffmpeg/libavformat/rawdec.c",
    "//third_party/ijkffmpeg/libavformat/rawenc.c",
    "//third_party/ijkffmpeg/libavformat/rawutils.c",
    "//third_party/ijkffmpeg/libavformat/rdt.c",
    "//third_party/ijkffmpeg/libavformat/replaygain.c",
    "//third_party/ijkffmpeg/libavformat/riff.c",
    "//third_party/ijkffmpeg/libavformat/riffdec.c",
    "//third_party/ijkffmpeg/libavformat/riffenc.c",
    "//third_party/ijkffmpeg/libavformat/rm.c",
    "//third_party/ijkffmpeg/libavformat/rmdec.c",
    "//third_party/ijkffmpeg/libavformat/rmsipr.c",
    "//third_party/ijkffmpeg/libavformat/rtmphttp.c",
    "//third_party/ijkffmpeg/libavformat/rtmpproto.c", 
    "//third_party/ijkffmpeg/libavformat/rtmpdigest.c",   
    "//third_party/ijkffmpeg/libavformat/rtmppkt.c",  
    "//third_party/ijkffmpeg/libavformat/rtmpcrypt.c",   
    "//third_party/ijkffmpeg/libavformat/rtmpdh.c",   
    "//third_party/ijkffmpeg/libavformat/rtp.c",
    "//third_party/ijkffmpeg/libavformat/tls_openssl.c",
    "//third_party/ijkffmpeg/libavformat/tls.c", 
    "//third_party/ijkffmpeg/libavformat/ijkutils.c",   
    "//third_party/ijkffmpeg/libavformat/sdp.c",
     "//third_party/ijkffmpeg/libavformat/tcp.c",
    "//third_party/ijkffmpeg/libavformat/udp.c",
    "//third_party/ijkffmpeg/libavformat/url.c",
    "//third_party/ijkffmpeg/libavformat/urldecode.c",
    "//third_party/ijkffmpeg/libavformat/utils.c",
   "//third_party/ijkffmpeg/libavformat/vorbiscomment.c",
    "//third_party/ijkffmpeg/libavformat/vpcc.c",
    "//third_party/ijkffmpeg/libavformat/wavdec.c",
    "//third_party/ijkffmpeg/libavformat/oggparsedaala.c",
    "//third_party/ijkffmpeg/libavformat/rtsp.c",
    "//third_party/ijkffmpeg/libavformat/rtspdec.c",
    "//third_party/ijkffmpeg/libavformat/rtspenc.c",
    "//third_party/ijkffmpeg/libavformat/rtpproto.c",
    "//third_party/ijkffmpeg/libavformat/rtpdec.c", 
    "//third_party/ijkffmpeg/libavformat/rtpdec_ac3.c",
    "//third_party/ijkffmpeg/libavformat/rtpdec_amr.c",
    "//third_party/ijkffmpeg/libavformat/rtpdec_asf.c",
    "//third_party/ijkffmpeg/libavformat/rtpdec_dv.c",
    "//third_party/ijkffmpeg/libavformat/rtpdec_g726.c",
    "//third_party/ijkffmpeg/libavformat/rtpdec_h261.c",
    "//third_party/ijkffmpeg/libavformat/rtpdec_h263.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_h263_rfc2190.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_h264.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_hevc.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_ilbc.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_jpeg.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_latm.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_mpa_robust.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_mpeg12.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_mpeg4.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_mpegts.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_qcelp.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_qdm2.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_qt.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_rfc4175.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_svq3.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_vc2hq.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_vp8.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_vp9.c",
     "//third_party/ijkffmpeg/libavformat/rtpdec_xiph.c",
    "//third_party/ijkffmpeg/libavformat/rtpenc_chain.c",
     "//third_party/ijkffmpeg/libavformat/srtp.c",
    "//third_party/ijkffmpeg/libavutil/adler32.c",
    "//third_party/ijkffmpeg/libavutil/aes.c",
    "//third_party/ijkffmpeg/libavutil/aes_ctr.c",
    "//third_party/ijkffmpeg/libavutil/audio_fifo.c",
    "//third_party/ijkffmpeg/libavutil/avstring.c",
    "//third_party/ijkffmpeg/libavutil/base64.c",
    "//third_party/ijkffmpeg/libavutil/blowfish.c",
    "//third_party/ijkffmpeg/libavutil/bprint.c",
    "//third_party/ijkffmpeg/libavutil/buffer.c",
    "//third_party/ijkffmpeg/libavutil/camellia.c",
    "//third_party/ijkffmpeg/libavutil/cast5.c",
    "//third_party/ijkffmpeg/libavutil/channel_layout.c",
    "//third_party/ijkffmpeg/libavutil/color_utils.c",
    "//third_party/ijkffmpeg/libavutil/cpu.c",
    "//third_party/ijkffmpeg/libavutil/crc.c",
    "//third_party/ijkffmpeg/libavutil/des.c",
    "//third_party/ijkffmpeg/libavutil/dict.c",
    "//third_party/ijkffmpeg/libavutil/display.c",
    "//third_party/ijkffmpeg/libavutil/downmix_info.c",
    "//third_party/ijkffmpeg/libavutil/encryption_info.c",
    "//third_party/ijkffmpeg/libavutil/error.c",
    "//third_party/ijkffmpeg/libavutil/eval.c",
    "//third_party/ijkffmpeg/libavutil/fifo.c",
    "//third_party/ijkffmpeg/libavutil/file.c",
    "//third_party/ijkffmpeg/libavutil/file_open.c",
    "//third_party/ijkffmpeg/libavutil/fixed_dsp.c",
    "//third_party/ijkffmpeg/libavutil/float_dsp.c",
    "//third_party/ijkffmpeg/libavutil/frame.c",
    "//third_party/ijkffmpeg/libavutil/hash.c",
    "//third_party/ijkffmpeg/libavutil/hmac.c",
    "//third_party/ijkffmpeg/libavutil/hwcontext.c",
    "//third_party/ijkffmpeg/libavutil/imgutils.c",
    "//third_party/ijkffmpeg/libavutil/integer.c",
    "//third_party/ijkffmpeg/libavutil/intmath.c",
    "//third_party/ijkffmpeg/libavutil/lfg.c",
    "//third_party/ijkffmpeg/libavutil/lls.c",
    "//third_party/ijkffmpeg/libavutil/log.c",
    "//third_party/ijkffmpeg/libavutil/log2_tab.c",
    "//third_party/ijkffmpeg/libavutil/application.c",
    "//third_party/ijkffmpeg/libavutil/mastering_display_metadata.c",
    "//third_party/ijkffmpeg/libavutil/mathematics.c",
    "//third_party/ijkffmpeg/libavutil/md5.c",
    "//third_party/ijkffmpeg/libavutil/mem.c",
    "//third_party/ijkffmpeg/libavutil/murmur3.c",
    "//third_party/ijkffmpeg/libavutil/opt.c",
    "//third_party/ijkffmpeg/libavutil/parseutils.c",
    "//third_party/ijkffmpeg/libavutil/pixdesc.c",
    "//third_party/ijkffmpeg/libavutil/pixelutils.c",
    "//third_party/ijkffmpeg/libavutil/random_seed.c",
    "//third_party/ijkffmpeg/libavutil/rational.c",
    "//third_party/ijkffmpeg/libavutil/rc4.c",
    "//third_party/ijkffmpeg/libavutil/reverse.c",
    "//third_party/ijkffmpeg/libavutil/ripemd.c",
    "//third_party/ijkffmpeg/libavutil/samplefmt.c",
    "//third_party/ijkffmpeg/libavutil/sha.c",
    "//third_party/ijkffmpeg/libavutil/sha512.c",
    "//third_party/ijkffmpeg/libavutil/slicethread.c",
    "//third_party/ijkffmpeg/libavutil/spherical.c",
    "//third_party/ijkffmpeg/libavutil/stereo3d.c",
    "//third_party/ijkffmpeg/libavutil/tea.c",
    "//third_party/ijkffmpeg/libavutil/threadmessage.c",
    "//third_party/ijkffmpeg/libavutil/time.c",
    "//third_party/ijkffmpeg/libavutil/timecode.c",
    "//third_party/ijkffmpeg/libavutil/tree.c",
    "//third_party/ijkffmpeg/libavutil/twofish.c",
    "//third_party/ijkffmpeg/libavutil/utils.c",
    "//third_party/ijkffmpeg/libavutil/dns_cache.c",    
    "//third_party/ijkffmpeg/libavutil/xtea.c",
    "//third_party/ijkffmpeg/libswresample/audioconvert.c",
    "//third_party/ijkffmpeg/libswresample/dither.c",
    "//third_party/ijkffmpeg/libswresample/options.c",
    "//third_party/ijkffmpeg/libswresample/rematrix.c",
    "//third_party/ijkffmpeg/libswresample/resample.c",
    "//third_party/ijkffmpeg/libswresample/resample_dsp.c",
    "//third_party/ijkffmpeg/libswresample/swresample.c",
    "//third_party/ijkffmpeg/libswresample/swresample_frame.c",
    "//third_party/ijkffmpeg/libswscale/alphablend.c",
    "//third_party/ijkffmpeg/libswscale/gamma.c",
    "//third_party/ijkffmpeg/libswscale/hscale.c",
    "//third_party/ijkffmpeg/libswscale/hscale_fast_bilinear.c",
    "//third_party/ijkffmpeg/libswscale/input.c",
    "//third_party/ijkffmpeg/libswscale/options.c",
    "//third_party/ijkffmpeg/libswscale/output.c",
    "//third_party/ijkffmpeg/libswscale/rgb2rgb.c",
    "//third_party/ijkffmpeg/libswscale/slice.c",
    "//third_party/ijkffmpeg/libswscale/swscale.c",
    "//third_party/ijkffmpeg/libswscale/swscale_unscaled.c",
    "//third_party/ijkffmpeg/libswscale/utils.c",
    "//third_party/ijkffmpeg/libswscale/vscale.c",
    "//third_party/ijkffmpeg/libswscale/yuv2rgb.c",
  ]
  include_dirs = [
    "//third_party/ijkffmpeg/",
    "//third_party/ijkffmpeg/libavformat/",
    "//third_party/ijkffmpeg/libavcodec/",
    "//third_party/ijkffmpeg/libswresample/",
    "//third_party/ijkffmpeg/libavfilter/",
    "//third_party/ijkffmpeg/libswscale/",
    "${target_gen_dir}/include/",
    "//third_party/openssl/include"
  ]
  deps = [ ":gen_config_header" ]
  configs = [ ":ijkffmpeg_config" ]
}

action("gen_config_header") {
  script = "//third_party/ijkffmpeg/ohos_ijkffmpeg_config.sh"

  args = [
    rebase_path("//third_party/ijkffmpeg", root_build_dir),
    rebase_path("${target_gen_dir}/include/", root_build_dir),
  ]

  outputs = [ "${target_gen_dir}/include/config.h" ]
}


ohos_executable("ijkffmpeg") {
  sources = [
    "//third_party/ijkffmpeg/test.cpp",
  ]
  deps = [ "//third_party/ijkffmpeg:biliffmpeg"]
  subsystem_name = "ijkffmpeg"
  part_name = "ijkffmpeg"
  output_name = "ijkffmpeg"
}

ohos_shared_library("biliffmpeg") {
  deps = [ ":ijkffmpeg_dynamic","//third_party/openssl:libcrypto_static", "//third_party/openssl:ssl_source",]
  part_name = "ijkffmpeg"
  output_name = "ijkffmpeg"
}
