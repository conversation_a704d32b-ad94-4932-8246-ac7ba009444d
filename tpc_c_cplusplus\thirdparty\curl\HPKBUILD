# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=curl
pkgver=curl-8_0_1
pkgrel=0
pkgdesc="A command line tool and library for transferring data with URL syntax, supporting DICT, FILE, FTP, FTPS, GOPHER, GOPHERS, HTTP, HTTPS, IMAP, IMAPS, LDAP, LDAPS, MQTT, POP3, POP3S, RTMP, RTMPS, RTSP, SCP, SFTP, SMB, SMBS, SMTP, SMTPS, TELNET, TFTP, WS and WSS. libcurl offers a myriad of powerful features"
url="https://curl.se/"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD/ISC/curl")
depends=("openssl" "zstd" "nghttp2") # QUICHE/WEBSOCKETS
makedepends=()

# 官方下载地址: https://github.com/curl/$pkgname/archive/refs/tags/$pkgver.tar.gz，因网络原因采用gitee mirrors
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"

autounpack=true
downloadpackage=true
patchflag=true

builddir=$pkgname-${pkgver}
packagename=$builddir.zip

prepare() {
    if $patchflag
    then
        cd $builddir
        # 编译识别 OpenHarmony 系统
        patch -p1 < `pwd`/../curl_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DCURL_CA_BUNDLE="/etc/ssl/certs/cacert.pem" -DCURL_CA_PATH="/etc/ssl/certs/" -DCURL_ZSTD=ON -DENABLE_WEBSOCKETS=ON -DUSE_NGHTTP2=ON -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd "$builddir"
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    cd "$builddir"
    make -C $ARCH-build testdeps >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # TODO test 
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
