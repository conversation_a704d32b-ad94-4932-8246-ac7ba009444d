# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# Contributor: <PERSON> <<EMAIL>>, typ <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=libwtf # The source code of the project is WebKit, but only the libwtf library is ported
pkgver=webkitgtk-2.41.90
pkgrel=0
pkgdesc="Home of the WebKit project, the browser engine used by Safari, Mail, App Store and many other applications on macOS, iOS and Linux."
url="https://github.com/WebKit/WebKit"
archs=("arm64-v8a") # armeabi-v7a causes compiler bugs
license=("MIT")
depends=("icu")
makedepends=()

source="https://github.com/WebKit/WebKit/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
patchflag=true

builddir=WebKit-${pkgver}
packagename=$builddir.tar.gz

prepare() {
    if $patchflag
    then
        cd $builddir
        # 添加系统识别
        patch -p1 < `pwd`/../JavaScriptCore_oh_pkg.patch
        # 修改因平台差异导致char类型符号位差异，而引起相关测试用例报错
        patch -p1 < `pwd`/../JavaScriptCore_oh_test.patch

        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -B$ARCH-build -S./ \
        -DCMAKE_CXX_FLAGS="-I$PKGBUILD_ROOT/$builddir/Source/WTF/" -DENABLE_WEBCORE=OFF \
        -DENABLE_WEBKIT=OFF -DPORT="JSCOnly" -DENABLE_STATIC_JSC=ON -DCMAKE_BUILD_TYPE="Release" \
        -DSHOULD_INSTALL_JS_SHELL=OFF > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp $ARCH-build/lib/*.a $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    cp -r $ARCH-build/WTF/Headers/wtf/* $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cd $OLDPWD
}

check() {
    cd $builddir/$ARCH-build/Tools/TestWebKitAPI
    sed -i 's/TestWTF PROPERTIES  TIMEOUT "60"/TestWTF PROPERTIES  TIMEOUT "600"/g' CTestTestfile.cmake
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
