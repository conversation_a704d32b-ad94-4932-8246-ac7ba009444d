# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=sqlcipher
pkgver=v4.5.5
pkgrel=0
pkgdesc="SQLCipher is a standalone fork of the SQLite database library that adds 256 bit AES encryption of database files"
url="https://github.com/sqlcipher/sqlcipher"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD-3-Clause")
depends=("openssl" "tcl")
# 安裝tclsh工具 sudo apt install tcl
makedepends=("tclsh")
source="https://github.com/$pkgname/$pkgname/archive/refs/tags/$pkgver.tar.gz"

downloadpackage=true
autounpack=true
buildtools="configure"

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

source envset.sh
host=

prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
}

build() {
    cd $builddir/$ARCH-build
    PKG_CONFIG_LIBDIR="${pkgconfigpath}" LDFLAGS="$LDFLAGS -L${LYCIUM_ROOT}/usr/openssl/${ARCH}/lib -lcrypto \
        -I${LYCIUM_ROOT}/usr/tcl/${ARCH}/include -L${LYCIUM_ROOT}/usr/tcl/${ARCH}/lib -ltcl8.6" \
        CFLAGS="$CFLAGS -DSQLITE_HAS_CODEC -DSQLCIPHER_TEST -I${LYCIUM_ROOT}/usr/openssl/${ARCH}/include" \
        ../configure "$@" --host=$host --enable-fts5 > $buildlog 2>&1
    $MAKE >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    $MAKE install >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    cd $builddir/$ARCH-build
    $MAKE testfixture >> $buildlog 2>&1
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # cd tpc_c_cplusplus/thirdparty/sqlcipher/sqlcipher-4.5.5/arm64-v8a-build/
    # ./testfixture ../test/sqlcipher.test
    ret=$?
    cd $OLDPWD
    return $ret
}

recoverpkgbuildenv() {
    unset host
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    else
        echo "${ARCH} not support"
        return -1
    fi
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}$builddir # ${PWD}/$packagename
}
