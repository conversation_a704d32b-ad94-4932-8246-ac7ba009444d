# Contributor: wuping<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=libxlsxwriter
pkgver=RELEASE_1.1.5
pkgrel=0
pkgdesc="Libxlsxwriter is a C library that can be used to write text, numbers, formulas and hyperlinks to multiple worksheets in an Excel 2007+ XLSX file."
url="https://github.com/jmcnamara/libxlsxwriter"
archs=("armeabi-v7a" "arm64-v8a")
license=("FreeBSD")
depends=()
makedepends=()
install=
source="https://github.com/jmcnamara/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DBUILD_TESTS=ON -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 在OpenHarmony开发板中执行用例
    # ctest
}

cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
