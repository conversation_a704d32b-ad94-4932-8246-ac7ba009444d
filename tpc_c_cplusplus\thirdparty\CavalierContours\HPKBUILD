# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=CavalierContours 
pkgver=master 
pkgrel=0 
pkgdesc="A 2D multi-fold line library for offset, cross-merging, and other combinations"
url="https://github.com/jbuckmccready/CavalierContours" 
archs=("armeabi-v7a" "arm64-v8a") # cpu 架构
license=("MIT License")
source="https://github.com/jbuckmccready/$pkgname.git" 
commitid=7a35376eb4c2d5f917d3e0564ea630c94137255e

patchflag=true
autounpack=false
downloadpackage=false

builddir=$pkgname-${commitid}
packagename=
cloneflag=true

prepare() {
    if [ $cloneflag == true ]
    then
        mkdir $builddir
        git clone -b $pkgver $source $builddir
        if [ $? -ne 0 ]
        then
            return -1
        fi
        cd $builddir
        git reset --hard $commitid
        if [ $? -ne 0 ]
        then
            return -2
        fi
        cd $OLDPWD
        cloneflag=false
    fi
    if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../CavalierContours_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DCMAKE_C_FLAGS=-Wno-unused-command-line-argument -DCMAKE_CXX_FLAGS=-Wno-unused-command-line-argument -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

# 安装打包
package() {
    cd $builddir
    # 需要手动拷贝头文件
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    cp -r include $LYCIUM_ROOT/usr/$pkgname/$ARCH/
    cp -r $ARCH-build/libCavalierContours.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    cd $OLDPWD
}

# 测试，需要在 ohos 设备上进行
check() {
    echo "The test must be on an OpenHarmony device!"
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}