diff -rupN mxnet/CMakeLists.txt mxnet_patch/CMakeLists.txt
--- mxnet/CMakeLists.txt        2023-08-24 17:18:50.431851699 +0800
+++ mxnet_patch/CMakeLists.txt  2023-08-24 18:28:26.303128761 +0800
@@ -7,7 +7,7 @@ if(CMAKE_CROSSCOMPILING)
 endif()
 
 project(mxnet C CXX)
-set(CMAKE_CXX_STANDARD 11)
+set(CMAKE_CXX_STANDARD 17)
 set(CMAKE_CXX_STANDARD_REQUIRED ON)
 set(CMAKE_CXX_EXTENSIONS ON)
 
@@ -541,6 +541,11 @@ if(NOT USE_INTGEMM)
   list(REMOVE_ITEM SOURCE ${INTGEMM_OPERATOR_SOURCE})
 endif()
 
+if(NOT USE_MKLDNN)
+  FILE(GLOB_RECURSE MKLDNN_SOURCE "include/mkldnn/*")
+  list(REMOVE_ITEM SOURCE ${MKLDNN_SOURCE})
+endif()
+
 # add nnvm to source
 FILE(GLOB_RECURSE NNVMSOURCE
   3rdparty/tvm/nnvm/src/c_api/*.cc
@@ -962,7 +967,9 @@ install(DIRECTORY ${CMAKE_CURRENT_SOURCE
 install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/dmlc-core/include/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
 install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/mshadow/mshadow/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/mshadow)
 install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/mxnet/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/mxnet)
+# if(USE_TVM_OP)
 install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/tvm/nnvm/include/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
+# endif()
 if (INSTALL_EXAMPLES)
   install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/example  DESTINATION ${CMAKE_INSTALL_DATADIR}/${PROJECT_NAME})
 endif()

diff -rupN mxnet/src/operator/random/shuffle_op.cc mxnet_patch/src/operator/random/shuffle_op.cc
--- mxnet/src/operator/random/shuffle_op.cc     2023-08-24 17:18:51.859726742 +0800
+++ mxnet_patch/src/operator/random/shuffle_op.cc       2023-08-24 18:28:26.303128761 +0800
@@ -22,7 +22,7 @@
  * \brief Operator to shuffle elements of an NDArray
  */
 #if ((__GNUC__ > 4 && !defined(__clang__major__)) || (__clang_major__ > 4 && __linux__)) && \
-  defined(_OPENMP) && !defined(__ANDROID__)
+  defined(_OPENMP) && !defined(__ANDROID__) && !defined(__OHOS__)
 #define USE_GNU_PARALLEL_SHUFFLE
 #endif
 
diff -rupN mxnet/src/storage/cpu_shared_storage_manager.h mxnet_patch/src/storage/cpu_shared_storage_manager.h
--- mxnet/src/storage/cpu_shared_storage_manager.h      2023-08-24 17:18:51.803731638 +0800
+++ mxnet_patch/src/storage/cpu_shared_storage_manager.h        2023-08-24 18:28:26.303128761 +0800
@@ -160,6 +160,7 @@ void CPUSharedStorageManager::Alloc(Stor
       << "Failed to map shared memory. MapViewOfFile failed with error " << GetLastError();
   map_handle_map_[ptr] = map_handle;
 #else
+#ifndef __OHOS__
   if (handle->shared_id == -1 && handle->shared_pid == -1) {
     is_new = true;
     handle->shared_pid = getpid();
@@ -204,6 +205,7 @@ void CPUSharedStorageManager::Alloc(Stor
       << "Failed to close shared memory. close failed with error "
       << strerror(errno);
 #endif  // __linux__
+#endif // __OHOS__
 #endif  // _WIN32
 
   if (is_new) {
@@ -219,6 +221,7 @@ void CPUSharedStorageManager::FreeImpl(c
 #ifdef _WIN32
   is_free_[handle.dptr] = handle;
 #else
+#ifndef __OHOS__
   CHECK_EQ(munmap(static_cast<char*>(handle.dptr) - alignment_,
                   handle.size + alignment_), 0)
       << "Failed to unmap shared memory. munmap failed with error "
@@ -238,6 +241,7 @@ void CPUSharedStorageManager::FreeImpl(c
         << strerror(errno);
   }
 #endif  // __linux__
+#endif  // __OHOS__
 #endif  // _WIN32
 }