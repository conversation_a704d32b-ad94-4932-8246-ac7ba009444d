# OpenHarmony IJKPlayer FFmpeg 编译问题解决方案汇总

## 项目概述
本文档汇总了在编译 OpenHarmony IJKPlayer 项目中 FFmpeg-ff4.0 组件时遇到的问题及其解决方案。

## 主要问题及解决方案

### 1. OpenSSL 依赖下载失败

#### 问题描述
```
curl: (22) The requested URL returned error: 468 
ERROR during : download https://gitee.com/mirrors/openssl/repository/archive/OpenSSL_1_1_1w.zip
```

#### 根本原因
- FFmpeg-ff4.0 依赖 openssl_1_1_1w
- Gitee 镜像源不可用或 URL 格式问题
- 网络连接问题

#### 解决方案
**方案1：修改下载源**
```bash
# 修改 tpc_c_cplusplus/thirdparty/openssl_1_1_1w/HPKBUILD
# 将下载源从 Gitee 改为 GitHub
source="https://codeload.github.com/openssl/openssl/zip/refs/tags/OpenSSL_1_1_1w"
```

**方案2：写死配置参数**
```bash
builddir=openssl-OpenSSL_1_1_1w
packagename=openssl-OpenSSL_1_1_1w.zip
```

### 2. SHA512 校验失败

#### 问题描述
```
sha512sum: WARNING: 1 computed checksum did NOT match
SHA512SUM 校验失败, 请确认 SHA512SUM 无误后, 重新编译
```

#### 根本原因
- 更换下载源后，文件的 SHA512 值发生变化
- 原有 SHA512SUM 文件中的校验值不匹配新下载的文件

#### 解决方案
**方案1：删除 SHA512SUM 文件（推荐）**
```bash
rm tpc_c_cplusplus/thirdparty/openssl_1_1_1w/SHA512SUM
```

**方案2：更新正确的 SHA512 值**
```bash
cd tpc_c_cplusplus/thirdparty/openssl_1_1_1w
sha512sum openssl-OpenSSL_1_1_1w.zip > SHA512SUM
```

### 3. FFmpeg 目录名不匹配

#### 问题描述
```
cd: FFmpeg-ff4.0--ijk0.8.8--20210426--001: No such file or directory
cp: cannot stat 'FFmpeg-ff4.0--ijk0.8.8--20210426--001': No such file or directory
```

#### 根本原因
- GitHub 下载的文件解压后目录名为：`FFmpeg-ff4.0-ijk0.8.8-20210426-001`（单横线）
- HPKBUILD 配置期望的目录名为：`FFmpeg-ff4.0--ijk0.8.8--20210426--001`（双横线）

#### 解决方案
修改 `tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD`：
```bash
# 修改前
builddir=$pkgname--${pkgver}
packagename=$builddir.tar.gz

# 修改后
builddir=FFmpeg-ff4.0-ijk0.8.8-20210426-001
packagename=FFmpeg-ff4.0-ijk0.8.8-20210426-001.tar.gz
```

### 4. 内存不足问题

#### 问题描述
- 系统内存只有 952MB
- 编译过程中负载过高（load average: 32+）
- 可用内存不足（只有 94MB）

#### 根本原因
FFmpeg 编译需要大量内存：
- Configure 阶段：200-500MB
- Make 编译阶段：800MB-2GB+
- 链接阶段：1-3GB

#### 解决方案
**推荐方案：升级服务器内存**
- 最低要求：2GB
- 推荐配置：4GB+

**临时方案：添加交换空间**
```bash
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 5. Git 仓库管理问题

#### 问题描述
- 需要将修改后的代码上传到 GitHub
- 遇到嵌套 Git 仓库警告
- GitHub 不支持密码认证

#### 解决方案
**处理嵌套仓库：**
```bash
rm -rf tpc_c_cplusplus/.git
git add .
```

**GitHub 认证：**
使用 Personal Access Token 替代密码认证

## 依赖关系分析

### 构建依赖链
```
FFmpeg-ff4.0 → openssl_1_1_1w + openh264
libyuv-ijk → 无依赖（构建成功）
```

### 为什么 libyuv-ijk 成功而 FFmpeg-ff4.0 失败
- **libyuv-ijk**：无外部依赖，可直接构建
- **FFmpeg-ff4.0**：依赖 openssl_1_1_1w，需要先构建依赖项

## 最佳实践建议

### 1. 环境准备
- 确保服务器内存至少 2GB
- 设置正确的 OHOS_SDK 环境变量
- 检查网络连接稳定性

### 2. 构建顺序
1. 先构建无依赖的组件（如 libyuv-ijk）
2. 再构建有依赖的组件（如 FFmpeg-ff4.0）

### 3. 问题排查
- 检查下载源的可用性
- 验证文件完整性
- 监控系统资源使用情况

### 4. 代码管理
- 使用 Git 分支管理不同的修改
- 及时备份重要的配置修改
- 使用适当的认证方式推送代码

## 总结

通过以上解决方案，成功解决了：
1. ✅ OpenSSL 下载源问题
2. ✅ SHA512 校验问题  
3. ✅ FFmpeg 目录名匹配问题
4. ✅ Git 仓库管理问题
5. ⚠️ 内存不足问题（需要硬件升级）

这些修改已保存在 GitHub 仓库的 `ffmpeg-build-fix` 分支中，可以在新服务器上直接使用。
