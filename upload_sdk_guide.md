# OpenHarmony SDK 上传指导

## 您的SDK路径
本地SDK路径: `D:\harmonyFor\openSDK\11`

## 上传方案

### 方案1：直接上传（推荐）

在Windows命令行或PowerShell中执行：

```bash
# 替换 username 和 server_ip 为您的实际信息
scp -r "D:\harmonyFor\openSDK\11" username@server_ip:/home/<USER>/ohos_ijkplayer-2.0.3/../ohos-sdk-11/linux/

# 示例（请替换实际值）：
# scp -r "D:\harmonyFor\openSDK\11" ubuntu@*************:/home/<USER>/ohos_ijkplayer-2.0.3/../ohos-sdk-11/linux/
```

### 方案2：压缩后上传（网络不稳定时）

```powershell
# 1. 在Windows PowerShell中压缩SDK
Compress-Archive -Path "D:\harmonyFor\openSDK\11" -DestinationPath "D:\harmonyFor\ohos-sdk-11.zip"

# 2. 上传压缩包
scp "D:\harmonyFor\ohos-sdk-11.zip" username@server_ip:/home/<USER>/ohos_ijkplayer-2.0.3/../

# 3. 在服务器上解压
ssh username@server_ip
cd /home/<USER>/ohos_ijkplayer-2.0.3/../
unzip ohos-sdk-11.zip
mkdir -p ohos-sdk-11/linux/
mv 11 ohos-sdk-11/linux/
rm ohos-sdk-11.zip
```

### 方案3：使用WinSCP图形界面

1. 下载安装 WinSCP: https://winscp.net/
2. 连接到Ubuntu服务器
3. 在服务器端创建目录: `/home/<USER>/ohos_ijkplayer-2.0.3/../ohos-sdk-11/linux/`
4. 将本地 `D:\harmonyFor\openSDK\11` 拖拽到服务器目录

## 上传后验证

登录服务器后执行：

```bash
# 检查SDK是否上传成功
ls -la ../ohos-sdk-11/linux/11/

# 检查关键文件
ls -la ../ohos-sdk-11/linux/11/native/llvm/bin/clang

# 运行SDK配置脚本
./setup_sdk.sh
```

## 预期的目录结构

上传成功后，服务器上应该有以下结构：

```
ohos_ijkplayer-2.0.3/
├── ...项目文件...
└── ../ohos-sdk-11/
    └── linux/
        └── 11/
            ├── native/
            │   └── llvm/
            │       └── bin/
            │           ├── clang
            │           ├── clang++
            │           └── ...
            ├── toolchains/
            └── ...其他SDK文件...
```

## 常见问题

### 1. 权限被拒绝
```bash
# 确保有SSH访问权限
ssh-keygen -t rsa  # 生成SSH密钥（如果没有）
ssh-copy-id username@server_ip  # 复制公钥到服务器
```

### 2. 目录不存在
```bash
# 在服务器上创建目录
ssh username@server_ip "mkdir -p /home/<USER>/ohos_ijkplayer-2.0.3/../ohos-sdk-11/linux/"
```

### 3. 上传中断
使用rsync支持断点续传：
```bash
# 在Windows上安装rsync（通过WSL或Git Bash）
rsync -avz --progress "D:\harmonyFor\openSDK\11/" username@server_ip:/home/<USER>/ohos_ijkplayer-2.0.3/../ohos-sdk-11/linux/11/
```

## 下一步

上传完成后：
1. 运行 `./setup_sdk.sh` 配置SDK路径
2. 执行 `./prebuild.sh` 开始编译依赖库
