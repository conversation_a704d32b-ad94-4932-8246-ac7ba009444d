# OpenHarmony ijkplayer 依赖库编译指导 (Windows环境)

## 概述
本文档提供在Windows环境下为OpenHarmony ijkplayer项目编译所需依赖库的详细指导。

## 所需依赖库
1. **FFmpeg** - 基于B站的FFmpeg版本(ff4.0--ijk0.8.8--20210426--001)
2. **soundtouch** - 基于B站的soundtouch版本(ijk-r0.1.2-dev)  
3. **libyuv** - 基于B站的libyuv版本(ijk-r0.2.1-dev)
4. **openssl** - FFmpeg的依赖库

## 方案选择

### 方案1：使用预编译库（推荐）
由于交叉编译的复杂性，建议直接使用预编译的库文件。

#### 下载链接
- FFmpeg: https://github.com/bilibili/FFmpeg/releases/tag/ff4.0--ijk0.8.8--20210426--001
- soundtouch: https://github.com/bilibili/soundtouch/tree/ijk-r0.1.2-dev
- libyuv: https://github.com/bilibili/libyuv/tree/ijk-r0.2.1-dev

### 方案2：Linux环境编译（完整方案）
如果需要完整的编译过程，建议使用Linux环境。

#### 环境要求
- Ubuntu 18.04+ 或其他Linux发行版
- OpenHarmony SDK
- 交叉编译工具链

## Windows环境快速解决方案

### 步骤1：创建目录结构
```cmd
mkdir ijkplayer\src\main\cpp\third_party\ffmpeg\ffmpeg
mkdir ijkplayer\src\main\cpp\third_party\soundtouch
mkdir ijkplayer\src\main\cpp\third_party\yuv
mkdir ijkplayer\src\main\cpp\third_party\openssl
```

### 步骤2：下载依赖源码
使用Git下载各个依赖库的源码：

```cmd
# 下载FFmpeg
git clone https://github.com/bilibili/FFmpeg.git -b ff4.0--ijk0.8.8--20210426--001 temp_ffmpeg

# 下载soundtouch
git clone https://github.com/bilibili/soundtouch.git -b ijk-r0.1.2-dev temp_soundtouch

# 下载libyuv
git clone https://github.com/bilibili/libyuv.git -b ijk-r0.2.1-dev temp_libyuv
```

### 步骤3：配置编译环境
1. 安装OpenHarmony SDK
2. 设置环境变量：
   ```cmd
   set OHOS_SDK=D:\path\to\ohos-sdk\windows\11
   ```

### 步骤4：使用Linux子系统编译（推荐）
如果有WSL或虚拟机：

```bash
# 在Linux环境中执行
cd /mnt/d/new/ohos_ijkplayer-2.0.3
chmod +x prebuild.sh
./prebuild.sh
```

## 手动配置方案

如果无法使用自动编译脚本，可以手动配置：

### FFmpeg配置
1. 复制FFmpeg源码到 `ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/`
2. 确保包含以下目录结构：
   - include/ (头文件)
   - lib/ (库文件)

### soundtouch配置  
1. 复制soundtouch源码到 `ijkplayer/src/main/cpp/third_party/soundtouch/`
2. 确保包含：
   - include/
   - lib/

### libyuv配置
1. 复制libyuv源码到 `ijkplayer/src/main/cpp/third_party/yuv/`
2. 确保包含：
   - include/
   - lib/

## 验证安装
检查以下文件是否存在：
- `ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/include/`
- `ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/lib/`
- `ijkplayer/src/main/cpp/third_party/soundtouch/include/`
- `ijkplayer/src/main/cpp/third_party/soundtouch/lib/`
- `ijkplayer/src/main/cpp/third_party/yuv/include/`
- `ijkplayer/src/main/cpp/third_party/yuv/lib/`
- `ijkplayer/src/main/cpp/third_party/openssl/include/`
- `ijkplayer/src/main/cpp/third_party/openssl/lib/`

## 注意事项
1. 确保所有库文件都是为OpenHarmony平台编译的
2. 检查架构兼容性（arm64-v8a, armeabi-v7a等）
3. 验证头文件路径在CMakeLists.txt中正确配置

## 下一步
完成依赖库配置后，可以使用DevEco Studio编译整个项目。
