# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1    # 导入HPKBUILD文件
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

# 在OH环境执行测试的接口
openharmonycheck() {
    res=1
    cd ${builddir}/${ARCH}-build

    # OH环境测试结果与原库在linux测试结果进行比对，实现自动化测试
    # 原库在linux测试结果：nFrames decoded: 75 (316x240 @ 947.57 fps)
    # 其中帧率值受硬件条件影响，非固定值，不进行对比
    result=$(./dec265/dec265 ../testdata/girlshy.h265 2>&1)
    echo $result > ${logfile} 2>&1
    result1=`echo $result|awk -F '@' '{print $1}'`
    if [ "$result1" == "nFrames decoded: 75 (316x240 " ];then
        res=0
    fi
    cd $OLDPWD

    return $res
}