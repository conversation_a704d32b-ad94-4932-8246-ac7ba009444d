diff -rupN libuv-1.44.2/CMakeLists.txt libuv-1.44.2-patched/CMakeLists.txt
--- libuv-1.44.2/CMakeLists.txt	2022-07-12 16:16:33.000000000 +0000
+++ libuv-1.44.2-patched/CMakeLists.txt	2023-04-19 08:14:47.240275531 +0000
@@ -227,7 +227,7 @@ if(CMAKE_SYSTEM_NAME STREQUAL "Android")
        src/unix/epoll.c)
 endif()
 
-if(APPLE OR CMAKE_SYSTEM_NAME MATCHES "Android|Linux")
+if(APPLE OR CMAKE_SYSTEM_NAME MATCHES "Android|Linux|OHOS")
   list(APPEND uv_sources src/unix/proctitle.c)
 endif()
 
@@ -275,7 +275,7 @@ if(CMAKE_SYSTEM_NAME STREQUAL "kFreeBSD"
   list(APPEND uv_libraries dl freebsd-glue)
 endif()
 
-if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
+if(CMAKE_SYSTEM_NAME MATCHES "Linux|OHOS")
   list(APPEND uv_defines _GNU_SOURCE _POSIX_C_SOURCE=200112)
   list(APPEND uv_libraries dl rt)
   list(APPEND uv_sources
@@ -384,7 +384,7 @@ if(CMAKE_SYSTEM_NAME STREQUAL "QNX")
   list(APPEND uv_libraries socket)
 endif()
 
-if(APPLE OR CMAKE_SYSTEM_NAME MATCHES "DragonFly|FreeBSD|Linux|NetBSD|OpenBSD")
+if(APPLE OR CMAKE_SYSTEM_NAME MATCHES "DragonFly|FreeBSD|Linux|NetBSD|OpenBSD|OHOS")
   list(APPEND uv_test_libraries util)
 endif()
 
