# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>
pkgname=xxHash
pkgver=v0.8.1
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD")
depends=()
makedepends=()

source="https://github.com/Cyan4973/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=false
downloadpackage=true
buildtools="make"

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

source envset.sh

prepare() {
    mkdir $pkgname-$ARCH-build
    tar -zxf $packagename -C $pkgname-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
    fi
}

build() {
    cd $pkgname-$ARCH-build/$builddir
    make -j4 > `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $pkgname-$ARCH-build/$builddir
    make install PREFIX="" DESTDIR=$LYCIUM_ROOT/usr/$pkgname/$ARCH >> `pwd`/build.log 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
}

check() {
    echo "The test must be on an OpenHarmony device!"
    #拷贝到开发板运行：
    #cd xxHash-0.8.1/tests
    #/bin/sh ./unicode_lint.sh
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir $pkgname-armeabi-v7a-build  $pkgname-arm64-v8a-build #${PWD}/$packagename
}
