[{"Name": "geos", "License": "LGPL v2.1", "License File": "COPYING", "Version Number": "LGPL v2.1", "Owner": "<EMAIL>", "Upstream URL": "https://github.com/libgeos/geos/archive/refs/tags/3.11.2.tar.gz", "Description": "GEOS is a C++ library for performing operations on two-dimensional vector geometries. It is primarily a port of the JTS Topology Suite Java library. It provides many of the algorithms used by PostGIS, the Shapely package for Python, the sf package for R, and others."}]