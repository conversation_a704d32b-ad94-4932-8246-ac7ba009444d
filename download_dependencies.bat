@echo off
REM OpenHarmony ijkplayer 依赖库编译脚本 (Windows版本)
REM 基于prebuild.sh脚本改写，适用于Windows环境
REM 此脚本模拟Linux版本的功能，但需要手动处理一些步骤

echo ========================================
echo OpenHarmony ijkplayer 依赖库编译工具 (Windows版)
echo ========================================
echo 基于原始prebuild.sh脚本改写
echo ========================================

setlocal enabledelayedexpansion

REM 配置变量 (对应prebuild.sh中的变量)
set ROOT_DIR=%~dp0
set API_VERSION=11
set SDK_DIR=%ROOT_DIR%..\ohos-sdk-%API_VERSION%\windows\%API_VERSION%
set LYCIUM_TOOLS_URL=https://gitee.com/openharmony-sig/tpc_c_cplusplus.git
set LYCIUM_ROOT_DIR=%ROOT_DIR%tpc_c_cplusplus
set LYCIUM_TOOLS_DIR=%LYCIUM_ROOT_DIR%\lycium
set LYCIUM_THIRDPARTY_DIR=%LYCIUM_ROOT_DIR%\thirdparty
set DEPENDS_DIR=%ROOT_DIR%doc
set FFMPEG_NAME=FFmpeg-ff4.0
set LIBYUV_NAME=libyuv-ijk
set SOUNDTOUCH_NAME=soundtouch-ijk
set OPENSSL_NAME=openssl_1_1_1w
set TEMP_DIR=%ROOT_DIR%temp_downloads
set TARGET_DIR=%ROOT_DIR%ijkplayer\src\main\cpp\third_party

echo 当前目录: %ROOT_DIR%
echo SDK目录: %SDK_DIR%
echo 临时下载目录: %TEMP_DIR%
echo 目标目录: %TARGET_DIR%
echo.

REM 检查Git是否可用
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Git，请先安装Git
    pause
    exit /b 1
)

REM ========================================
REM 函数：检查SDK (对应check_sdk函数)
REM ========================================
:check_sdk
echo 检查OpenHarmony SDK...
if not exist "%SDK_DIR%" (
    echo 警告: 未找到OpenHarmony SDK目录: %SDK_DIR%
    echo 请确保已安装OpenHarmony SDK并设置正确的路径
    echo 您可以手动设置OHOS_SDK环境变量
    set /p user_sdk_path="请输入SDK路径 (或按回车跳过): "
    if not "!user_sdk_path!"=="" (
        set SDK_DIR=!user_sdk_path!
        set OHOS_SDK=!user_sdk_path!
    )
) else (
    echo 找到SDK目录: %SDK_DIR%
    set OHOS_SDK=%SDK_DIR%
)
goto :eof

REM ========================================
REM 函数：准备lycium工具 (对应prepare_lycium函数)
REM ========================================
:prepare_lycium
echo 准备lycium编译工具...
if exist "%LYCIUM_ROOT_DIR%" (
    echo 清理旧的lycium目录...
    rmdir /s /q "%LYCIUM_ROOT_DIR%"
)

echo 下载lycium工具链...
git clone %LYCIUM_TOOLS_URL% -b support_x86 --depth=1 "%LYCIUM_ROOT_DIR%"
if %errorlevel% neq 0 (
    echo 错误: lycium工具链下载失败
    goto :error
)

echo lycium工具链下载完成
goto :eof

REM ========================================
REM 函数：创建目录结构
REM ========================================
:create_directories
echo 创建目录结构...
if exist "%TEMP_DIR%" (
    echo 清理旧的临时目录...
    rmdir /s /q "%TEMP_DIR%"
)
mkdir "%TEMP_DIR%"

REM 创建目标目录结构
echo 创建目标目录结构...
if not exist "%TARGET_DIR%" mkdir "%TARGET_DIR%"
if not exist "%TARGET_DIR%\ffmpeg\ffmpeg" mkdir "%TARGET_DIR%\ffmpeg\ffmpeg"
if not exist "%TARGET_DIR%\soundtouch" mkdir "%TARGET_DIR%\soundtouch"
if not exist "%TARGET_DIR%\yuv" mkdir "%TARGET_DIR%\yuv"
if not exist "%TARGET_DIR%\openssl" mkdir "%TARGET_DIR%\openssl"
goto :eof

REM ========================================
REM 函数：下载依赖源码 (对应原脚本的下载部分)
REM ========================================
:download_dependencies
cd /d "%TEMP_DIR%"

echo.
echo ========================================
echo 1. 下载FFmpeg源码...
echo ========================================
git clone https://github.com/bilibili/FFmpeg.git -b ff4.0--ijk0.8.8--20210426--001 --depth=1 ffmpeg_src
if %errorlevel% neq 0 (
    echo 警告: FFmpeg下载失败，请检查网络连接
    set FFMPEG_DOWNLOAD_FAILED=1
) else (
    echo FFmpeg源码下载完成
    set FFMPEG_DOWNLOAD_FAILED=0
)

echo.
echo ========================================
echo 2. 下载soundtouch源码...
echo ========================================
git clone https://github.com/bilibili/soundtouch.git -b ijk-r0.1.2-dev --depth=1 soundtouch_src
if %errorlevel% neq 0 (
    echo 警告: soundtouch下载失败，请检查网络连接
    set SOUNDTOUCH_DOWNLOAD_FAILED=1
) else (
    echo soundtouch源码下载完成
    set SOUNDTOUCH_DOWNLOAD_FAILED=0
)

echo.
echo ========================================
echo 3. 下载libyuv源码...
echo ========================================
git clone https://github.com/bilibili/libyuv.git -b ijk-r0.2.1-dev --depth=1 libyuv_src
if %errorlevel% neq 0 (
    echo 警告: libyuv下载失败，请检查网络连接
    set LIBYUV_DOWNLOAD_FAILED=1
) else (
    echo libyuv源码下载完成
    set LIBYUV_DOWNLOAD_FAILED=0
)

echo.
echo ========================================
echo 4. 下载OpenSSL源码...
echo ========================================
git clone https://github.com/openssl/openssl.git -b OpenSSL_1_1_1w --depth=1 openssl_src
if %errorlevel% neq 0 (
    echo 警告: OpenSSL下载失败，请检查网络连接
    set OPENSSL_DOWNLOAD_FAILED=1
) else (
    echo OpenSSL源码下载完成
    set OPENSSL_DOWNLOAD_FAILED=0
)
goto :eof

REM ========================================
REM 函数：复制依赖配置 (对应prepare_depends函数)
REM ========================================
:prepare_depends
echo 复制依赖配置文件...

REM 复制libyuv-ijk配置到lycium thirdparty目录
if exist "%DEPENDS_DIR%\%LIBYUV_NAME%" (
    if exist "%LYCIUM_THIRDPARTY_DIR%\%LIBYUV_NAME%" (
        rmdir /s /q "%LYCIUM_THIRDPARTY_DIR%\%LIBYUV_NAME%"
    )
    xcopy /s /e /y "%DEPENDS_DIR%\%LIBYUV_NAME%" "%LYCIUM_THIRDPARTY_DIR%\%LIBYUV_NAME%\"
    echo libyuv配置复制完成
)

REM 复制soundtouch-ijk配置到lycium thirdparty目录
if exist "%DEPENDS_DIR%\%SOUNDTOUCH_NAME%" (
    if exist "%LYCIUM_THIRDPARTY_DIR%\%SOUNDTOUCH_NAME%" (
        rmdir /s /q "%LYCIUM_THIRDPARTY_DIR%\%SOUNDTOUCH_NAME%"
    )
    xcopy /s /e /y "%DEPENDS_DIR%\%SOUNDTOUCH_NAME%" "%LYCIUM_THIRDPARTY_DIR%\%SOUNDTOUCH_NAME%\"
    echo soundtouch配置复制完成
)
goto :eof

REM ========================================
REM 函数：复制源码到目标目录 (对应install_depends函数)
REM ========================================
:copy_sources_to_target
echo.
echo ========================================
echo 复制源码到目标目录...
echo ========================================

REM 复制FFmpeg源码（保留现有的config.h）
if exist "ffmpeg_src" (
    if %FFMPEG_DOWNLOAD_FAILED%==0 (
        echo 复制FFmpeg源码...
        xcopy /s /e /y "ffmpeg_src\*" "%TARGET_DIR%\ffmpeg\ffmpeg\"
        echo FFmpeg源码复制完成
    )
)

REM 复制soundtouch源码
if exist "soundtouch_src" (
    if %SOUNDTOUCH_DOWNLOAD_FAILED%==0 (
        echo 复制soundtouch源码...
        xcopy /s /e /y "soundtouch_src\*" "%TARGET_DIR%\soundtouch\"
        echo soundtouch源码复制完成
    )
)

REM 复制libyuv源码
if exist "libyuv_src" (
    if %LIBYUV_DOWNLOAD_FAILED%==0 (
        echo 复制libyuv源码...
        xcopy /s /e /y "libyuv_src\*" "%TARGET_DIR%\yuv\"
        echo libyuv源码复制完成
    )
)

REM 复制OpenSSL源码
if exist "openssl_src" (
    if %OPENSSL_DOWNLOAD_FAILED%==0 (
        echo 复制OpenSSL源码...
        xcopy /s /e /y "openssl_src\*" "%TARGET_DIR%\openssl\"
        echo OpenSSL源码复制完成
    )
)
goto :eof

REM ========================================
REM 主程序入口 (对应prebuild函数)
REM ========================================
:main
echo 开始执行依赖库编译流程...

REM 1. 检查SDK
call :check_sdk

REM 2. 创建目录结构
call :create_directories

REM 3. 准备lycium工具 (仅在Linux环境需要)
echo.
echo ========================================
echo 注意：lycium工具链需要Linux环境
echo ========================================
echo 在Windows环境下，我们将跳过lycium工具链的安装
echo 如需完整编译，请在Linux环境下运行原始的prebuild.sh脚本
echo.

REM 4. 下载依赖源码
call :download_dependencies

REM 5. 复制源码到目标目录
call :copy_sources_to_target

REM 6. 清理临时文件
echo.
echo ========================================
echo 清理临时文件...
echo ========================================
cd /d "%ROOT_DIR%"
if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%"
    echo 临时文件清理完成
)

REM 7. 显示结果
call :show_results
goto :end

REM ========================================
REM 函数：显示结果
REM ========================================
:show_results
echo.
echo ========================================
echo 源码下载完成！
echo ========================================
echo.
echo 源码已下载到以下目录：
if %FFMPEG_DOWNLOAD_FAILED%==0 echo - FFmpeg: %TARGET_DIR%\ffmpeg\ffmpeg\
if %SOUNDTOUCH_DOWNLOAD_FAILED%==0 echo - soundtouch: %TARGET_DIR%\soundtouch\
if %LIBYUV_DOWNLOAD_FAILED%==0 echo - libyuv: %TARGET_DIR%\yuv\
if %OPENSSL_DOWNLOAD_FAILED%==0 echo - OpenSSL: %TARGET_DIR%\openssl\
echo.
echo ========================================
echo 重要提示：
echo ========================================
echo 1. 这些是源码文件，还需要进行交叉编译才能在OpenHarmony上使用
echo 2. 完整的编译需要Linux环境和OpenHarmony交叉编译工具链
echo 3. 建议使用以下方法之一进行编译：
echo    - 在Linux环境下运行原始的 prebuild.sh 脚本
echo    - 使用WSL (Windows Subsystem for Linux)
echo    - 使用Docker容器
echo    - 使用虚拟机
echo.
echo 4. 如果您有Linux环境，请执行以下命令：
echo    chmod +x prebuild.sh
echo    ./prebuild.sh
echo.
echo 5. 编译完成后，库文件将自动复制到正确的目录结构中
echo.
goto :eof

REM ========================================
REM 错误处理
REM ========================================
:error
echo.
echo ========================================
echo 发生错误，脚本执行失败！
echo ========================================
echo 请检查网络连接和权限设置
pause
exit /b 1

REM ========================================
REM 正常结束
REM ========================================
:end
echo 脚本执行完成！
echo.
pause
exit /b 0

REM ========================================
REM 脚本入口点
REM ========================================
call :main
