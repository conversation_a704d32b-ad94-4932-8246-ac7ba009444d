# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=openmp
pkgver=release_90
pkgrel=0
pkgdesc="openmp is a compoment of llvm-project."
url="https://github.com/llvm-mirror/openmp"
archs=("armeabi-v7a" "arm64-v8a")
license=("View license")
depends=()
makedepends=()
source="https://codeload.github.com/llvm-mirror/$pkgname/zip/refs/heads/$pkgver"

autounpack=true
downloadpackage=true
patchflag=true

builddir=$pkgname-$pkgver
packagename=$pkgname-$pkgver.zip

prepare() {
    # 因平台差异问题，缺少pthread_cancel需要打patch
    if [ $patchflag == true ];then
        cd $builddir
        patch -p1 < `pwd`/../openmp_oh_pkg.patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" \
        -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}