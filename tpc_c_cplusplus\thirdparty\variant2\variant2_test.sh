#!/bin/bash -e

./is_output_streamable
./quick
./variant_alternative
./variant_convert_construct
./variant_convert_construct_throw
./variant_copy_assign
./variant_copy_assign_throw
./variant_copy_construct
./variant_copy_construct_throw
./variant_default_construct
./variant_destroy
./variant_emplace_index
./variant_emplace_type
./variant_eq_ne
./variant_get_by_index
./variant_get_by_index_nx
./variant_get_by_type
./variant_get_by_type_nx
./variant_hash
./variant_holds_alternative
./variant_in_place_index_construct
./variant_in_place_type_construct
./variant_json_value_from
./variant_json_value_to
./variant_lt_gt
./variant_many_types
./variant_move_assign
./variant_move_assign_throw
./variant_move_construct
./variant_move_construct_throw
./variant_ostream_insert
./variant_size
./variant_special
./variant_subset
./variant_subset_nx
./variant_swap
./variant_trivial
./variant_value_assign
./variant_value_construct
./variant_valueless
./variant_visit
./variant_visit_by_index
./variant_visit_derived
./variant_visit_r