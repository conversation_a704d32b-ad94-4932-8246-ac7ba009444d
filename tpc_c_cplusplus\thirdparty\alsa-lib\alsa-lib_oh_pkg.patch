--- alsa-lib-1.1.3/src/ucm/parser.c
+++ alsa-lib-1.1.3/src/ucm/parser.c
@@ -32,6 +32,7 @@
 
 #include "ucm_local.h"
 #include <dirent.h>
+#include <linux/limits.h>
 
 /** The name of the environment variable containing the UCM directory */
 #define ALSA_CONFIG_UCM_VAR "ALSA_CONFIG_UCM"
@@ -1419,7 +1420,7 @@ int uc_mgr_scan_master_configs(const char **_list[])
 		"%s", env ? env : ALSA_USE_CASE_DIR);
 	filename[MAX_FILE-1] = '\0';
 
-#if defined(_GNU_SOURCE) && !defined(__NetBSD__) && !defined(__FreeBSD__) && !defined(__sun)
+#if defined(_GNU_SOURCE) && !defined(__NetBSD__) && !defined(__FreeBSD__) && !defined(__sun) && !defined(__OHOS__)
 #define SORTFUNC	versionsort
 #else
 #define SORTFUNC	alphasort
--- alsa-lib-1.1.3/src/conf.c
+++ alsa-lib-1.1.3/src/conf.c
@@ -3715,7 +3715,7 @@ int snd_config_hook_load(snd_config_t *root, snd_config_t *config, snd_config_t
 			int n;
 
 #ifndef DOC_HIDDEN
-#if defined(_GNU_SOURCE) && !defined(__NetBSD__) && !defined(__FreeBSD__) && !defined(__sun)
+#if defined(_GNU_SOURCE) && !defined(__NetBSD__) && !defined(__FreeBSD__) && !defined(__sun)&& !defined(__OHOS__)
 #define SORTFUNC	versionsort
 #else
 #define SORTFUNC	alphasort