# openssl三方库说明
## 功能简介
OpenSSL是一个强大的、商业级的、功能齐全的用于传输层安全（TLS）协议的开源工具包，以前称为安全套接字层（SSL）协议，应用程序可以使用这个包来进行安全通信，避免窃听，同时确认另一端连接者的身份。
## 使用约束
- IDE版本：DevEco Studio 3.1 Release
- SDK版本：ohos_sdk_public 4.0.8.1 (API Version 10 Release)
- 三方库版本：OpenSSL_1_1_1t
- 当前适配的功能：支持openssl加密传输功能

#### Legalities
  A number of nations restrict the use or export of cryptography. If you are potentially subject to such restrictions, you should seek legal advice before attempting to develop or distribute cryptographic code.

## 集成方式
+ [应用hap包集成](docs/hap_integrate.md)
