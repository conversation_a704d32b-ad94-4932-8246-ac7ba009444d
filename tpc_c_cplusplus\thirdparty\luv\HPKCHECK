# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd $builddir
    mkdir /usr/local/lib -p
    cp ${LYCIUM_ROOT}/../lycium/usr/${pkgname}/${ARCH}/lib/*.so* /usr/local/lib
    cp ${LYCIUM_ROOT}/../lycium/usr/libuv/${ARCH}/lib/*.so* /usr/local/lib
    cp ${LYCIUM_ROOT}/../lycium/usr/LuaJIT/${ARCH}/lib/*.so* /usr/local/lib
    mkdir /usr/local/lib/lua/5.1/ -p
    ln /usr/local/lib/libluv.so.1 /usr/local/lib/lua/5.1/luv.so
    ${LYCIUM_ROOT}/../lycium/usr/LuaJIT/${ARCH}/bin/luajit-2.1.0-beta3 tests/run.lua > ${logfile} 2>&1
    res=$?
    rm -rf /usr/local/lib
    cd $OLDPWD
    return $res
}