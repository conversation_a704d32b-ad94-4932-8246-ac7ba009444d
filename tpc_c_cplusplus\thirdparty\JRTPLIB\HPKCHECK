# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, wen fan <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

checkprepare() {
    return 0
}

openharmonycheck() {
    cd ${builddir}/${ARCH}-build/tests

    GREEN="\033[0;32m"
    RED="\033[0;31m"
    NC="\033[0m"

    declare -i fail=0
    declare -i pass=0
    declare -i all=0
    ret=

    echo "" > ${logfile} 2>&1
    for test in `ls`
    do
        if [ "${test: -4}" == "test" ] || [ "${test: 0: 4}" == "test" ]
        then
            if [ ${test} == "tcptest" ]
            then
                ./${test} 8888 >> ${logfile} 2>&1
                ret=$?
            else
                ./${test} >> ${logfile} 2>&1
                ret=$?
            fi

            if [ ${ret} -eq 0 ]
            then
                echo -e ${GREEN}PASS ${NC}${sample} >> ${logfile} 2>&1
                ((pass++))
            else
                echo -e ${RED}FAIL ${NC}${sample} >> ${logfile} 2>&1
                ((fail++))
            fi

            ((all++))
        fi
    done

    echo "TOATLLY: "${all} >> ${logfile} 2>&1
    echo -e ${GREEN}"PASS: "${NC}${pass} >> ${logfile} 2>&1
    echo -e ${RED}"FAIL: "${NC}${fail} >> ${logfile} 2>&1

    cd ${OLDPWD}
    if [ ${fail} -eq 0 ]
    then
        return 0
    else
        return -1
    fi
}
