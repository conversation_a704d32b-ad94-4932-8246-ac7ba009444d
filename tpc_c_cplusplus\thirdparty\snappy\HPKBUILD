# Contributor: huang<PERSON><PERSON>hong <<EMAIL>>
# Maintainer:  huang<PERSON>zhong <<EMAIL>>
pkgname=snappy
pkgver=1.1.10
pkgrel=0
pkgdesc="Snappy is a compression/decompression library. It does not aim for maximum compression, or compatibility with any other compression library; instead, it aims for very high speeds and reasonable compression."
url="https://github.com/google/snappy"
archs=("armeabi-v7a" "arm64-v8a")
license=("snappy license")
depends=()
makedepends=()
source="https://github.com/google/snappy.git"

downloadpackage=false
autounpack=false

builddir=$pkgname-$pkgver

cloneflag=true # 第一次下载成功标记为true，防止第二次下载
prepare() {
    if $cloneflag
    then
        git clone $source $builddir
        ret=$?
        if [ $ret -ne 0 ]; then
            echo "fail: git clone $source $builddir $ret"
            return $ret
        fi

        cd  $builddir
        git checkout -b $pkgver $pkgver
        ret=$?
        if [ $ret -ne 0 ]; then
            echo "fail: git checkout $pkgver $ret"
            return $ret
        fi

        git submodule update --init
        ret=$?
        if [ $ret -ne 0 ]; then
            echo "fail: git submodule update --init $ret"
            return $ret
        fi
        cd $OLDPWD
        cloneflag=false 
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake -DCMAKE_CXX_FLAGS="-w" "$@" -S./ \
        -B$ARCH-build -DOHOS_ARCH=$ARCH > $ARCH-build/build.log 2>&1
    make VERBOSE=1 -j4 -C $ARCH-build >> $ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 进入编译目录
    # ctest 
}

cleanbuild() {
    rm -rf ${PWD}/$builddir # ${PWD}/$packagename
}
