[
    {
        "Name": "thrift",
        "License": "Apache License 2.0",
        "License File": "https://github.com/apache/thrift/blob/master/LICENSE",
        "Version Number": "v0.18.1",
        "Owner": "<EMAIL>",
        "Upstream URL": "https://github.com/apache/thrift/archive/refs/tags/v0.18.1.tar.gz",
        "Description": "Thrift is an interface description language and binary communication protocol used to define and create cross-language services. It is designed as a remote procedure call (RPC) framework and was developed by Facebook for "large-scale cross-language service development"."
    },
    {
        "Name": "boost",
        "License": "Boost Software License",
        "License File": "https://www.boost.org/LICENSE_1_0.txt",
        "Version Number": "1.81.0",
        "Owner": "<EMAIL>",
        "Upstream URL": "https://boostorg.jfrog.io/artifactory/main/release/1.81.0/source/boost_1_81_0.tar.gz",
        "Description": "Boost provides free peer-reviewed portable C++ source libraries."
    },
    {
        "Name": "openssl",
        "License": "OpenSSL License and Original SSLeay License",
        "License File": "https://www.openssl.org/source/license-openssl-ssleay.txt",
        "Version Number": "1.1.1u",
        "Owner": "<EMAIL>",
        "Upstream URL": "https://gitee.com/mirrors/openssl/repository/archive/OpenSSL_1_1_1u.zip",
        "Description": "OpenSSL is a robust, commercial-grade, full-featured Open Source Toolkit for the Transport Layer Security (TLS) protocol formerly known as the Secure Sockets Layer (SSL) protocol."
    },
    {
        "Name": "zlib",
        "License": "zlib License",
        "License File": "https://github.com/madler/zlib/blob/master/LICENSE",
        "Version Number": "v1.2.13",
        "Owner": "<EMAIL>",
        "Upstream URL": "https://github.com/madler/zlib/releases/download/v1.2.13/zlib-1.2.13.tar.gz",
        "Description": "A massively spiffy yet delicately unobtrusive compression library."
    }
]
