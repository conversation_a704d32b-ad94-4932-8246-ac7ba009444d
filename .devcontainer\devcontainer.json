{"name": "OpenHarmony ijkplayer Development", "image": "mcr.microsoft.com/vscode/devcontainers/cpp:ubuntu-20.04", "features": {"ghcr.io/devcontainers/features/common-utils:2": {"installZsh": true, "configureZshAsDefaultShell": true, "installOhMyZsh": true}, "ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "customizations": {"vscode": {"extensions": ["ms-vscode.cpptools", "ms-vscode.cmake-tools", "ms-vscode.makefile-tools", "ms-python.python"], "settings": {"terminal.integrated.defaultProfile.linux": "zsh"}}}, "postCreateCommand": "bash .devcontainer/setup.sh", "remoteUser": "vscode", "mounts": ["source=${localWorkspaceFolder}/.devcontainer/cache,target=/tmp/cache,type=bind,consistency=cached"], "forwardPorts": [8080, 3000], "portsAttributes": {"8080": {"label": "Development Server", "onAutoForward": "notify"}}}