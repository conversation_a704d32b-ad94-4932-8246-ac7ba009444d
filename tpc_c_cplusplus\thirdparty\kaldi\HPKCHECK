# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
OLD_LD_LIBRARY_PATH=$LD_LIBRARY_PATH

checkprepare() {
    export LD_LIBRARY_PATH=`pwd`/$builddir-$ARCH-build:$LD_LIBRARY_PATH
    if [ ! -x /usr/bin/env ]
    then
        rm -rf /usr/bin/env
        ln -s /bin/env /usr/bin/env
    fi
}

openharmonycheck() {
    res=0
    cd $builddir-$ARCH-build/src
    make test > ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        # 收集报错日志
        if [ ! -d $LYCIUM_FAULT_PATH/${pkgname} ]
        then
            mkdir -p $LYCIUM_FAULT_PATH/${pkgname}
        fi
        cp $(find . -name *.testlog) $LYCIUM_FAULT_PATH/${pkgname}/
    fi

    export LD_LIBRARY_PATH=$OLD_LD_LIBRARY_PATH
    unset OLD_LD_LIBRARY_PATH
    cd $OLDPWD
    rm -rf /usr/bin/env
    return $res
}
