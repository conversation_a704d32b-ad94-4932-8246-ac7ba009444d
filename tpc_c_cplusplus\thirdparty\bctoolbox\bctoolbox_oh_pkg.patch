--- bctoolbox-5.2.91_old/src/tester.c	2023-07-03 16:34:28.000000000 +0800
+++ bctoolbox-5.2.91/src/tester.c	2023-07-12 18:47:02.450488840 +0800
@@ -475,7 +475,7 @@
 	//insert empty line
 	bc_tester_printf(bc_printf_verbosity_info,"");
 
-#ifdef __linux__
+#if defined(__linux__) && !defined(__OHOS__)
 	/* use mallinfo() to monitor allocated space. It is linux specific but other methods don't work:
 	 * setrlimit() RLIMIT_DATA doesn't count memory allocated via mmap() (which is used internally by malloc)
 	 * setrlimit() RLIMIT_AS works but also counts virtual memory allocated by thread stacks, which is very big and
@@ -1074,7 +1074,7 @@
 				}
 		}
 end:
-#ifdef __linux__
+#if defined(__linux__) && !defined(__OHOS__)
 	bc_tester_printf(bc_printf_verbosity_info, "Still %i kilobytes allocated when all tests are finished.",
 			 mallinfo().uordblks / 1024);
 #endif

