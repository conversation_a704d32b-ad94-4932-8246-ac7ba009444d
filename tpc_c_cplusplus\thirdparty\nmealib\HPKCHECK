# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, Sunjiamei<<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd $builddir-$ARCH-build/build/samples
    echo "total test 5"  > $logfile
    ./generate >> ${logfile} 2>&1
    res=$?
    if [$res -ne 0];then
        echo "test 1 error" >> $logfile
        cd $OLDPWD
        return $res
    fi
    echo "1 pass" >> $logfile

    echo -e "list samples\ngenerator" | ./generator >> $logfile
    res=$?
    if [ $res -ne 0 ]
    then
        echo "test 2 error" >> $logfile
        cd $OLDPWD
        return $res
    fi
    echo "2 pass" >> $logfile

    echo -e "list samples\nmath" | ./math >> $logfile
    res=$?
    if [ $res -ne 0 ]
    then
        echo "test 3 error" >> $logfile
        cd $OLDPWD
        return $res
    fi 
    echo "3 pass" >> $logfile

    echo -e "list samples\nparse" | ./parse >> $logfile
    res=$?
    if [ $res -ne 0 ]
    then
        echo "test 4 error" >> $logfile
        cd $OLDPWD
        return $res
    fi 
    echo "4 pass" >> $logfile

    echo -e "list samples\nparse_file" | ./parse_file >> $logfile
    res=$?
    if [ $res -ne 0 ]
    then
        echo "test 5 error" >> $logfile
        cd $OLDPWD
        return $res
    fi 
    echo "5 pass" >> $logfile

    cd $OLDPWD
    return $res
}
