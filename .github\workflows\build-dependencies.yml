name: Build ijkplayer Dependencies

on:
  workflow_dispatch: # 手动触发
    inputs:
      sdk_url:
        description: 'OpenHarmony SDK 下载链接'
        required: true
        default: 'https://example.com/ohos-sdk.tar.gz'
  push:
    branches: [main]
    paths:
      - 'prebuild.sh'
      - 'doc/**'

jobs:
  build-dependencies:
    runs-on: ubuntu-latest
    timeout-minutes: 360 # 6小时超时

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check current user and path
        run: |
          # 检查当前用户和路径，确保后续操作正确
          echo "当前用户: $(whoami)"
          echo "当前路径: $(pwd)"
          echo "HOME: $HOME"

      - name: Free up disk space
        run: |
          # 清理不需要的软件，释放空间
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /usr/local/lib/android
          sudo rm -rf /opt/ghc
          sudo rm -rf /opt/hostedtoolcache/CodeQL
          sudo docker image prune --all --force
          df -h

      - name: Setup environment
        run: |
          sudo apt update
          sudo apt install -y gcc make cmake pkg-config autoconf automake patch libtool
          sudo apt install -y wget unzip git python3 python3-pip ninja-build meson
          sudo apt install -y build-essential flex bison yasm nasm

      - name: Download and extract OpenHarmony SDK
        run: |
          # 下载 SDK 压缩包
          SDK_URL="${{ github.event.inputs.sdk_url || 'https://cidownload.openharmony.cn/version/Release_Version/OpenHarmony-********/20250521_154226/version-Release_Version-OpenHarmony-********-20250521_154226-ohos-sdk-full_4.1-Release.tar.gz' }}"
          echo "下载 SDK: $SDK_URL"
          wget "$SDK_URL" -O ohos-sdk-full.tar.gz

          # 步骤1: 创建 ohos-sdk-4.1 目录
          echo "=== 步骤1: 创建 SDK 基础目录 ==="
          mkdir -p /opt/ohos-sdk-4.1

          # 步骤2: 解压 SDK 到 ohos-sdk-4.1 目录
          echo "=== 步骤2: 解压 SDK 到 /opt/ohos-sdk-4.1/ ==="
          tar -xzf ohos-sdk-full.tar.gz -C /opt/ohos-sdk-4.1/

          # 检查解压结果
          echo "=== 检查解压结果 ==="
          echo "ohos-sdk-4.1 目录内容："
          ls -la /opt/ohos-sdk-4.1/

          if [ -d "/opt/ohos-sdk-4.1/ohos-sdk" ]; then
            echo "✅ 找到 ohos-sdk 目录"
            ls -la /opt/ohos-sdk-4.1/ohos-sdk/

            if [ -d "/opt/ohos-sdk-4.1/ohos-sdk/linux" ]; then
              echo "✅ 找到 linux 目录"
              echo "linux 目录中的 zip 文件："
              ls -la /opt/ohos-sdk-4.1/ohos-sdk/linux/*.zip 2>/dev/null || echo "未找到 zip 文件"
            else
              echo "❌ 未找到 linux 目录"
            fi
          else
            echo "❌ 未找到 ohos-sdk 目录"
          fi

      - name: Setup SDK directory structure
        run: |
          # 步骤3: 创建 11 文件夹
          echo "=== 步骤3: 创建 11 文件夹 ==="
          mkdir -p /opt/ohos-sdk-4.1/ohos-sdk/linux/11
          echo "✅ 创建了 /opt/ohos-sdk-4.1/ohos-sdk/linux/11 目录"

          # 步骤4: 从 ohos-sdk-4.1/ohos-sdk/linux 路径下找到 zip 文件并解压到 11 文件夹
          echo "=== 步骤4: 解压 zip 文件到 11 文件夹 ==="

          ZIP_SOURCE_PATH="/opt/ohos-sdk-4.1/ohos-sdk/linux"
          ZIP_TARGET_PATH="/opt/ohos-sdk-4.1/ohos-sdk/linux/11"

          if [ -d "$ZIP_SOURCE_PATH" ]; then
            echo "✅ 找到 zip 源目录: $ZIP_SOURCE_PATH"

            # 列出所有 zip 文件
            echo "找到的 zip 文件："
            ls -la "$ZIP_SOURCE_PATH"/*.zip 2>/dev/null || echo "未找到 zip 文件"

            # 解压所有 zip 文件到 11 目录
            cd "$ZIP_SOURCE_PATH"
            for zip_file in *.zip; do
              if [ -f "$zip_file" ]; then
                echo "解压: $zip_file → $ZIP_TARGET_PATH"
                unzip -q "$zip_file" -d "$ZIP_TARGET_PATH"

                if [ $? -eq 0 ]; then
                  echo "  ✅ $zip_file 解压成功"
                else
                  echo "  ❌ $zip_file 解压失败"
                fi
              fi
            done

            # 返回工作目录
            cd "$GITHUB_WORKSPACE"

          else
            echo "❌ 未找到 zip 源目录: $ZIP_SOURCE_PATH"
            echo "当前 ohos-sdk-4.1 目录结构："
            find /opt/ohos-sdk-4.1 -type d | head -10
            exit 1
          fi

          # 检查最终结果
          echo "=== 最终 SDK 结构检查 ==="
          echo "11 目录内容："
          ls -la "$ZIP_TARGET_PATH"

          # 查找关键组件
          echo "=== 查找关键组件 ==="
          if [ -d "$ZIP_TARGET_PATH/native" ]; then
            echo "✅ 找到 native 目录"
            find "$ZIP_TARGET_PATH/native" -name "clang" -type f | head -3
          else
            echo "⚠️  未找到 native 目录，列出所有目录："
            find "$ZIP_TARGET_PATH" -type d | head -10
          fi

          # 设置执行权限
          echo "=== 设置执行权限 ==="
          find "$ZIP_TARGET_PATH" -type f -exec chmod +x {} \; 2>/dev/null || true
          echo "✅ 权限设置完成"

      - name: Verify system architecture
        run: |
          echo "=== 系统架构信息 ==="
          uname -m    # 应该显示 x86_64
          uname -s    # 应该显示 Linux
          lscpu | grep Architecture
          echo "=== 系统资源 ==="
          free -h
          df -h

      - name: Set SDK environment
        run: |
          # 设置 SDK 环境变量
          export OHOS_SDK="/opt/ohos-sdk-4.1/ohos-sdk/linux/11"
          echo "OHOS_SDK=$OHOS_SDK" >> $GITHUB_ENV
          echo "✅ SDK 环境变量已设置: $OHOS_SDK"

      - name: Build dependencies
        run: |
          # 保持在当前工作目录，只对需要权限的命令使用 sudo
          chmod +x prebuild.sh

          # 修改 SDK 路径和 apt 命令
          sed -i 's|SDK_DIR=/root/ohos-sdk-4.1/ohos-sdk/linux/11|SDK_DIR=/opt/ohos-sdk-4.1/ohos-sdk/linux/11|g' prebuild.sh
          sed -i 's|apt update|sudo apt update|g' prebuild.sh
          sed -i 's|apt install|sudo apt install|g' prebuild.sh

          # 确保在正确的工作目录执行
          echo "执行编译前的路径检查:"
          pwd
          ls -la prebuild.sh

          # 创建一个智能的编译脚本
          cat > smart_build.sh << 'EOF'
          #!/bin/bash
          set -e

          echo "=== 智能编译脚本开始 ==="

          # 启动原始编译脚本
          ./prebuild.sh &
          BUILD_PID=$!

          # 监控 tpc_c_cplusplus 下载
          echo "=== 监控 tpc_c_cplusplus 下载 ==="
          while [ ! -d "tpc_c_cplusplus/thirdparty" ]; do
            if ! kill -0 $BUILD_PID 2>/dev/null; then
              echo "编译进程意外结束"
              exit 1
            fi
            echo "等待 tpc_c_cplusplus 下载..."
            sleep 3
          done

          echo "✅ tpc_c_cplusplus 下载完成"

          # 等待一下确保文件完整
          sleep 5

          # 修改 FFmpeg 配置（如果文件存在）
          if [ -f "tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD" ]; then
            echo "=== 修改 FFmpeg 配置 ==="

            # 显示修改前的配置
            echo "修改前的配置:"
            grep -A 2 -B 2 "enable-libopenh264" tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD || echo "未找到 enable-libopenh264"

            # 更强力的修改方式 - 直接在 configure 行添加 VP8 支持
            # 方法1: 在 --enable-libopenh264 后添加
            sed -i 's|--enable-libopenh264|--enable-libopenh264 --enable-decoder=vp8 --enable-parser=vp8 --enable-decoder=vp9 --enable-parser=vp9|g' tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD

            # 方法2: 如果上面没成功，在 configure 行末尾添加
            sed -i 's|--sysroot=${OHOS_SDK}/native/sysroot|--sysroot=${OHOS_SDK}/native/sysroot --enable-decoder=vp8 --enable-parser=vp8 --enable-decoder=vp9 --enable-parser=vp9|g' tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD

            echo "✅ FFmpeg 配置修改完成"

            # 验证修改
            echo "=== 验证修改结果 ==="
            echo "修改后的配置:"
            grep -A 2 -B 2 "enable-decoder=vp8\|enable-libopenh264" tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD || echo "未找到相关配置"

            # 显示完整的 configure 行
            echo "完整的 configure 行:"
            grep "PKG_CONFIG_LIBDIR.*configure" tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD

            if grep -q "enable-decoder=vp8" tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD; then
              echo "✅ VP8 解码器已启用"
            else
              echo "❌ VP8 解码器未启用"
            fi
          fi

          # 等待编译完成
          echo "=== 等待编译完成 ==="
          wait $BUILD_PID
          EOF

          chmod +x smart_build.sh

          # 不修改编译顺序，保持原样（不编译 libvpx）
          echo "保持原始编译顺序"

          # 执行智能编译
          ./smart_build.sh

      - name: Check build results
        run: |
          echo "检查编译结果..."
          ls -la ijkplayer/src/main/cpp/third_party/
          echo "=== 检查各个库目录 ==="
          for lib in ffmpeg yuv openssl soundtouch openh264; do
            if [ -d "ijkplayer/src/main/cpp/third_party/$lib" ]; then
              echo "✅ $lib 目录存在"
              ls -la "ijkplayer/src/main/cpp/third_party/$lib" | head -5
            else
              echo "❌ $lib 目录不存在"
            fi
          done
          echo "=== 静态库文件 ==="
          find ijkplayer/src/main/cpp/third_party/ -name "*.a" | head -15

          echo "=== 检查 VP8 支持 ==="
          # 检查 libavcodec.a 文件大小
          for arch in armeabi-v7a arm64-v8a x86_64; do
            LIBAVCODEC="ijkplayer/src/main/cpp/third_party/ffmpeg/$arch/lib/libavcodec.a"
            if [ -f "$LIBAVCODEC" ]; then
              SIZE=$(ls -lh "$LIBAVCODEC" | awk '{print $5}')
              echo "📦 $arch libavcodec.a 大小: $SIZE"

              # 尝试查找 VP8 相关符号
              if strings "$LIBAVCODEC" | grep -i vp8 | head -3; then
                echo "✅ $arch: 发现 VP8 相关符号"
              else
                echo "❌ $arch: 未发现 VP8 相关符号"
              fi
            else
              echo "❌ $arch: libavcodec.a 不存在"
            fi
          done

          echo "=== 检查编译配置是否生效 ==="
          if [ -f "tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD" ]; then
            echo "最终的 FFmpeg 配置:"
            grep "PKG_CONFIG_LIBDIR.*configure" tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD
            if grep -q "enable-decoder=vp8" tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD; then
              echo "✅ 配置文件中包含 VP8 支持"
            else
              echo "❌ 配置文件中不包含 VP8 支持"
            fi
          fi

      - name: Package results
        run: |
          tar -czf ijkplayer-dependencies.tar.gz ijkplayer/src/main/cpp/third_party/
          ls -lh ijkplayer-dependencies.tar.gz

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ijkplayer-dependencies
          path: ijkplayer-dependencies.tar.gz
          retention-days: 30
