name: Build ijkplayer Dependencies

on:
  workflow_dispatch: # 手动触发
    inputs:
      sdk_url:
        description: 'OpenHarmony SDK 下载链接'
        required: true
        default: 'https://example.com/ohos-sdk.tar.gz'
  push:
    branches: [main]
    paths:
      - 'prebuild.sh'
      - 'doc/**'

jobs:
  build-dependencies:
    runs-on: ubuntu-latest
    timeout-minutes: 360 # 6小时超时

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check current user and path
        run: |
          # 检查当前用户和路径，确保后续操作正确
          echo "当前用户: $(whoami)"
          echo "当前路径: $(pwd)"
          echo "HOME: $HOME"

      - name: Free up disk space
        run: |
          # 清理不需要的软件，释放空间
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /usr/local/lib/android
          sudo rm -rf /opt/ghc
          sudo rm -rf /opt/hostedtoolcache/CodeQL
          sudo docker image prune --all --force
          df -h

      - name: Setup environment
        run: |
          sudo apt update
          sudo apt install -y gcc make cmake pkg-config autoconf automake patch libtool
          sudo apt install -y wget unzip git python3 python3-pip ninja-build meson
          sudo apt install -y build-essential flex bison yasm nasm

      - name: Download and extract OpenHarmony SDK
        run: |
          # 下载 SDK 压缩包
          SDK_URL="${{ github.event.inputs.sdk_url || 'https://cidownload.openharmony.cn/version/Release_Version/OpenHarmony-********/20250521_154226/version-Release_Version-OpenHarmony-********-20250521_154226-ohos-sdk-full_4.1-Release.tar.gz' }}"
          echo "下载 SDK: $SDK_URL"
          wget "$SDK_URL" -O ohos-sdk-full.tar.gz

          # 步骤1: 创建 ohos-sdk-4.1 目录
          echo "=== 步骤1: 创建 SDK 基础目录 ==="
          mkdir -p /opt/ohos-sdk-4.1

          # 步骤2: 解压 SDK 到 ohos-sdk-4.1 目录
          echo "=== 步骤2: 解压 SDK 到 /opt/ohos-sdk-4.1/ ==="
          tar -xzf ohos-sdk-full.tar.gz -C /opt/ohos-sdk-4.1/

          # 检查解压结果
          echo "=== 检查解压结果 ==="
          echo "ohos-sdk-4.1 目录内容："
          ls -la /opt/ohos-sdk-4.1/

          if [ -d "/opt/ohos-sdk-4.1/ohos-sdk" ]; then
            echo "✅ 找到 ohos-sdk 目录"
            ls -la /opt/ohos-sdk-4.1/ohos-sdk/

            if [ -d "/opt/ohos-sdk-4.1/ohos-sdk/linux" ]; then
              echo "✅ 找到 linux 目录"
              echo "linux 目录中的 zip 文件："
              ls -la /opt/ohos-sdk-4.1/ohos-sdk/linux/*.zip 2>/dev/null || echo "未找到 zip 文件"
            else
              echo "❌ 未找到 linux 目录"
            fi
          else
            echo "❌ 未找到 ohos-sdk 目录"
          fi

      - name: Setup SDK directory structure
        run: |
          # 步骤3: 创建 11 文件夹
          echo "=== 步骤3: 创建 11 文件夹 ==="
          mkdir -p /opt/ohos-sdk-4.1/ohos-sdk/linux/11
          echo "✅ 创建了 /opt/ohos-sdk-4.1/ohos-sdk/linux/11 目录"

          # 步骤4: 从 ohos-sdk-4.1/ohos-sdk/linux 路径下找到 zip 文件并解压到 11 文件夹
          echo "=== 步骤4: 解压 zip 文件到 11 文件夹 ==="

          ZIP_SOURCE_PATH="/opt/ohos-sdk-4.1/ohos-sdk/linux"
          ZIP_TARGET_PATH="/opt/ohos-sdk-4.1/ohos-sdk/linux/11"

          if [ -d "$ZIP_SOURCE_PATH" ]; then
            echo "✅ 找到 zip 源目录: $ZIP_SOURCE_PATH"

            # 列出所有 zip 文件
            echo "找到的 zip 文件："
            ls -la "$ZIP_SOURCE_PATH"/*.zip 2>/dev/null || echo "未找到 zip 文件"

            # 解压所有 zip 文件到 11 目录
            cd "$ZIP_SOURCE_PATH"
            for zip_file in *.zip; do
              if [ -f "$zip_file" ]; then
                echo "解压: $zip_file → $ZIP_TARGET_PATH"
                unzip -q "$zip_file" -d "$ZIP_TARGET_PATH"

                if [ $? -eq 0 ]; then
                  echo "  ✅ $zip_file 解压成功"
                else
                  echo "  ❌ $zip_file 解压失败"
                fi
              fi
            done

            # 返回工作目录
            cd "$GITHUB_WORKSPACE"

          else
            echo "❌ 未找到 zip 源目录: $ZIP_SOURCE_PATH"
            echo "当前 ohos-sdk-4.1 目录结构："
            find /opt/ohos-sdk-4.1 -type d | head -10
            exit 1
          fi

          # 检查最终结果
          echo "=== 最终 SDK 结构检查 ==="
          echo "11 目录内容："
          ls -la "$ZIP_TARGET_PATH"

          # 查找关键组件
          echo "=== 查找关键组件 ==="
          if [ -d "$ZIP_TARGET_PATH/native" ]; then
            echo "✅ 找到 native 目录"
            find "$ZIP_TARGET_PATH/native" -name "clang" -type f | head -3
          else
            echo "⚠️  未找到 native 目录，列出所有目录："
            find "$ZIP_TARGET_PATH" -type d | head -10
          fi

          # 设置执行权限
          echo "=== 设置执行权限 ==="
          find "$ZIP_TARGET_PATH" -type f -exec chmod +x {} \; 2>/dev/null || true
          echo "✅ 权限设置完成"

      - name: Verify system architecture
        run: |
          echo "=== 系统架构信息 ==="
          uname -m    # 应该显示 x86_64
          uname -s    # 应该显示 Linux
          lscpu | grep Architecture
          echo "=== 系统资源 ==="
          free -h
          df -h

      - name: Set SDK environment
        run: |
          # 设置 SDK 环境变量
          export OHOS_SDK="/opt/ohos-sdk-4.1/ohos-sdk/linux/11"
          echo "OHOS_SDK=$OHOS_SDK" >> $GITHUB_ENV
          echo "✅ SDK 环境变量已设置: $OHOS_SDK"

      - name: Build dependencies
        run: |
          # 保持在当前工作目录，只对需要权限的命令使用 sudo
          chmod +x prebuild.sh

          # 修改 SDK 路径和 apt 命令
          sed -i 's|SDK_DIR=/root/ohos-sdk-4.1/ohos-sdk/linux/11|SDK_DIR=/opt/ohos-sdk-4.1/ohos-sdk/linux/11|g' prebuild.sh
          sed -i 's|apt update|sudo apt update|g' prebuild.sh
          sed -i 's|apt install|sudo apt install|g' prebuild.sh

          # 确保在正确的工作目录执行
          echo "执行编译前的路径检查:"
          pwd
          ls -la prebuild.sh

          # 启动编译脚本（后台运行）
          echo "=== 启动编译脚本 ==="
          ./prebuild.sh &
          PREBUILD_PID=$!

          # 等待 tpc_c_cplusplus 下载完成
          echo "=== 等待 tpc_c_cplusplus 下载完成 ==="
          while [ ! -d "tpc_c_cplusplus/thirdparty" ]; do
            echo "等待 tpc_c_cplusplus 下载..."
            sleep 5
          done

          echo "=== 检查所有 FFmpeg 相关目录 ==="
          find tpc_c_cplusplus/thirdparty -name "*ffmpeg*" -o -name "*FFmpeg*" -type d

          echo "=== 检查所有 HPKBUILD 文件 ==="
          find tpc_c_cplusplus/thirdparty -name "HPKBUILD" | grep -i ffmpeg

          # 检查所有可能的 FFmpeg HPKBUILD 文件
          for ffmpeg_dir in $(find tpc_c_cplusplus/thirdparty -name "*ffmpeg*" -o -name "*FFmpeg*" -type d); do
            if [ -f "$ffmpeg_dir/HPKBUILD" ]; then
              echo "=== 找到 FFmpeg HPKBUILD: $ffmpeg_dir/HPKBUILD ==="
              echo "文件内容:"
              cat "$ffmpeg_dir/HPKBUILD"
              echo ""
              echo "configure 相关行:"
              grep -n "configure" "$ffmpeg_dir/HPKBUILD"
              echo ""
            fi
          done

          echo "=== 修改所有 FFmpeg 配置以支持 VP8 ==="

          # 对所有找到的 FFmpeg HPKBUILD 文件进行修改
          for ffmpeg_dir in $(find tpc_c_cplusplus/thirdparty -name "*ffmpeg*" -o -name "*FFmpeg*" -type d); do
            HPKBUILD_FILE="$ffmpeg_dir/HPKBUILD"
            if [ -f "$HPKBUILD_FILE" ]; then
              echo "=== 修改 $HPKBUILD_FILE ==="

              echo "修改前的 configure 行:"
              grep "PKG_CONFIG_LIBDIR.*configure" "$HPKBUILD_FILE" || echo "未找到 configure 行"

              # 尝试多种修改方式
              echo "尝试方式1: 在 --enable-libopenh264 后添加"
              sed -i 's|--enable-libopenh264|--enable-libopenh264 --enable-decoder=vp8 --enable-parser=vp8|g' "$HPKBUILD_FILE"

              echo "尝试方式2: 在 --sysroot 前添加"
              sed -i 's|--sysroot=${OHOS_SDK}/native/sysroot|--enable-decoder=vp8 --enable-parser=vp8 --sysroot=${OHOS_SDK}/native/sysroot|g' "$HPKBUILD_FILE"

              echo "尝试方式3: 在 > \$buildlog 前添加"
              sed -i 's| > \$buildlog| --enable-decoder=vp8 --enable-parser=vp8 > \$buildlog|g' "$HPKBUILD_FILE"

              echo "修改后的 configure 行:"
              grep "PKG_CONFIG_LIBDIR.*configure" "$HPKBUILD_FILE" || echo "未找到 configure 行"

              if grep -q "enable-decoder=vp8" "$HPKBUILD_FILE"; then
                echo "✅ $HPKBUILD_FILE VP8 解码器已添加"
                echo "VP8 相关配置:"
                grep -o "enable-[^[:space:]]*vp8[^[:space:]]*" "$HPKBUILD_FILE"
              else
                echo "❌ $HPKBUILD_FILE VP8 解码器未添加"
                echo "最后尝试: 直接在 configure 行末尾添加"
                sed -i 's|2>&1|--enable-decoder=vp8 --enable-parser=vp8 2>\&1|g' "$HPKBUILD_FILE"

                if grep -q "enable-decoder=vp8" "$HPKBUILD_FILE"; then
                  echo "✅ 最后尝试成功"
                else
                  echo "❌ 所有尝试都失败了"
                fi
              fi
              echo ""
            fi
          done

          # 等待编译完成
          wait $PREBUILD_PID
          PREBUILD_RESULT=$?
          echo "编译结果: $PREBUILD_RESULT"

          if [ $PREBUILD_RESULT -ne 0 ]; then
            echo "❌ 编译失败"
            exit $PREBUILD_RESULT
          fi

      - name: Check build results
        run: |
          echo "检查编译结果..."
          ls -la ijkplayer/src/main/cpp/third_party/
          echo "=== 检查各个库目录 ==="
          for lib in ffmpeg yuv openssl soundtouch openh264; do
            if [ -d "ijkplayer/src/main/cpp/third_party/$lib" ]; then
              echo "✅ $lib 目录存在"
              ls -la "ijkplayer/src/main/cpp/third_party/$lib" | head -5
            else
              echo "❌ $lib 目录不存在"
            fi
          done
          echo "=== 静态库文件 ==="
          find ijkplayer/src/main/cpp/third_party/ -name "*.a" | head -10

          echo "=== 检查 FFmpeg 编译配置 ==="
          echo "搜索所有可能的 config.h 文件位置..."

          # 搜索更广泛的路径
          CONFIG_H_FILES=$(find tpc_c_cplusplus/ -name "config.h" 2>/dev/null | grep -i ffmpeg || true)

          if [ -n "$CONFIG_H_FILES" ]; then
            echo "找到 FFmpeg 相关的 config.h 文件:"
            echo "$CONFIG_H_FILES"

            for config_file in $CONFIG_H_FILES; do
              echo "=== 检查 $config_file 中的 VP8 配置 ==="
              if grep -q "CONFIG_VP8_DECODER" "$config_file"; then
                VP8_CONFIG=$(grep "CONFIG_VP8_DECODER" "$config_file")
                echo "VP8 解码器配置: $VP8_CONFIG"

                if echo "$VP8_CONFIG" | grep -q "CONFIG_VP8_DECODER 1"; then
                  echo "✅ VP8 解码器已启用"
                else
                  echo "❌ VP8 解码器未启用"
                fi
              else
                echo "❌ 未找到 CONFIG_VP8_DECODER 配置"
              fi

              if grep -q "CONFIG_VP8_PARSER" "$config_file"; then
                VP8_PARSER=$(grep "CONFIG_VP8_PARSER" "$config_file")
                echo "VP8 解析器配置: $VP8_PARSER"
              else
                echo "❌ 未找到 CONFIG_VP8_PARSER 配置"
              fi
              echo ""
            done
          else
            echo "❌ 未找到 FFmpeg 相关的 config.h 文件"
            echo "搜索所有 config.h 文件:"
            find tpc_c_cplusplus/ -name "config.h" 2>/dev/null | head -10 || echo "未找到任何 config.h 文件"
          fi

          echo "=== 检查所有 FFmpeg HPKBUILD 的最终配置 ==="
          for ffmpeg_dir in $(find tpc_c_cplusplus/thirdparty -name "*ffmpeg*" -o -name "*FFmpeg*" -type d); do
            HPKBUILD_FILE="$ffmpeg_dir/HPKBUILD"
            if [ -f "$HPKBUILD_FILE" ]; then
              echo "=== 检查 $HPKBUILD_FILE 最终配置 ==="
              echo "最终的 configure 行:"
              grep "PKG_CONFIG_LIBDIR.*configure" "$HPKBUILD_FILE" || echo "未找到 configure 行"

              if grep -q "enable-decoder=vp8" "$HPKBUILD_FILE"; then
                echo "✅ $HPKBUILD_FILE 中包含 VP8 配置"
                echo "VP8 配置详情:"
                grep -o "enable-[^[:space:]]*vp8[^[:space:]]*" "$HPKBUILD_FILE"
              else
                echo "❌ $HPKBUILD_FILE 中不包含 VP8 配置"
              fi
              echo ""
            fi
          done

      - name: Package results
        run: |
          tar -czf ijkplayer-dependencies.tar.gz ijkplayer/src/main/cpp/third_party/
          ls -lh ijkplayer-dependencies.tar.gz

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ijkplayer-dependencies
          path: ijkplayer-dependencies.tar.gz
          retention-days: 30
