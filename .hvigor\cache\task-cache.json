{":ohos_ijkplayer-2.0.3:ijkplayer:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":11,\"_valueType\":\"number\",\"_hash\":\"93f5cd3d75ef7f4c00083401b682d592\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":11,\"_valueType\":\"number\",\"_hash\":\"e44e5a65058824d789a8244107920ae0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"3f44547af15457ff8e1ec09282ca9922\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"ijkplayer\",\"_valueType\":\"string\",\"_hash\":\"19a5965fc7504f13ae896fa1ae787edb\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"4.1.10.3\",\"_valueType\":\"string\",\"_hash\":\"6eeb8a57d5d6490fb8cae38a90bbcf01\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":1,\"_valueType\":\"number\",\"_hash\":\"0ec6123a9c2b84b1bb8ff1aa490b637d\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "<PERSON><PERSON>player", "_taskName": "default@PreBuild", "_key": ":ohos_ijkplayer-2.0.3:ijkplayer:default@PreBuild", "_executionId": ":ohos_ijkplayer-2.0.3:ijkplayer:default@PreBuild:1753097753668", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "cef64c4399b89e54c9071a4e00dc5397"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "f01c3672923502ce0ee6e31c74be10a9"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5", {"fileSnapShotHashValue": "ddc742760938c40b1c3f36e9e5ef53f4"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build-profile.json5", {"fileSnapShotHashValue": "c7544b603ddf3eb39a0e0facc75ccb8c"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\oh-package.json5", {"fileSnapShotHashValue": "d1f0b0c6a45f89cf4a31701104f904e2"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":ohos_ijkplayer-2.0.3:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":1,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"4.1.10.3\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":ohos_ijkplayer-2.0.3:entry:default@PreBuild", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@PreBuild:1753097815521", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "cef64c4399b89e54c9071a4e00dc5397"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "141b61b2b51dbacc2007e80cfd681425"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5", {"fileSnapShotHashValue": "ddc742760938c40b1c3f36e9e5ef53f4"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build-profile.json5", {"fileSnapShotHashValue": "d69a2a8b026875354d95f1c4fb7ad683"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "ce5793bd5d1828d71e303e2b7f9c78f6"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\oh-package.json5", {"fileSnapShotHashValue": "455dec0ebed1d9d60d66472c7d4de422"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":ohos_ijkplayer-2.0.3:entry:default@GenerateMetadata": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleDeviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"product\",\"_value\":\"default\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"entryModules\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allRemoteHspPathList\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@GenerateMetadata", "_key": ":ohos_ijkplayer-2.0.3:entry:default@GenerateMetadata", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@GenerateMetadata:1753097815638", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "141b61b2b51dbacc2007e80cfd681425"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", {"fileSnapShotHashValue": "50112cec35c2b0863c3186e27244ceb4"}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":ohos_ijkplayer-2.0.3:entry:default@CreateBuildProfile", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@CreateBuildProfile:1753097815656", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5", {"fileSnapShotHashValue": "cef64c4399b89e54c9071a4e00dc5397"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5", {"fileSnapShotHashValue": "ddc742760938c40b1c3f36e9e5ef53f4"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "c79805d2bea8db661ed7d646e4839990"}]]}}, ":ohos_ijkplayer-2.0.3:ijkplayer:default@CreateHarBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"har<PERSON><PERSON><PERSON>\",\"_value\":\"2.0.3\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "<PERSON><PERSON>player", "_taskName": "default@CreateHarBuildProfile", "_key": ":ohos_ijkplayer-2.0.3:ijkplayer:default@CreateHarBuildProfile", "_executionId": ":ohos_ijkplayer-2.0.3:ijkplayer:default@CreateHarBuildProfile:1753097815662", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5", {"fileSnapShotHashValue": "cef64c4399b89e54c9071a4e00dc5397"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5", {"fileSnapShotHashValue": "ddc742760938c40b1c3f36e9e5ef53f4"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\BuildProfile.ets", {"fileSnapShotHashValue": "27e8e308ca9570fe7f367f90fd34b9dc"}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\entry\\\",\\\"ijkplayer\\\":\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\ijkplayer\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\entry\\\\modules.ap\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":ohos_ijkplayer-2.0.3:entry:default@GenerateLoaderJson", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@GenerateLoaderJson:1753097815670", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "6bd09de6bceeddff47fbf78b843e218d"}]]}}, ":ohos_ijkplayer-2.0.3:ijkplayer:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\"build\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "<PERSON><PERSON>player", "_taskName": "default@MergeProfile", "_key": ":ohos_ijkplayer-2.0.3:ijkplayer:default@MergeProfile", "_executionId": ":ohos_ijkplayer-2.0.3:ijkplayer:default@MergeProfile:1753097815694", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5", {"fileSnapShotHashValue": "cef64c4399b89e54c9071a4e00dc5397"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5", {"fileSnapShotHashValue": "ddc742760938c40b1c3f36e9e5ef53f4"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\module.json5", {"fileSnapShotHashValue": "f01c3672923502ce0ee6e31c74be10a9"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "49b5334b4f1268d2763dbe80d83e56d7"}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\"build\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":ohos_ijkplayer-2.0.3:entry:default@MergeProfile", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@MergeProfile:1753097815704", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5", {"fileSnapShotHashValue": "cef64c4399b89e54c9071a4e00dc5397"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5", {"fileSnapShotHashValue": "ddc742760938c40b1c3f36e9e5ef53f4"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "141b61b2b51dbacc2007e80cfd681425"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "49b5334b4f1268d2763dbe80d83e56d7"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "5fe1a64bd6faae1feea43d7d94fcc881"}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@MakePackInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@MakePackInfo", "_key": ":ohos_ijkplayer-2.0.3:entry:default@MakePackInfo", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@MakePackInfo:1753097815718", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5", {"fileSnapShotHashValue": "cef64c4399b89e54c9071a4e00dc5397"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "141b61b2b51dbacc2007e80cfd681425"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5", {"fileSnapShotHashValue": "ddc742760938c40b1c3f36e9e5ef53f4"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\outputs\\default\\pack.info", {"fileSnapShotHashValue": "87ddca1f15ceda726572bd56c4ec00ee"}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":ohos_ijkplayer-2.0.3:entry:default@ProcessProfile", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@ProcessProfile:1753097815744", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "5fe1a64bd6faae1feea43d7d94fcc881"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "f4596dcf111cf540a2053ebc15b11b47"}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@ProcessResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"resConfigJsonContent\",\"_value\":\"{\\\"configPath\\\":\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\process_profile\\\\\\\\default\\\\\\\\module.json\\\",\\\"packageName\\\":\\\"com.example.ijkplayer\\\",\\\"output\\\":\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\",\\\"moduleNames\\\":\\\"entry,ijkplayer\\\",\\\"ResourceTable\\\":[\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\generated\\\\\\\\r\\\\\\\\default\\\\\\\\ResourceTable.h\\\"],\\\"applicationResource\\\":\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\AppScope\\\\\\\\resources\\\",\\\"moduleResources\\\":[\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\resources\\\"],\\\"dependencies\\\":[\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\ijkplayer\\\\\\\\src\\\\\\\\main\\\\\\\\resources\\\"],\\\"iconCheck\\\":false,\\\"ids\\\":\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\",\\\"definedIds\\\":\\\"D:\\\\\\\\new\\\\\\\\ohos_ijkplayer-2.0.3\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\\\\\\id_defined.json\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@ProcessResource", "_key": ":ohos_ijkplayer-2.0.3:entry:default@ProcessResource", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@ProcessResource:1753097816093", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "430d9e346f53e69bc3bc459bf57e976b"}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@CompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe,-l,D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@CompileResource", "_key": ":ohos_ijkplayer-2.0.3:entry:default@CompileResource", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@CompileResource:1753097816107", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "5e7003fbcac67fe637a6e5995cd48083"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\resources", {"fileSnapShotHashValue": "f8bb3cb772fc09333d7c014bad179b55"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\resources", {"fileSnapShotHashValue": "9de891437ab51ccd1bd0f2284dbf608a"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "f4596dcf111cf540a2053ebc15b11b47"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "430d9e346f53e69bc3bc459bf57e976b"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "696d44dec2f6a9b0f47d7a7eaff5b6b9"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", {"isDirectory": false, "fileSnapShotHashValue": "77bbc7861dcf8f8319a451c83e6f82f5"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "d70d00f3707158d44de3fb607acfc793"}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@CompileArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"@ohos/ijkplayer\",\"_value\":\"@ohos/ijkplayer: file:../ijkplayer\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@CompileArkTS", "_key": ":ohos_ijkplayer-2.0.3:entry:default@CompileArkTS", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@CompileArkTS:1753097816352", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "3fe82451bab96d1163b2641470f36f20"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\index.ets", {"fileSnapShotHashValue": "4bde852fd522b80f3179aef852e106a6"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "9a61bf7bc7235c2538a6f205784eca1b"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "a9d5e037330105652102c5c0c72fe37b"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"fileSnapShotHashValue": "822ea5fbdeff98c34b92e270fab2a1e7"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "82b0fa448ffa7a8905a14ee4c9f3e526"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "d4c153896f163b98a3aa8eee56e29272"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "c79805d2bea8db661ed7d646e4839990"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\BuildProfile.ets", {"fileSnapShotHashValue": "27e8e308ca9570fe7f367f90fd34b9dc"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":ohos_ijkplayer-2.0.3:entry:default@BuildJS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"@ohos/ijkplayer\",\"_value\":\"@ohos/ijkplayer: file:../ijkplayer\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "ohos_ijkplayer-2.0.3", "_moduleName": "entry", "_taskName": "default@BuildJS", "_key": ":ohos_ijkplayer-2.0.3:entry:default@BuildJS", "_executionId": ":ohos_ijkplayer-2.0.3:entry:default@BuildJS:1753097816388", "_inputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "3fe82451bab96d1163b2641470f36f20"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\index.ets", {"fileSnapShotHashValue": "4bde852fd522b80f3179aef852e106a6"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "a9d5e037330105652102c5c0c72fe37b"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"fileSnapShotHashValue": "822ea5fbdeff98c34b92e270fab2a1e7"}], ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "82b0fa448ffa7a8905a14ee4c9f3e526"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader_out\\default\\js", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}