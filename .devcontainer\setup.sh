#!/bin/bash

# GitHub Codespaces 环境设置脚本

echo "========================================"
echo "设置OpenHarmony ijkplayer开发环境"
echo "========================================"

# 更新包管理器
sudo apt-get update

# 安装必要的编译工具
echo "安装编译工具..."
sudo apt-get install -y \
    build-essential \
    gcc \
    g++ \
    make \
    cmake \
    autoconf \
    automake \
    libtool \
    pkg-config \
    git \
    wget \
    curl \
    unzip \
    zip \
    tar \
    gzip \
    bzip2 \
    xz-utils \
    python3 \
    python3-pip \
    ninja-build \
    meson \
    yasm \
    nasm \
    patch \
    flex \
    bison

echo "编译工具安装完成"

# 设置Git配置（如果需要）
echo "配置Git..."
git config --global init.defaultBranch main
git config --global pull.rebase false

# 创建必要的目录
echo "创建工作目录..."
mkdir -p /tmp/cache
mkdir -p /workspaces/sdk

# 设置环境变量
echo "设置环境变量..."
echo 'export OHOS_SDK="/workspaces/sdk/11"' >> ~/.bashrc
echo 'export PATH=$PATH:/workspaces/sdk/11/native/llvm/bin' >> ~/.bashrc

# 创建SDK下载脚本
cat > /workspaces/download_sdk.sh << 'EOF'
#!/bin/bash
echo "========================================"
echo "OpenHarmony SDK下载指南"
echo "========================================"
echo "由于SDK文件较大，需要手动下载："
echo ""
echo "1. 访问华为开发者联盟："
echo "   https://developer.harmonyos.com/cn/develop/deveco-studio"
echo ""
echo "2. 下载DevEco Studio并安装SDK"
echo ""
echo "3. 或者直接下载SDK："
echo "   https://repo.harmonyos.com/harmonyos/os/sdk/"
echo ""
echo "4. 将SDK上传到 /workspaces/sdk/11/ 目录"
echo ""
echo "5. 或者设置环境变量指向您的SDK路径："
echo "   export OHOS_SDK=/path/to/your/sdk"
EOF

chmod +x /workspaces/download_sdk.sh

# 显示完成信息
echo ""
echo "========================================"
echo "环境设置完成！"
echo "========================================"
echo ""
echo "下一步："
echo "1. 运行 /workspaces/download_sdk.sh 查看SDK下载指南"
echo "2. 设置OpenHarmony SDK路径"
echo "3. 运行 ./prebuild.sh 编译依赖库"
echo ""
echo "有用的命令："
echo "- 检查工具: gcc --version, make --version, cmake --version"
echo "- 运行编译: chmod +x prebuild.sh && ./prebuild.sh"
echo ""

# 显示系统信息
echo "系统信息:"
echo "- OS: $(lsb_release -d | cut -f2)"
echo "- CPU: $(nproc) cores"
echo "- Memory: $(free -h | grep '^Mem:' | awk '{print $2}')"
echo "- Disk: $(df -h / | tail -1 | awk '{print $4}') available"
echo ""
