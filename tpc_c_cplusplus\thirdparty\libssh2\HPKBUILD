# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=libssh2 
pkgver=1.11.0
pkgrel=0 
pkgdesc="libssh is a lightweight SSH client library." 
url="https://github.com/libssh2/libssh2" 
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD Licensed")
depends=("openssl") 
makedepends=()
install= 
source="https://github.com/$pkgname/$pkgname/archive/refs/tags/$pkgname-$pkgver.tar.gz"

downloadpackage=true
autounpack=true
patchflag=true
buildtools="cmake"
builddir=$pkgname-$pkgname-${pkgver}
packagename=$builddir.tar.gz 

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L >  $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

# 打包安装
package() {
    cd $builddir
    make -C $ARCH-build install >>  $buildlog 2>&1
    cd $OLDPWD
}

# 进行测试的准备和说明
check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
