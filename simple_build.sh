#!/bin/bash

# 简化的编译脚本 - 不依赖lycium工具链
# 直接使用源码进行基础配置

echo "========================================"
echo "简化编译方案"
echo "========================================"

set -e

ROOT_DIR=$(pwd)
TARGET_DIR="$ROOT_DIR/ijkplayer/src/main/cpp/third_party"
TEMP_DIR="$ROOT_DIR/temp_downloads"

echo "项目根目录: $ROOT_DIR"
echo "目标目录: $TARGET_DIR"
echo "源码目录: $TEMP_DIR"

# 检查源码是否存在
echo ""
echo "检查源码目录..."
for src in ffmpeg_src soundtouch_src libyuv_src openssl_src; do
    if [ -d "$TEMP_DIR/$src" ]; then
        file_count=$(find "$TEMP_DIR/$src" -type f | wc -l)
        echo "✓ $src: $file_count 个文件"
    else
        echo "✗ $src: 目录不存在"
    fi
done

# 创建目标目录结构
echo ""
echo "创建目标目录结构..."
mkdir -p "$TARGET_DIR/ffmpeg/ffmpeg"
mkdir -p "$TARGET_DIR/soundtouch"
mkdir -p "$TARGET_DIR/yuv"
mkdir -p "$TARGET_DIR/openssl"

echo "目标目录创建完成"

# 方案1：直接复制源码（用于开发和调试）
echo ""
echo "========================================"
echo "方案1: 复制源码到目标目录"
echo "========================================"
echo "注意: 这不是完整的编译，只是将源码复制到正确位置"
echo "用于让项目能够找到头文件，但可能缺少编译后的库文件"

read -p "是否执行源码复制方案？(y/N): " copy_source
if [[ "$copy_source" =~ ^[Yy]$ ]]; then
    
    # 复制FFmpeg源码
    if [ -d "$TEMP_DIR/ffmpeg_src" ]; then
        echo "复制FFmpeg源码..."
        # 保留原有的config.h
        if [ -f "$TARGET_DIR/ffmpeg/config.h" ]; then
            cp "$TARGET_DIR/ffmpeg/config.h" "$TEMP_DIR/config.h.backup"
        fi
        
        cp -r "$TEMP_DIR/ffmpeg_src"/* "$TARGET_DIR/ffmpeg/ffmpeg/"
        
        # 恢复config.h
        if [ -f "$TEMP_DIR/config.h.backup" ]; then
            cp "$TEMP_DIR/config.h.backup" "$TARGET_DIR/ffmpeg/config.h"
            rm "$TEMP_DIR/config.h.backup"
        fi
        
        echo "✓ FFmpeg源码复制完成"
    fi
    
    # 复制soundtouch源码
    if [ -d "$TEMP_DIR/soundtouch_src" ]; then
        echo "复制soundtouch源码..."
        cp -r "$TEMP_DIR/soundtouch_src"/* "$TARGET_DIR/soundtouch/"
        echo "✓ soundtouch源码复制完成"
    fi
    
    # 复制libyuv源码
    if [ -d "$TEMP_DIR/libyuv_src" ]; then
        echo "复制libyuv源码..."
        cp -r "$TEMP_DIR/libyuv_src"/* "$TARGET_DIR/yuv/"
        echo "✓ libyuv源码复制完成"
    fi
    
    # 复制OpenSSL源码
    if [ -d "$TEMP_DIR/openssl_src" ]; then
        echo "复制OpenSSL源码..."
        cp -r "$TEMP_DIR/openssl_src"/* "$TARGET_DIR/openssl/"
        echo "✓ OpenSSL源码复制完成"
    fi
    
    echo ""
    echo "源码复制完成！"
fi

# 方案2：尝试使用系统工具进行简单编译
echo ""
echo "========================================"
echo "方案2: 尝试简单编译"
echo "========================================"
echo "注意: 这将尝试使用系统的编译工具进行基础编译"
echo "可能不会生成OpenHarmony兼容的库文件"

read -p "是否尝试简单编译？(y/N): " simple_compile
if [[ "$simple_compile" =~ ^[Yy]$ ]]; then
    
    # 尝试编译soundtouch（相对简单）
    if [ -d "$TEMP_DIR/soundtouch_src" ]; then
        echo "尝试编译soundtouch..."
        cd "$TEMP_DIR/soundtouch_src"
        
        if [ -f "CMakeLists.txt" ]; then
            echo "使用CMake编译soundtouch..."
            mkdir -p build
            cd build
            if cmake .. && make -j$(nproc); then
                echo "✓ soundtouch编译成功"
                # 复制编译结果
                find . -name "*.a" -exec cp {} "$TARGET_DIR/soundtouch/" \;
                find . -name "*.so" -exec cp {} "$TARGET_DIR/soundtouch/" \;
            else
                echo "✗ soundtouch编译失败"
            fi
            cd ..
        fi
        
        cd "$ROOT_DIR"
    fi
    
    # 尝试编译libyuv
    if [ -d "$TEMP_DIR/libyuv_src" ]; then
        echo "尝试编译libyuv..."
        cd "$TEMP_DIR/libyuv_src"
        
        if [ -f "CMakeLists.txt" ]; then
            echo "使用CMake编译libyuv..."
            mkdir -p build
            cd build
            if cmake .. && make -j$(nproc); then
                echo "✓ libyuv编译成功"
                # 复制编译结果
                find . -name "*.a" -exec cp {} "$TARGET_DIR/yuv/" \;
                find . -name "*.so" -exec cp {} "$TARGET_DIR/yuv/" \;
            else
                echo "✗ libyuv编译失败"
            fi
            cd ..
        fi
        
        cd "$ROOT_DIR"
    fi
fi

# 显示结果
echo ""
echo "========================================"
echo "处理完成"
echo "========================================"

echo "检查目标目录内容："
for dir in ffmpeg/ffmpeg soundtouch yuv openssl; do
    if [ -d "$TARGET_DIR/$dir" ]; then
        file_count=$(find "$TARGET_DIR/$dir" -type f | wc -l)
        echo "- $dir: $file_count 个文件"
    fi
done

echo ""
echo "========================================"
echo "重要说明"
echo "========================================"
echo "1. 这是一个简化方案，可能无法完全替代lycium工具链"
echo "2. 生成的库文件可能不兼容OpenHarmony平台"
echo "3. 建议在Linux环境下使用原始的prebuild.sh脚本"
echo "4. 或者寻找预编译的OpenHarmony兼容库文件"
echo ""
echo "如果您需要完整的OpenHarmony兼容编译，建议："
echo "- 使用Linux虚拟机或WSL"
echo "- 或者寻找预编译的依赖库"
echo "- 或者联系项目维护者获取编译好的库文件"
