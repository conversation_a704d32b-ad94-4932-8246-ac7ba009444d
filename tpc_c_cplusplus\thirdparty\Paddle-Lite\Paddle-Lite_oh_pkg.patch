diff -Naur <PERSON>ddle-Lite-2.12_old/cmake/os/common.cmake Paddle-Lite-2.12/cmake/os/common.cmake
--- Paddle-Lite-2.12_old/cmake/os/common.cmake	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/cmake/os/common.cmake	2023-08-10 16:42:51.394850737 +0800
@@ -16,7 +16,7 @@
 
 # Arm config
 if(LITE_WITH_ARM)
-  set(ARM_TARGET_OS_LIST "android" "armlinux" "ios" "ios64" "armmacos" "qnx")
+  set(ARM_TARGET_OS_LIST "android" "armlinux" "ios" "ios64" "armmacos" "qnx" "ohos")
   set(ARM_TARGET_ARCH_ABI_LIST "armv8" "armv7" "armv7hf" "arm64-v8a" "armeabi-v7a")
   set(ARM_TARGET_LANG_LIST "gcc" "clang")
   set(ARM_TARGET_LIB_TYPE_LIST "static" "shared")
@@ -72,6 +72,9 @@
   if(ARM_TARGET_OS STREQUAL "android")
     include(os/android)
   endif()
+  if(ARM_TARGET_OS STREQUAL "ohos")
+    include(os/ohos)
+  endif()
   if(ARM_TARGET_OS STREQUAL "armlinux")
     include(os/armlinux)
   endif()
@@ -129,7 +132,7 @@
 endif()
 
 # TODO(Superjomn) Remove WITH_ANAKIN option if not needed latter.
-if(ANDROID OR IOS OR ARMLINUX OR ARMMACOS)
+if(ANDROID OR IOS OR ARMLINUX OR ARMMACOS OR OHOS)
   set(WITH_DSO OFF CACHE STRING
     "Disable DSO when cross-compiling for Android and iOS" FORCE)
   set(WITH_AVX OFF CACHE STRING
@@ -141,7 +144,7 @@
 endif()
 
 # Python
-if(ANDROID OR IOS)
+if(ANDROID OR IOS OR OHOS)
   set(LITE_WITH_PYTHON OFF CACHE STRING
     "Disable PYTHON when cross-compiling for Android and iOS" FORCE)
 endif()
--- Paddle-Lite-2.12_old/cmake/os/ohos.cmake	1970-01-01 08:00:00.000000000 +0800
+++ Paddle-Lite-2.12/cmake/os/ohos.cmake	2023-08-23 13:00:26.603645441 +0800
@@ -0,0 +1,31 @@
+# Copyright (c) 2018 PaddlePaddle Authors. All Rights Reserved.
+
+# Licensed under the Apache License, Version 2.0 (the "License");
+# you may not use this file except in compliance with the License.
+# You may obtain a copy of the License at
+
+#     http://www.apache.org/licenses/LICENSE-2.0
+
+# Unless required by applicable law or agreed to in writing, software
+# distributed under the License is distributed on an "AS IS" BASIS,
+# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
+# See the License for the specific language governing permissions and
+# limitations under the License.
+
+include(${OHOS_SDK}/native/build/cmake/ohos.toolchain.cmake)
+
+if(LITE_WITH_OPENMP)
+    set(OpenMP_C_FLAGS "-fopenmp")
+    set(OpenMP_C_LIB_NAMES "omp")
+    set(OpenMP_CXX_FLAGS "-fopenmp")
+    set(OpenMP_CXX_LIB_NAMES "omp")
+    set(OpenMP_omp_LIBRARY omp)
+    set(OpenMP_C_FLAGS_WORK "-fopenmp")
+    set(OpenMP_C_LIB_NAMES_WORK "omp")
+    set(OpenMP_CXX_FLAGS_WORK "-fopenmp")
+    set(OpenMP_CXX_LIB_NAMES_WORK "omp")
+endif()
+
+# Definitions
+add_definitions(-DLITE_WITH_LINUX)
+add_definitions(-DLITE_WITH_OHOS)
diff -Naur Paddle-Lite-2.12_old/cmake/backends/common.cmake Paddle-Lite-2.12/cmake/backends/common.cmake
--- Paddle-Lite-2.12_old/cmake/backends/common.cmake	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/cmake/backends/common.cmake	2023-08-10 16:42:51.390850665 +0800
@@ -45,7 +45,7 @@
   include(ccache)               # set ccache for compilation
   include(util)                 # set unittest and link libs
   include(version)              # set PADDLE_VERSION
-  if(NOT APPLE)
+  if(NOT (APPLE OR OHOS))
     include(flags)
   endif()
   set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-O3 -g -DNDEBUG")
diff -Naur Paddle-Lite-2.12_old/cmake/external/gflags.cmake Paddle-Lite-2.12/cmake/external/gflags.cmake
--- Paddle-Lite-2.12_old/cmake/external/gflags.cmake	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/cmake/external/gflags.cmake	2023-08-10 16:42:51.390850665 +0800
@@ -15,6 +15,14 @@
 if (NOT EMSCRIPTEN)
 INCLUDE(ExternalProject)
 
+if (OHOS)
+find_package(gflags)
+message("GFLAGS_FOUND: ${gflags_FOUND},${GFLAGS_INCLUDE_DIR}")
+INCLUDE_DIRECTORIES(${GFLAGS_INCLUDE_DIR})
+SET_PROPERTY(TARGET gflags PROPERTY IMPORTED_LOCATION ${GFLAGS_LIBRARIES})
+return()
+endif()
+
 SET(GFLAGS_SOURCES_DIR ${PADDLE_SOURCE_DIR}/third-party/gflags)
 SET(GFLAGS_INSTALL_DIR ${THIRD_PARTY_PATH}/install/gflags)
 SET(GFLAGS_INCLUDE_DIR "${GFLAGS_INSTALL_DIR}/include" CACHE PATH "gflags include directory." FORCE)
diff -Naur Paddle-Lite-2.12_old/cmake/external/protobuf.cmake Paddle-Lite-2.12/cmake/external/protobuf.cmake
--- Paddle-Lite-2.12_old/cmake/external/protobuf.cmake	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/cmake/external/protobuf.cmake	2023-08-10 16:42:51.390850665 +0800
@@ -141,7 +141,7 @@
     SET(PROTOBUF_ROOT ${THIRD_PARTY_PATH}/install/protobuf)
 ENDIF(WIN32)
 
-if (NOT "${PROTOBUF_ROOT}" STREQUAL "")
+if (WIN32)
     find_path(PROTOBUF_INCLUDE_DIR google/protobuf/message.h PATHS ${PROTOBUF_ROOT}/include NO_DEFAULT_PATH)
     find_library(PROTOBUF_LIBRARY protobuf libprotobuf.lib PATHS ${PROTOBUF_ROOT}/lib NO_DEFAULT_PATH)
     find_library(PROTOBUF_LITE_LIBRARY protobuf-lite libprotobuf-lite.lib PATHS ${PROTOBUF_ROOT}/lib NO_DEFAULT_PATH)
@@ -231,7 +231,7 @@
             ${TARGET_NAME}
             ${EXTERNAL_PROJECT_LOG_ARGS}
             PREFIX          ${PROTOBUF_SOURCES_DIR}
-            SOURCE_SUBDIR   cmake
+           # SOURCE_SUBDIR   cmake
             UPDATE_COMMAND  ""
 	    PATCH_COMMAND   ${PATCH_COMMAND}
             GIT_REPOSITORY  ""
@@ -286,10 +286,26 @@
 SET(PROTOBUF_VERSION 3.3.0)
 
 IF(LITE_WITH_ARM)
-    build_protobuf(protobuf_host TRUE)
-    LIST(APPEND external_project_dependencies protobuf_host)
-    SET(PROTOBUF_PROTOC_EXECUTABLE ${protobuf_host_PROTOC_EXECUTABLE}
-        CACHE FILEPATH "protobuf executable." FORCE)
+    IF(OHOS)
+        find_path(PROTOBUF_INCLUDE_DIR google/protobuf/message.h PATHS ${PROTOBUF_ROOT}/include NO_DEFAULT_PATH)
+        find_library(PROTOBUF_LIBRARY protobuf libprotobuf.a PATHS ${PROTOBUF_ROOT}/lib NO_DEFAULT_PATH)
+        find_library(PROTOBUF_LITE_LIBRARY protobuf-lite libprotobuf-lite.a PATHS ${PROTOBUF_ROOT}/lib NO_DEFAULT_PATH)
+        find_library(PROTOBUF_PROTOC_LIBRARY protoc libprotoc.a PATHS ${PROTOBUF_ROOT}/lib NO_DEFAULT_PATH)
+        SET(PROTOBUF_PROTOC_EXECUTABLE ${protobuf_host_PROTOC_EXECUTABLE})
+        IF (PROTOBUF_INCLUDE_DIR AND PROTOBUF_LIBRARY AND PROTOBUF_LITE_LIBRARY AND PROTOBUF_PROTOC_LIBRARY AND PROTOBUF_PROTOC_EXECUTABLE)
+            message(STATUS "Using custom protobuf library in ${PROTOBUF_ROOT}.")
+            SET(PROTOBUF_FOUND true)
+            SET_PROTOBUF_VERSION()
+            PROMPT_PROTOBUF_LIB()
+        ELSE()
+            message(WARNING "Cannot find protobuf library in ${PROTOBUF_ROOT}")
+        ENDIF()
+    ELSE()
+        build_protobuf(protobuf_host TRUE)
+        LIST(APPEND external_project_dependencies protobuf_host)
+        SET(PROTOBUF_PROTOC_EXECUTABLE ${protobuf_host_PROTOC_EXECUTABLE}
+            CACHE FILEPATH "protobuf executable." FORCE)
+    ENDIF()
 ENDIF()
 
 IF(NOT PROTOBUF_FOUND)
diff -Naur Paddle-Lite-2.12_old/cmake/functions.cmake Paddle-Lite-2.12/cmake/functions.cmake
--- Paddle-Lite-2.12_old/cmake/functions.cmake	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/cmake/functions.cmake	2023-08-10 16:42:51.386850593 +0800
@@ -167,7 +167,7 @@
           COMMAND ${TARGET} ${args_ARGS}
           WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR})
   # No unit test should exceed 10 minutes.
-  set_tests_properties(${TARGET} PROPERTIES TIMEOUT 1200)
+  set_tests_properties(${TARGET} PROPERTIES TIMEOUT 12000)
 
 endfunction()

diff -Naur Paddle-Lite-2.12_old/cmake/postproject.cmake Paddle-Lite-2.12/cmake/postproject.cmake
--- Paddle-Lite-2.12_old/cmake/postproject.cmake	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/cmake/postproject.cmake	2023-08-24 07:20:37.389326593 +0800
@@ -131,7 +131,11 @@
     set(CMAKE_SHARED_LINKER_FLAGS ${CMAKE_SHARED_LINKER_FLAGS} PARENT_SCOPE)
 endfunction()
 
-set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
+if (NOT OHOS)
+    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
+else()
+    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")
+endif()
 if((LITE_WITH_OPENCL AND (ARM_TARGET_LANG STREQUAL "clang")) OR LITE_WITH_PYTHON OR LITE_WITH_EXCEPTION OR (NOT LITE_ON_TINY_PUBLISH))
     set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fexceptions -fasynchronous-unwind-tables -funwind-tables")
 else ()
@@ -238,6 +242,17 @@
         "-D__ANDROID_API__=${ANDROID_NATIVE_API_LEVEL}"
         )
 endif()
+if(OHOS)
+    set(CROSS_COMPILE_CMAKE_ARGS ${CROSS_COMPILE_CMAKE_ARGS} 
+        "-DCMAKE_SYSTEM_PROCESSOR=${CMAKE_SYSTEM_PROCESSOR}"
+        "-DCMAKE_CXX_STANDARD=17"
+        "-DCMAKE_TOOLCHAIN_FILE=${OHOS_SDK}/native/build/cmake/ohos.toolchain.cmake"
+        "-DARM_TARGET_ARCH_ABI=${ARM_TARGET_ARCH_ABI}"
+        "-DOHOS_ARCH=${OHOS_ARCH}"
+        "-DCMAKE_EXE_LINKER_FLAGS=${CMAKE_EXE_LINKER_FLAGS}"
+        "-DCMAKE_SHARED_LINKER_FLAGS=${CMAKE_SHARED_LINKER_FLAGS}"
+        )
+endif() 
   
 if(IOS)
     if(LITE_WITH_ARM82_FP16)
diff -Naur Paddle-Lite-2.12_old/lite/api/CMakeLists.txt Paddle-Lite-2.12/lite/api/CMakeLists.txt
--- Paddle-Lite-2.12_old/lite/api/CMakeLists.txt	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/lite/api/CMakeLists.txt	2023-08-10 16:42:51.410851023 +0800
@@ -96,7 +96,7 @@
         set_target_properties(paddle_light_api_shared PROPERTIES LINK_FLAGS ${LINK_FLAGS})
         add_dependencies(paddle_light_api_shared custom_linker_map)
    endif()
-   if(NOT WIN32 AND NOT ARM_TARGET_OS STREQUAL "android" AND WITH_TESTING)
+   if(NOT WIN32 AND ((NOT ARM_TARGET_OS STREQUAL "android") OR (NOT ARM_TARGET_OS STREQUAL "ohos")) AND WITH_TESTING)
         # check symbol hidden
         FILE(WRITE ${CMAKE_CURRENT_BINARY_DIR}/check_symbol.cmake
             "execute_process(COMMAND sh -c \"${CMAKE_CURRENT_SOURCE_DIR}/../tools/check_symbol.sh"
--- Paddle-Lite-2.12_old/lite/api/test/CMakeLists.txt	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/lite/api/test/CMakeLists.txt	2023-08-10 16:42:51.414851095 +0800
@@ -41,9 +41,17 @@
 if(LITE_WITH_ARM AND WITH_TESTING)
     set(lite_model_test_DEPS cxx_api ops kernels)
 
+    if(OHOS)
+    message("test_mobilenetv1_int8 --model_dir=${LITE_MODEL_DIR}/mobilenet_v1_int8")
+    lite_cc_test(test_mobilenetv1_int8 SRCS mobilenetv1_int8_test.cc
+       ARGS --model_dir=${LITE_MODEL_DIR}/mobilenet_v1_int8 SERIAL)
+    add_dependencies(test_mobilenetv1_int8 extern_lite_download_mobilenet_v1_int8_tar_gz)
+    else()
+    message("test_mobilenetv1_int8 --model_dir=${LITE_MODEL_DIR}/MobilenetV1_quant")
     lite_cc_test(test_mobilenetv1_int8 SRCS mobilenetv1_int8_test.cc
        ARGS --model_dir=${LITE_MODEL_DIR}/MobilenetV1_quant SERIAL)
     add_dependencies(test_mobilenetv1_int8 extern_lite_download_MobileNetV1_quant_tar_gz)
+    endif()
 
     lite_cc_test(test_mobilenetv1 SRCS mobilenetv1_test.cc
        ARGS --model_dir=${LITE_MODEL_DIR}/mobilenet_v1 SERIAL)
@@ -72,11 +80,18 @@
        ARGS --model_dir=${LITE_MODEL_DIR}/resnet50 SERIAL)
     add_dependencies(test_resnet50 extern_lite_download_resnet50_tar_gz)
 
+    if(LITE_WITH_OPENCL)
     lite_cc_test(test_inceptionv4 SRCS inceptionv4_test.cc
        ARGS --model_dir=${LITE_MODEL_DIR}/inception_v4 SERIAL)
+    add_dependencies(test_inceptionv4 extern_lite_download_inception_v4_tar_gz)
+    else()
+    lite_cc_test(test_inceptionv4 SRCS inceptionv4_test.cc
+       ARGS --model_dir=${LITE_MODEL_DIR}/inception_v4_simple SERIAL)
     add_dependencies(test_inceptionv4 extern_lite_download_inception_v4_simple_tar_gz)
+    endif()
 
-    lite_cc_test(test_fast_rcnn SRCS fast_rcnn_test.cc)
+    lite_cc_test(test_fast_rcnn SRCS fast_rcnn_test.cc
+       ARGS --model_dir=${LITE_MODEL_DIR}/fast_rcnn_fluid184 SERIAL)
     add_dependencies(test_fast_rcnn extern_lite_download_fast_rcnn_fluid184_tar_gz)
 
    # brief: we comment ocr_test_ut because we do not supply ocr model to test, it is the reference to infer nlp model
diff -Naur Paddle-Lite-2.12_old/lite/CMakeLists.txt Paddle-Lite-2.12/lite/CMakeLists.txt
--- Paddle-Lite-2.12_old/lite/CMakeLists.txt	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/lite/CMakeLists.txt	2023-08-10 16:42:51.702856251 +0800
@@ -122,6 +122,7 @@
         lite_download_and_uncompress(${LITE_MODEL_DIR} ${LITE_URL_FOR_NNADAPTER_UNITTESTS} "ppTSN.tar.gz"     MODEL_PATH "PaddleVideo/v2.2.0")
     endif()
     if(LITE_WITH_ARM)
+        lite_download_and_uncompress(${LITE_MODEL_DIR} ${LITE_URL} "mobilenet_v1_int8.tar.gz")
 	    lite_download_and_uncompress(${LITE_MODEL_DIR} ${LITE_URL} "mobilenet_v1_int16.tar.gz")
         lite_download_and_uncompress(${LITE_MODEL_DIR} ${LITE_URL} "resnet50.tar.gz")
         lite_download_and_uncompress(${LITE_MODEL_DIR} ${LITE_URL} "MobileNetV1_quant.tar.gz")
@@ -422,7 +423,7 @@
                 add_dependencies(publish_inference_cxx_lib bundle_light_api)
                 add_dependencies(publish_inference_cxx_lib test_model_bin)
                 add_dependencies(publish_inference_cxx_lib benchmark_bin)
-                if (ARM_TARGET_OS STREQUAL "android" OR ARM_TARGET_OS STREQUAL "armlinux" OR ARM_TARGET_OS STREQUAL "armmacos" OR ARM_TARGET_OS STREQUAL "qnx")
+                if (ARM_TARGET_OS STREQUAL "android" OR ARM_TARGET_OS STREQUAL "ohos" OR ARM_TARGET_OS STREQUAL "armlinux" OR ARM_TARGET_OS STREQUAL "armmacos" OR ARM_TARGET_OS STREQUAL "qnx")
                     add_dependencies(publish_inference_cxx_lib paddle_full_api_shared)
                     add_dependencies(publish_inference paddle_light_api_shared)
                     add_custom_command(TARGET publish_inference_cxx_lib
@@ -457,7 +458,7 @@
             add_dependencies(tiny_publish_cxx_lib paddle_api_light_bundled)
             add_dependencies(publish_inference tiny_publish_cxx_lib)
         else()
-            if ((ARM_TARGET_OS STREQUAL "android") OR (ARM_TARGET_OS STREQUAL "armlinux") OR (ARM_TARGET_OS STREQUAL "qnx"))
+            if ((ARM_TARGET_OS STREQUAL "android") OR (ARM_TARGET_OS STREQUAL "ohos") OR (ARM_TARGET_OS STREQUAL "armlinux") OR (ARM_TARGET_OS STREQUAL "qnx"))
                 # compile cplus shared library, pack the cplus demo and lib into the publish directory.
                 add_custom_target(tiny_publish_cxx_lib ${TARGET}
                     COMMAND mkdir -p "${INFER_LITE_PUBLISH_ROOT}/cxx"
diff -Naur Paddle-Lite-2.12_old/lite/backends/arm/math/gemm_prepacked_int8.cc Paddle-Lite-2.12/lite/backends/arm/math/gemm_prepacked_int8.cc
--- Paddle-Lite-2.12_old/lite/backends/arm/math/gemm_prepacked_int8.cc	2022-11-17 10:44:14.000000000 +0800
+++ Paddle-Lite-2.12/lite/backends/arm/math/gemm_prepacked_int8.cc	2023-08-18 10:40:07.077238261 +0800
@@ -4598,7 +4598,11 @@
   int8x16_t vzero = vdupq_n_s8(0);
   uint8x16_t vmask = vcltq_u8(vld1q_u8(mask_buffer), vdupq_n_u8(x_rem));
 
-  int stride_out = ylen_roundup * MBLOCK_INT8_OTH;
+#ifdef __aarch64__
+  int64_t stride_out = ylen_roundup * MBLOCK_INT8_OTH;
+#else
+  int32_t stride_out = ylen_roundup * MBLOCK_INT8_OTH;
+#endif
 
   int8_t* zerobuf = static_cast<int8_t*>(malloc(xlen_roundup));
   memset(zerobuf, 0, xlen_roundup);
@@ -4919,7 +4923,11 @@
   int kup = ROUNDUP(y_len, KBLOCK_INT8);
   int kcnt = x_len / NBLOCK_INT8_OTH;
   int rem = x_len & (NBLOCK_INT8_OTH - 1);
-  int stride_out = NBLOCK_INT8_OTH * kup;
+#ifdef __aarch64__
+  int64_t stride_out = NBLOCK_INT8_OTH * kup;
+#else
+  int32_t stride_out = NBLOCK_INT8_OTH * kup;
+#endif
 
   int8x16_t vzero = vdupq_n_s8(0);
   uint8x16_t vmask = vcltq_u8(vld1q_u8(mask_buffer), vdupq_n_u8(rem));

