# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=libical
pkgver=v3.0.16
pkgrel=0
pkgdesc="Libical is an Open Source implementation of the iCalendar protocols and protocol data units."
url="https://github.com/libical/libical"
archs=("armeabi-v7a" "arm64-v8a")
license=("MPL v2.0 or LGPL v2.1")
depends=("icu" "libxml2")
makedepends=()
# 原仓地址: https://github.com/libical/$pkgname/archive/refs/tags/$pkgver.tar.gz, 因网络原因使用镜像
source="https://gitee.com/lycium_pkg_mirror/$pkgname/repository/archive/$pkgver.zip"

autounpack=true
downloadpackage=true
patchflag=true

builddir=$pkgname-$pkgver
packagename=$pkgname-$pkgver.zip

prepare() {
    # 开发板用cmake -P 执行脚本始终报错，拆成两个指令可正常运行
    if [ $patchflag == true ];then
        cd $builddir
        patch -p1 < `pwd`/../libical_oh_pkg.patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    # 关闭ical_glib, README.md介绍说暂时不稳定
    # 使用内建时区数据，USE_BUILTIN_TZDATA=True
    PKG_CONFIG_PATH="${pkgconfigpath}" ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" \
        -DICAL_GLIB=False -DENABLE_GTK_DOC=False -DUSE_BUILTIN_TZDATA=True -DSTATIC_ONLY=True \
        -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L  > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    if [ $ARCH == "armeabi-v7a" ];then
        cp -af $OHOS_SDK/native/llvm/lib/arm-linux-ohos/libc++_shared.so $builddir/$ARCH-build/
    elif [ $ARCH == "arm64-v8a" ];then
        cp -af $OHOS_SDK/native/llvm/lib/aarch64-linux-ohos/libc++_shared.so $builddir/$ARCH-build/
    else
        echo "$ARCH not support"
        return -1
    fi
    echo "The test must be on an OpenHarmony device!"
    # real test
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}