--- AES-44407f634f11992404596129ec31545e3f268ad3/Makefile	2024-06-06 10:18:30.432850300 +0800
+++ AES-44407f634f11992404596129ec31545e3f268ad3/Makefile2	2024-06-06 10:20:32.192850300 +0800
@@ -0,0 +1,18 @@
+.PHONY:clean default install
+clang=
+ifeq ($(arch),armeabi-v7a)
+	clang=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang
+else
+	clang=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang
+endif
+
+# =====================  默认构建，生成测试文件  =====================
+default:../gmult.c ../aes.c ../main.c
+	$(clang) -o aes $^
+
+# =====================  生成动态库文件  =====================
+install:../gmult.c ../aes.c
+	$(clang) -Wall -fPIC -shared -o libaes.so $^
+
+clean:
+	rm *
