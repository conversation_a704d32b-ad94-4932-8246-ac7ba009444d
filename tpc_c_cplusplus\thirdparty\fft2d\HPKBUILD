# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=fft2d
pkgver=v2
pkgrel=0
pkgdesc="A package to calculate Discrete Fourier/Cosine/Sine Transforms of 1-dimensional sequences of length 2^N."
url="https://www.kurims.kyoto-u.ac.jp/~ooura/fft.html"
archs=("armeabi-v7a" "arm64-v8a")
license=("Free Software License")
depends=()
makedepends=()

source="https://www.kurims.kyoto-u.ac.jp/~ooura/$pkgname.tgz"

downloadpackage=true
autounpack=true

builddir=$pkgname
packagename=$builddir.tgz
patchflag=true

prepare() {
    mkdir -p $builddir/$ARCH-build
    
    if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../fft2d_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
}

build() {
    ret=0
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir
}
