# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, Sunjiamei<<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=libtomcrypt
pkgver=1.18.2
pkgrel=0
pkgdesc="libtomcrypt is a modular, portable, free and open source cryptographic library that provides implementations of various cryptographic algorithms, including symmetric cryptographic algorithms (e.g. AES, DES), public key cryptographic algorithms (e.g. RSA, ECC), hash algorithms (e.g. SHA-256, MD5), etc."
url="https://github.com/libtom/libtomcrypt"
archs=("armeabi-v7a" "arm64-v8a")
license=("DUAL licensing terms")
depends=("libtommath")
makedepends=()

source="https://github.com/libtom/$pkgname/archive/refs/tags/v$pkgver.zip"

autounpack=true
downloadpackage=true
buildtools="make"

builddir=$pkgname-${pkgver}
packagename=$builddir.zip

source envset.sh
patchflag=true

# libtomcrypt 采用makefile编译构建，为了保留构建环境(方便测试)。因此同一份源码在解压后分为两份,各自编译互不干扰
prepare() {
    cp -rf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
    fi
}

build() {
    cd $builddir-$ARCH-build
    $MAKE CFLAGS="-DUSE_LTM -DLTM_DESC -I${LYCIUM_ROOT}/usr/libtommath/$ARCH/include" EXTRALIBS="${LYCIUM_ROOT}/usr/libtommath/$ARCH/lib/libtommath.a" test > $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    $MAKE install PREFIX=$LYCIUM_ROOT/usr/$pkgname/$ARCH >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

cleanbuild() {
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build
}
