# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>
pkgname=CUnit
pkgver=2.1-3
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=()
depends=()
makedepends=()
install=
source="https://sourceforge.net/projects/cunit/files/$pkgname/$pkgver/$pkgname-$pkgver.tar.bz2"

autounpack=false
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.bz2

source envset.sh
host=
prepare() {
    mkdir $pkgname-$ARCH-build
    tar -jxf $packagename -C $pkgname-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
    cd $pkgname-$ARCH-build/$builddir
    ./bootstrap > `pwd`/build.log 2>&1
    cd $OLDPWD
}

build() {
    cd $pkgname-$ARCH-build/$builddir
    ./configure "$@" --host=$host --enable-debug --enable-automated --enable-basic --enable-console --enable-examples --enable-test >> `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $pkgname-$ARCH-build/$builddir
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi

    unset host
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 在OpenHarmony开发板中执行用例
    # ./test_cunit
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$pkgname-armeabi-v7a-build ${PWD}/$pkgname-arm64-v8a-build #${PWD}/$packagename
}
