# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=libvips
pkgver=v8.14.5
pkgrel=0
pkgdesc="libvips is a demand-driven, horizontally threaded image processing library."
url="https://github.com/libvips/libvips"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL-2.1")
depends=("glib" "pcre2" "libffi" "libexpat" "libpng" "libjpeg-turbo" "zlib") 
makedepends=("meson" "ninja")

source="https://codeload.github.com/$pkgname/$pkgname/tar.gz/refs/tags/$pkgver"

downloadpackage=true
autounpack=true
buildtools="meson"

builddir=$pkgname-${pkgver:1}
packagename=$pkgname-${pkgver:1}.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
    cp $ARCH-cross-file.txt $builddir
}

build() {
    cd $builddir
    ohos_sdk_path=${OHOS_SDK//\//\\\/}
    sed -i 's/ohos_sdk/'"$ohos_sdk_path"'/g' $ARCH-cross-file.txt
    lycium_root_path=${LYCIUM_ROOT//\//\\\/}
    sed -i 's/lycium_root/'"$lycium_root_path"'/g' $ARCH-cross-file.txt

    meson setup $ARCH-build -D introspection=false -D jpeg="enabled" -D png="enabled" \
        --cross-file $ARCH-cross-file.txt \
        --prefix=$LYCIUM_ROOT/usr/$pkgname/$ARCH > $buildlog 2>&1
    $Ninja -v -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $Ninja -v -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 逐个执行测试用例
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir
}
