# Contributor: wuping<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=fdk-aac
pkgver=v2.0.2
pkgrel=0
pkgdesc="A standalone library of the Fraunhofer FDK AAC code from Android"
url="https://github.com/mstorsjo/fdk-aac"
archs=("armeabi-v7a" "arm64-v8a")
license=("LicenseRef-scancode-fraunhofer-fdk-aac-codec")
depends=()
makedepends=()

source="https://github.com/mstorsjo/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DBUILD_SHARED_LIBS=OFF -DBUILD_PROGRAMS=ON -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 在OpenHarmony开发板中执行用例
    # ./aac-enc test.wav out.aac
}

cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
