[{"Name": "cyrus-sasl", "License": "Carnegie Mellon Computing Services License", "License File": "COPYING", "Version Number": "2.1.28", "Owner": "<EMAIL>", "Upstream URL": "https://github.com/cyrusimap/cyrus-sasl/releases/download/cyrus-sasl-2.1.28/cyrus-sasl-2.1.28.tar.gz", "Description": "Simple Authentication and Security Layer (SASL) is a specification that describes how authentication mechanisms can be plugged into an application protocol on the wire. Cyrus SASL is an implementation of SASL that makes it easy for application developers to integrate authentication mechanisms into their application in a generic way."}]