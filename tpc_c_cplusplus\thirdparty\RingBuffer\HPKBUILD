# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=RingBuffer
pkgver=1.0.4
pkgrel=0
pkgdesc="环形缓冲器（ringr buffer），也称作圆形队列（circular queue），是一种用于表示一个固定尺寸、头尾相连的缓冲区的数据结构，适合缓存数据流。"
url="https://github.com/Locoduino/RingBuffer"
archs=("armeabi-v7a" "arm64-v8a")
license=("GPL-2.0 license")
depends=()
makedepends=()

source="https://github.com/Locoduino/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="cmake"
patchflag=true

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

prepare() {
    if [ "$patchflag" == true ]
    then
        cd $builddir
        # patch文件注释了中断功能，因为OHOS不支持，添加测试cpp文件和Cmake编译出测试可执行文件
        patch -p1 < ../RingBuffer_oh_pkg.patch
        cd $OLDPWD
        patchflag=false
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE VERBOSE=1 -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 测试方式
    # 进入构建目录
    # 执行: ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
