# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=libiconv
pkgver=1.17
pkgrel=0
pkgdesc="GNU libiconv provides an implementation of the iconv() function and the iconv program for character set conversion. For use on systems which don't have one, or whose implementation cannot convert from/to Unicode.
."
url="https://savannah.gnu.org/projects/libiconv/"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL 3.0")
depends=() 
makedepends=()

source="https://ftp.gnu.org/gnu/${pkgname}/${pkgname}-${pkgver}.tar.gz"

downloadpackage=true
autounpack=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$pkgname-${pkgver}.tar.gz
source envset.sh
host=

prepare() {
    cp -rf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
        export LDFLAGS="${OHOS_SDK}/native/sysroot/usr/lib/arm-linux-ohos/libc.a ${LDFLAGS}"
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
        export LDFLAGS="${OHOS_SDK}/native/sysroot/usr/lib/aarch64-linux-ohos/libc.a ${LDFLAGS}"
    else
        echo "can't support this archs:$ARCH"
        return -1
    fi

    return 0
}

build() {
    cd $builddir-$ARCH-build
    ./configure "$@" --host=$host >> $buildlog 2>&1
    $MAKE >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD

    return $ret
}

package() {
    cd $builddir-$ARCH-build
    make install >> $buildlog 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi

    unset host

    return 0
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test:
    # iconv -f fromCode -t desCode filename
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
    rm -rf ${PWD}/$builddir-armeabi-v7a-build
    rm -rf ${PWD}/$builddir-arm64-v8a-build
}
