#!/bin/bash

# 修复权限问题的脚本

echo "========================================"
echo "修复权限问题"
echo "========================================"

cd temp_downloads

echo "检查当前目录权限..."
ls -la

echo ""
echo "修复文件权限..."

# 修复所有文件和目录的权限
find . -type d -exec chmod 755 {} \; 2>/dev/null
find . -type f -exec chmod 644 {} \; 2>/dev/null

echo "权限修复完成"

echo ""
echo "========================================"
echo "重新尝试解压和重命名"
echo "========================================"

# 处理FFmpeg
if [ -d "FFmpeg-ff4.0-ijk0.8.8-20210426-001" ]; then
    echo "处理FFmpeg目录..."
    if [ -d "ffmpeg_src" ]; then
        echo "删除旧的ffmpeg_src目录..."
        rm -rf ffmpeg_src
    fi
    
    echo "复制FFmpeg目录..."
    if cp -r FFmpeg-ff4.0-ijk0.8.8-20210426-001 ffmpeg_src; then
        echo "✓ FFmpeg复制成功"
        rm -rf FFmpeg-ff4.0-ijk0.8.8-20210426-001
        echo "✓ 清理原目录完成"
    else
        echo "✗ FFmpeg复制失败"
    fi
else
    echo "FFmpeg目录不存在，跳过"
fi

# 处理soundtouch
soundtouch_dir=$(ls -d soundtouch-* 2>/dev/null | head -1)
if [ -n "$soundtouch_dir" ]; then
    echo "处理soundtouch目录: $soundtouch_dir"
    if [ -d "soundtouch_src" ]; then
        echo "删除旧的soundtouch_src目录..."
        rm -rf soundtouch_src
    fi
    
    echo "复制soundtouch目录..."
    if cp -r "$soundtouch_dir" soundtouch_src; then
        echo "✓ soundtouch复制成功"
        rm -rf "$soundtouch_dir"
        echo "✓ 清理原目录完成"
    else
        echo "✗ soundtouch复制失败"
    fi
else
    echo "soundtouch目录不存在，跳过"
fi

# 处理libyuv
libyuv_dir=$(ls -d libyuv-* 2>/dev/null | head -1)
if [ -n "$libyuv_dir" ]; then
    echo "处理libyuv目录: $libyuv_dir"
    if [ -d "libyuv_src" ]; then
        echo "删除旧的libyuv_src目录..."
        rm -rf libyuv_src
    fi
    
    echo "复制libyuv目录..."
    if cp -r "$libyuv_dir" libyuv_src; then
        echo "✓ libyuv复制成功"
        rm -rf "$libyuv_dir"
        echo "✓ 清理原目录完成"
    else
        echo "✗ libyuv复制失败"
    fi
else
    echo "libyuv目录不存在，跳过"
fi

# 处理OpenSSL
openssl_dir=$(ls -d openssl-* 2>/dev/null | head -1)
if [ -n "$openssl_dir" ]; then
    echo "处理OpenSSL目录: $openssl_dir"
    if [ -d "openssl_src" ]; then
        echo "删除旧的openssl_src目录..."
        rm -rf openssl_src
    fi
    
    echo "复制OpenSSL目录..."
    if cp -r "$openssl_dir" openssl_src; then
        echo "✓ OpenSSL复制成功"
        rm -rf "$openssl_dir"
        echo "✓ 清理原目录完成"
    else
        echo "✗ OpenSSL复制失败"
    fi
else
    echo "OpenSSL目录不存在，跳过"
fi

cd ..

echo ""
echo "========================================"
echo "最终检查"
echo "========================================"

# 检查最终结果
success_count=0
total_count=4

if [ -d "temp_downloads/ffmpeg_src" ]; then
    file_count=$(find temp_downloads/ffmpeg_src -type f | wc -l)
    echo "✓ FFmpeg: temp_downloads/ffmpeg_src ($file_count 个文件)"
    success_count=$((success_count + 1))
else
    echo "✗ FFmpeg: 目录不存在"
fi

if [ -d "temp_downloads/soundtouch_src" ]; then
    file_count=$(find temp_downloads/soundtouch_src -type f | wc -l)
    echo "✓ soundtouch: temp_downloads/soundtouch_src ($file_count 个文件)"
    success_count=$((success_count + 1))
else
    echo "✗ soundtouch: 目录不存在"
fi

if [ -d "temp_downloads/libyuv_src" ]; then
    file_count=$(find temp_downloads/libyuv_src -type f | wc -l)
    echo "✓ libyuv: temp_downloads/libyuv_src ($file_count 个文件)"
    success_count=$((success_count + 1))
else
    echo "✗ libyuv: 目录不存在"
fi

if [ -d "temp_downloads/openssl_src" ]; then
    file_count=$(find temp_downloads/openssl_src -type f | wc -l)
    echo "✓ OpenSSL: temp_downloads/openssl_src ($file_count 个文件)"
    success_count=$((success_count + 1))
else
    echo "✗ OpenSSL: 目录不存在"
fi

echo ""
echo "========================================"
echo "修复完成！"
echo "========================================"
echo "成功处理: $success_count/$total_count"

if [ $success_count -eq $total_count ]; then
    echo "🎉 所有源码都已成功处理！"
    echo ""
    echo "现在可以运行编译脚本："
    echo "export OHOS_SDK=\"/d/harmonyFor/openSDK/11\""
    echo "./build_msys2.sh"
elif [ $success_count -gt 0 ]; then
    echo "⚠ 部分源码处理成功，可以尝试继续编译"
else
    echo "❌ 处理失败，请检查权限设置"
fi
