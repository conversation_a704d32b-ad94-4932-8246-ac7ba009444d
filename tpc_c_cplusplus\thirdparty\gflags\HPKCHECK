# Contributor: wuping<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
OLDPATH=$LD_LIBRARY_PATH
checkprepare() {
    # 添加动态库libc++_shared.so所在路径到环境变量
    export LD_LIBRARY_PATH=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/$builddir/$ARCH-build:$LD_LIBRARY_PATH
}

openharmonycheck() {
    res=0
    cd ${builddir}/${ARCH}-build
    ctest > $logfile 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        mkdir ${LYCIUM_FAULT_PATH}/${pkgname}
        cp Testing/Temporary/LastTest.log ${LYCIUM_FAULT_PATH}/${pkgname}/
    fi  
    cd $OLDPWD
    # 恢复环境变量
    export LD_LIBRARY_PATH=$OLDPATH
    unset OLDPATH
    return $res
}
