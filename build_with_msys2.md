# 使用MSYS2编译OpenHarmony ijkplayer依赖库

## 概述
本指南将帮助您在Windows环境下使用MSYS2编译OpenHarmony ijkplayer所需的依赖库。

## 前提条件
- 已安装MSYS2
- 已安装OpenHarmony SDK
- 网络连接正常

## 步骤详解

### 1. 准备MSYS2环境

首先打开MSYS2终端，安装必要的工具：

```bash
# 更新包管理器
pacman -Syu

# 安装基础编译工具
pacman -S --needed base-devel mingw-w64-x86_64-toolchain
pacman -S git wget unzip cmake make autoconf automake libtool
pacman -S pkg-config python3 python3-pip
pacman -S mingw-w64-x86_64-cmake mingw-w64-x86_64-ninja
```

### 2. 设置环境变量

在MSYS2终端中设置必要的环境变量：

```bash
# 设置项目根目录 (请根据实际路径修改)
export ROOT_DIR="/d/new/ohos_ijkplayer-2.0.3"
export API_VERSION=11

# 设置OpenHarmony SDK路径 (请根据实际安装路径修改)
export OHOS_SDK="/d/path/to/ohos-sdk/windows/11"
# 或者如果SDK在其他位置
export OHOS_SDK="/c/Users/<USER>/AppData/Local/OpenHarmony/Sdk/11"

# 设置工作目录
cd "$ROOT_DIR"
```

### 3. 下载lycium编译工具链

```bash
# 下载lycium工具链
git clone https://gitee.com/openharmony-sig/tpc_c_cplusplus.git -b support_x86 --depth=1

# 进入lycium目录
cd tpc_c_cplusplus/lycium
```

### 4. 准备依赖库配置

```bash
# 返回项目根目录
cd "$ROOT_DIR"

# 复制依赖库配置到lycium thirdparty目录
cp -r doc/libyuv-ijk tpc_c_cplusplus/thirdparty/
cp -r doc/soundtouch-ijk tpc_c_cplusplus/thirdparty/
```

### 5. 手动下载依赖源码包

由于网络或权限问题，建议手动下载以下源码包：

#### FFmpeg源码包
```bash
# 方法1：使用git下载
git clone https://github.com/bilibili/FFmpeg.git -b ff4.0--ijk0.8.8--20210426--001 --depth=1 temp_ffmpeg

# 方法2：手动下载压缩包
# 访问 https://github.com/bilibili/FFmpeg/archive/refs/tags/ff4.0--ijk0.8.8--20210426--001.tar.gz
# 下载后解压到 temp_ffmpeg 目录
```

#### soundtouch源码包
```bash
# 方法1：使用git下载
git clone https://github.com/bilibili/soundtouch.git -b ijk-r0.1.2-dev --depth=1 temp_soundtouch

# 方法2：手动下载
# 访问 https://github.com/bilibili/soundtouch/archive/refs/heads/ijk-r0.1.2-dev.zip
```

#### libyuv源码包
```bash
# 方法1：使用git下载
git clone https://github.com/bilibili/libyuv.git -b ijk-r0.2.1-dev --depth=1 temp_libyuv

# 方法2：手动下载
# 访问 https://github.com/bilibili/libyuv/archive/refs/heads/ijk-r0.2.1-dev.zip
```

#### OpenSSL源码包
```bash
# 下载OpenSSL
git clone https://github.com/openssl/openssl.git -b OpenSSL_1_1_1w --depth=1 temp_openssl
```

### 6. 创建源码包

将下载的源码打包成lycium工具链期望的格式：

```bash
# 创建FFmpeg源码包
cd temp_ffmpeg
tar -czf ../tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/FFmpeg-ff4.0-ijk0.8.8-20210426-001.tar.gz .
cd ..

# 创建soundtouch源码包
cd temp_soundtouch
zip -r ../tpc_c_cplusplus/thirdparty/soundtouch-ijk/soundtouch-ijk-r0.1.2-dev.zip .
cd ..

# 创建libyuv源码包
cd temp_libyuv
zip -r ../tpc_c_cplusplus/thirdparty/libyuv-ijk/yuv-ijk-r0.2.1-dev.zip .
cd ..

# 创建OpenSSL源码包
cd temp_openssl
zip -r ../tpc_c_cplusplus/thirdparty/openssl_1_1_1w/openssl-OpenSSL_1_1_1w.zip .
cd ..
```

### 7. 修改编译脚本

由于Windows环境的特殊性，需要修改一些脚本：

```bash
# 进入lycium目录
cd tpc_c_cplusplus/lycium

# 检查build.sh脚本是否存在
ls -la build.sh

# 如果需要，修改脚本中的路径分隔符和命令
```

### 8. 执行编译

```bash
# 在lycium目录下执行编译
./build.sh FFmpeg-ff4.0 libyuv-ijk soundtouch-ijk
```

### 9. 复制编译结果

编译完成后，将结果复制到项目目录：

```bash
# 返回项目根目录
cd "$ROOT_DIR"

# 复制编译结果
cp -r tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0 ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg
cp -r tpc_c_cplusplus/lycium/usr/yuv ijkplayer/src/main/cpp/third_party/yuv
cp -r tpc_c_cplusplus/lycium/usr/soundtouch ijkplayer/src/main/cpp/third_party/soundtouch
cp -r tpc_c_cplusplus/lycium/usr/openssl_1_1_1w ijkplayer/src/main/cpp/third_party/openssl
```

## 故障排除

### 常见问题

1. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x build.sh
   chmod +x prebuild.sh
   ```

2. **路径问题**
   ```bash
   # 使用Unix风格路径
   # Windows: D:\path\to\file
   # MSYS2:   /d/path/to/file
   ```

3. **工具链问题**
   ```bash
   # 检查必要工具是否安装
   which gcc
   which make
   which cmake
   ```

### 验证安装

检查以下目录是否包含编译结果：
- `ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/include/`
- `ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/lib/`
- `ijkplayer/src/main/cpp/third_party/soundtouch/include/`
- `ijkplayer/src/main/cpp/third_party/soundtouch/lib/`
- `ijkplayer/src/main/cpp/third_party/yuv/include/`
- `ijkplayer/src/main/cpp/third_party/yuv/lib/`
- `ijkplayer/src/main/cpp/third_party/openssl/include/`
- `ijkplayer/src/main/cpp/third_party/openssl/lib/`

## 下一步

完成依赖库编译后，您可以：
1. 使用DevEco Studio打开项目
2. 编译整个ijkplayer项目
3. 在OpenHarmony设备上测试

## 注意事项

1. 确保所有路径使用Unix风格的斜杠 (/)
2. 某些步骤可能需要管理员权限
3. 编译过程可能需要较长时间
4. 如遇到问题，请检查MSYS2和OpenHarmony SDK的安装
