/*
 * Copyright (c) 2003 Bilibili
 * Copyright (c) 2003 Fabrice <PERSON>
 * Copyright (c) 2015 <PERSON> <<EMAIL>>
 *
 * This file is part of ijkPlayer.
 * Based on libavformat/allformats.c
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with <PERSON>mp<PERSON>; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include "libavformat/avformat.h"
#include "libavformat/url.h"
#include "libavformat/version.h"

#define IJK_REGISTER_DEMUXER(x)                                         \
    {                                                                   \
        extern AVInputFormat ijkff_##x##_demuxer;                       \
        ijkav_register_input_format(&ijkff_##x##_demuxer);              \
    }

#define IJK_REGISTER_PROTOCOL(x)                                        \
    {                                                                   \
        extern URLProtocol ijkimp_ff_##x##_protocol;                        \
        int ijkav_register_##x##_protocol(URLProtocol *protocol, int protocol_size);\
        ijkav_register_##x##_protocol(&ijkimp_ff_##x##_protocol, sizeof(URLProtocol));  \
    }

static struct AVInputFormat *ijkav_find_input_format(const char *iformat_name)
{
    AVInputFormat *fmt = NULL;
    if (!iformat_name)
        return NULL;
    while ((fmt = av_iformat_next(fmt))) {
        if (!fmt->name)
            continue;
        if (!strcmp(iformat_name, fmt->name))
            return fmt;
    }
    return NULL;
}

static void ijkav_register_input_format(AVInputFormat *iformat)
{
    if (ijkav_find_input_format(iformat->name)) {
        av_log(NULL, AV_LOG_WARNING, "skip     demuxer : %s (duplicated)\n", iformat->name);
    } else {
        av_log(NULL, AV_LOG_INFO,    "register demuxer : %s\n", iformat->name);
        av_register_input_format(iformat);
    }
}


void ijkav_register_all(void)
{
    av_log(NULL, AV_LOG_VERBOSE, "===== ijkav_register_all begin=====\n");
    static int initialized;

    if (initialized)
        return;
    initialized = 1;

    av_register_all();

    /* protocols */
    av_log(NULL, AV_LOG_INFO, "===== custom modules begin =====\n");
#ifdef __ANDROID__
        av_log(NULL, AV_LOG_INFO, "===== custom modules begin __ANDROID__=====\n");
    IJK_REGISTER_PROTOCOL(ijkmediadatasource);
#endif
    av_log(NULL, AV_LOG_INFO, "===== custom modules begin !__ANDROID__=====\n");
    IJK_REGISTER_PROTOCOL(ijkio);
    av_log(NULL, AV_LOG_INFO, "===== custom modules begin ijkio=====\n");
    IJK_REGISTER_PROTOCOL(async);
    av_log(NULL, AV_LOG_INFO, "===== custom modules begin async=====\n");
    IJK_REGISTER_PROTOCOL(ijklongurl);
    av_log(NULL, AV_LOG_INFO, "===== custom modules begin ijklongurl=====\n");
    IJK_REGISTER_PROTOCOL(ijktcphook);
    av_log(NULL, AV_LOG_INFO, "===== custom modules begin ijktcphook=====\n");
    IJK_REGISTER_PROTOCOL(ijkhttphook);
    av_log(NULL, AV_LOG_INFO, "===== custom modules begin ijkhttphook=====\n");
    IJK_REGISTER_PROTOCOL(ijksegment);
    av_log(NULL, AV_LOG_INFO, "===== custom modules begin ijksegment=====\n");
    /* demuxers */
    IJK_REGISTER_DEMUXER(ijklivehook);
    av_log(NULL, AV_LOG_INFO, "===== custom modules end =====\n");
}
