# Contributor: liu<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=fmt
pkgver=10.0.0
pkgrel=0
pkgdesc="{fmt} is an open-source formatting library providing a fast and safe alternative to C stdio and C++ iostreams."
url="https://fmt.dev/latest/index.html"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT")
depends=()
makedepends=()
source="https://github.com/fmtlib/$pkgname/archive/refs/tags/$pkgver.tar.gz"

downloadpackage=true
autounpack=true
buildtools="cmake"

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz


prepare()  {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L  > `pwd`/build.log 2>&1
    make -j4 -C $ARCH-build  >> `pwd`/build.log 2>&1 
    ret=$?
    cd $OLDPWD
    return $ret
}

# 打包安装
package() {
    cd $builddir
    make -C $ARCH-build install
    cd $OLDPWD
}

# 进行测试的准备和说明
check() {
    echo "The test must be on an OpenHarmony device!"
    # 测试方法如下:
    # 进入目录: cd lycium/main/fmt/fmt-10.0.0/armeabi-v7a-build
    # 执行    : ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir  #${PWD}/$packagename
}
