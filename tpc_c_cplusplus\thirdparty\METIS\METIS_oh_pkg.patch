diff -Naur METIS-5.2.1_old/programs/gpmetis.c METIS-5.2.1/programs/gpmetis.c
--- METIS-5.2.1_old/programs/gpmetis.c	2023-08-02 20:05:49.538058190 +0800
+++ METIS-5.2.1/programs/gpmetis.c	2023-08-02 20:06:11.002477166 +0800
@@ -245,7 +245,7 @@
     getrusage(RUSAGE_SELF, &usage);
     printf("  rusage.ru_maxrss:\t\t %7.3"PRREAL" MB\n", (real_t)(usage.ru_maxrss/(1024.0)));
   }
-  printf("  proc/self/stat/VmPeak:\t %7.3"PRREAL" MB\n", (real_t)gk_GetProcVmPeak()/(1024.0*1024.0));
+  //printf("  proc/self/stat/VmPeak:\t %7.3"PRREAL" MB\n", (real_t)gk_GetProcVmPeak()/(1024.0*1024.0));
 #endif
 
   printf("******************************************************************************\n");
diff -Naur METIS-5.2.1_old/programs/mpmetis.c METIS-5.2.1/programs/mpmetis.c
--- METIS-5.2.1_old/programs/mpmetis.c	2023-08-02 20:05:49.538058190 +0800
+++ METIS-5.2.1/programs/mpmetis.c	2023-08-02 20:06:29.618836694 +0800
@@ -194,7 +194,7 @@
     getrusage(RUSAGE_SELF, &usage);
     printf("  rusage.ru_maxrss:\t\t %7.3"PRREAL" MB\n", (real_t)(usage.ru_maxrss/(1024.0)));
   }
-  printf("  proc/self/stat/VmPeak:\t %7.3"PRREAL" MB\n", (real_t)gk_GetProcVmPeak()/(1024.0*1024.0));
+  //printf("  proc/self/stat/VmPeak:\t %7.3"PRREAL" MB\n", (real_t)gk_GetProcVmPeak()/(1024.0*1024.0));
 #endif
 
   printf("******************************************************************************\n");
diff -Naur METIS-5.2.1_old/programs/ndmetis.c METIS-5.2.1/programs/ndmetis.c
--- METIS-5.2.1_old/programs/ndmetis.c	2023-08-02 20:05:49.538058190 +0800
+++ METIS-5.2.1/programs/ndmetis.c	2023-08-02 20:06:22.970708705 +0800
@@ -178,7 +178,7 @@
     getrusage(RUSAGE_SELF, &usage);
     printf("  rusage.ru_maxrss:\t\t %7.3"PRREAL" MB\n", (real_t)(usage.ru_maxrss/(1024.0)));
   }
-  printf("  proc/self/stat/VmPeak:\t %7.3"PRREAL" MB\n", (real_t)gk_GetProcVmPeak()/(1024.0*1024.0));
+  //printf("  proc/self/stat/VmPeak:\t %7.3"PRREAL" MB\n", (real_t)gk_GetProcVmPeak()/(1024.0*1024.0));
 #endif
 
   printf("******************************************************************************\n");
