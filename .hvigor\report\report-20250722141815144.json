{"version": "1.0", "events": [{"head": {"id": "376b9ef2-ec9d-413d-a537-71ca73203876", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20976873689500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a25dd528-a7b4-4740-ab21-b93b9326a0c0", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20976876481500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b303dd3-c7c3-4f87-a254-f0750e0d0e67", "name": "hvigor daemon: Socket will be closed. socketId=gADR4QkGZvLAWq20AAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20976878028600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "957a5f20-e981-4e77-b4c3-f03c272f8e9d", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":24240,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"da40c76aaa205116577ed59fcd7f95f31ca0a621\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753165091848,\"createdBy\":\"deveco\",\"sessionId\":\"00000050022bce7ab59c49a8b949a4ec75ac1b810a6409489f737cf971412f08b823c69a5392c8f29b13728299b577de74059a82caea6ffde2afcaca0f7e0062be90aba571f303721e52dccdbd71209333dc70fb180b36a8de210fafcaff9bff\"}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20976879372500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c77351a-d7f4-4faa-89a6-b10eafb7ca9c", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20976884441000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ced8c66-3425-4056-88b2-006b6210de66", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20976884648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c81084f-a4e5-4adf-957e-37fc9fcdc8a3", "name": "hvigor daemon: Socket is connected. socketId=oyWArkHQ-LCU5GJOAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978169256200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0173792-4e7c-429a-a8c1-fb53ccc5abf3", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"42809c4110d90f682938597528c2556b5103a105\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\onlyreceiver\\\\editVersion\\\\update11\\\\globalstateUse\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5140,\"state\":\"idle\",\"lastUsedTime\":1752805626314,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"0000005035465bbb6f3d5c2c33fe4747ba2a5898fffda2e8e55ad25942bc459b9580a8b060cccfa52a7bd36d99a0b7e4b75bddd3d7c99c1bde55cbdd6b43f8cbd601fcaff5420efe69552e27c20ca2364d8aa9c3b0c8eea4159258819fbe6195\"},{\"keyId\":\"de6873857704b4ce74f1c0d1f8334f8f0bba00ff\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":12108,\"state\":\"stopped\",\"lastUsedTime\":1753108050354,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005074309a7aba4d9cc163951cdd26e958b9ea8d6c2e93b2bef17a8aa2b3546b4e373f0195b00e9e7602eba45a4c72d2f6e60fb8fdcf386b3bbb5137c16f12fcc42a5f933ab9e9386e1946f99953da91e7faa3fb68587049723318ed1201\"},{\"keyId\":\"7f593f7d063ab29472c464a73e9647bf74b68398\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":2128,\"state\":\"stopped\",\"lastUsedTime\":1753144693749,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050cfbf6772f0477443650070505343419936d7ce4f8950a94eaed726ee8ee3b3e512552b4df3c3636106237ea6b8bafd8f3a98781e18e7f3d38de17c4673758ca85ea8ace7a056c938ae1ddd99ea6f61be6d625b83998c0ea0bf557f08\"},{\"pid\":24240,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"da40c76aaa205116577ed59fcd7f95f31ca0a621\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753165091859,\"createdBy\":\"deveco\",\"sessionId\":\"00000050022bce7ab59c49a8b949a4ec75ac1b810a6409489f737cf971412f08b823c69a5392c8f29b13728299b577de74059a82caea6ffde2afcaca0f7e0062be90aba571f303721e52dccdbd71209333dc70fb180b36a8de210fafcaff9bff\"}]", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978170437900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "734e3996-0e28-414e-b6d1-3f96356a7c2a", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":24240,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"da40c76aaa205116577ed59fcd7f95f31ca0a621\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753165091859,\"createdBy\":\"deveco\",\"sessionId\":\"00000050022bce7ab59c49a8b949a4ec75ac1b810a6409489f737cf971412f08b823c69a5392c8f29b13728299b577de74059a82caea6ffde2afcaca0f7e0062be90aba571f303721e52dccdbd71209333dc70fb180b36a8de210fafcaff9bff\"}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978171459000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "828a0831-0099-4c46-8baa-bbfbcc21a543", "name": "set active socket. socketId=oyWArkHQ-LCU5GJOAAAD", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978176626400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11897930-eef1-4af6-942b-f8adda27e493", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":24240,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"da40c76aaa205116577ed59fcd7f95f31ca0a621\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753165093151,\"createdBy\":\"deveco\",\"sessionId\":\"00000050022bce7ab59c49a8b949a4ec75ac1b810a6409489f737cf971412f08b823c69a5392c8f29b13728299b577de74059a82caea6ffde2afcaca0f7e0062be90aba571f303721e52dccdbd71209333dc70fb180b36a8de210fafcaff9bff\"}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978177626400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63395e9-8277-4113-84ee-877b3450bf19", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=ijkplayer', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978181325200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea23c335-5c75-40d0-8546-50f9bff40a1f", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978182068400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb74d05-9d73-456d-8153-73a7d54ee3ba", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":24240,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"da40c76aaa205116577ed59fcd7f95f31ca0a621\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753165093157,\"createdBy\":\"deveco\",\"sessionId\":\"00000050022bce7ab59c49a8b949a4ec75ac1b810a6409489f737cf971412f08b823c69a5392c8f29b13728299b577de74059a82caea6ffde2afcaca0f7e0062be90aba571f303721e52dccdbd71209333dc70fb180b36a8de210fafcaff9bff\"}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978183087200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0d4128-d7ad-4d3f-b589-d58eb39a8b53", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978188185700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0bca61a-2003-4e39-b756-4c8a3d10edc8", "name": "Cache service initialization finished in 2 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978190372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "803f6970-213c-481e-84b3-a92506a2fb47", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978195814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "987efcda-3495-4750-afef-6ddfb5b512dd", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978201542000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6309b31-167a-4ac2-8cd2-3c5daac46b55", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978201580800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e1ab0f3-1644-48cf-b0c0-f12e5ece9d37", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978206163600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "041d0d7d-75f7-459b-97a4-6542c9df4fe6", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978208097600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f635eea-7b17-4d88-979e-1d86fe947c62", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978215323300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e108bae4-0b66-4389-a40d-a453ed90a391", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978215376900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b39b5f52-a0bf-4516-b5bb-cb826ac43e61", "name": "Module entry Collected Dependency: D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978228084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba2c7678-68ee-4518-9eb8-3eece2b8ce2b", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978228145300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9abdd23-fd93-436c-b320-f6598e242fb6", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978236292400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5840c44c-74ed-4041-a80c-9d26879485a6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978236339800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc8b2539-3a59-419b-bca3-1cec88173940", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978236442100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1867d8e7-5e29-437e-b48a-2248730cc45a", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978236488400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d5b576-22f8-4c89-bd6d-34d912890547", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978236499000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8369f292-7f67-467a-b824-7ccca1795d12", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978236505200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f25f1a05-4250-42b3-906e-880286f73697", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978236533100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd28989-350d-487c-be61-c1f01729a1e0", "name": "require SDK: toolchains,ArkTS; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978238032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ecaf03-3d43-42d2-9160-48730580b2c7", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978245107500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776d815a-a43b-458a-b6f7-15e1260f16a4", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978245158900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18c5c0a-2402-4a33-9a6c-e23b1474e4d5", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978248430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94cebbdc-b794-4525-91a0-c96f1e6f3d1d", "name": "hvigorfile, require result:  { harTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978254772100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d7ad4e-c4f7-43a7-86fe-807c1b17ed66", "name": "hvigorfile, binding system plugins { harTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978254815100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86a56e8-a637-42df-9498-effa38c31c8c", "name": "<PERSON><PERSON><PERSON> ijkplayer Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978263255900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66e8a4f9-94bd-4141-b9f6-ae30b234305c", "name": "<PERSON><PERSON><PERSON> ijkplayer's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978263288400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "298a4860-e740-46c5-b584-32a7e0e23f21", "name": "Start initialize module-target build option map, moduleName=ijkplayer, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978264724800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be7017d6-b1c5-423d-be56-7a49576b65e1", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978264756200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "478bc808-e176-4e47-a39f-e3d7032d0a86", "name": "Module 'ijkplayer' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978265119500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b98b237b-747f-449d-8c6a-32b02df5ea8b", "name": "End initialize module-target build option map, moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978265128800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30333f32-93a1-44bf-989e-0e14999e2811", "name": "Module 'ijkplayer' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978265148400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2207375c-6fa2-45fc-8e1a-55156ec26c16", "name": "require SDK: toolchains,ArkTS,native; moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978266514900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70a9ed43-6535-4521-9d7b-74061ea99efe", "name": "<PERSON><PERSON><PERSON> ijkplayer task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978270579200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c95d9791-054e-4615-88fe-5e13363676ac", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978271076800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6281514-3fc1-42a8-a637-965263729d38", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978271154000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28cab64d-a6a9-4c00-a3da-c3ffc81ef26b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978271174900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce5a6c1f-e373-4dc8-8239-edd9b89de805", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978271217400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bea7496-d393-461c-8277-e1b729d0003d", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978271226800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa999c02-c331-4b63-965e-e2a39a4a2cf1", "name": "<PERSON><PERSON><PERSON>_ijkplayer-2.0.3 Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978271807800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac7b679-d5c1-41ba-a05e-3a8c153e29d4", "name": "<PERSON><PERSON><PERSON> ohos_ijkplayer-2.0.3's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978271821200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fdc9872-225c-4675-b7af-07928624fbc2", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978273429300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0f29c18-86d8-4a3e-8782-73bef25b3c12", "name": "Sdk init in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978281407400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6bdb09f-059a-41d3-8eba-2108cce6f4c2", "name": "project has submodules:entry,ijkplayer", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978305249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a659c50-035e-4282-8d31-40525eb66e7f", "name": "module:ijkplayer no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978308826400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d98172b6-3772-4ec6-81b7-829ea42d38a1", "name": "Project task initialization takes 31 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978311491900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "209c2249-fb4e-4770-bce8-c6d40b9a3f40", "name": "Sdk init in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978316250700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0018f7a7-bb5f-4fd0-9cf2-938c45c251ce", "name": "Sdk init in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978324507900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8203dc-e1aa-46a1-a5f0-a8cd2a3e59f2", "name": "Configuration phase cost:135 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978325235700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a2be861-9035-475a-aec6-432c04b08d21", "name": "Configuration task cost before running: 140 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978326750700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968f4352-6c35-44fc-85ee-37fe652c3050", "name": "ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978335798300, "endTime": 20978349810300}, "additional": {"children": [], "state": "success", "detailId": "edd7ccc6-3ca4-4882-bf08-25c6db219042", "logId": "885557f0-c171-43e2-a19a-d585747a9090"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "edd7ccc6-3ca4-4882-bf08-25c6db219042", "name": "create ijkplayer:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978329395200}, "additional": {"logType": "detail", "children": [], "durationId": "968f4352-6c35-44fc-85ee-37fe652c3050"}}, {"head": {"id": "5b08b572-af42-4814-a71e-2c33f2d6199e", "name": "ijkplayer : default@PreBuild start {\n  rss: 192360448,\n  heapTotal: 126763008,\n  heapUsed: 99464808,\n  external: 1086216,\n  arrayBuffers: 120860\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978335755100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4b6bfe3-8515-438c-91a9-6ec51b67819d", "name": "Executing task :ijkplayer:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978335821600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5636cdc6-42d7-4349-a237-4634aaf0a2c6", "name": "Incremental task ijkplayer:default@PreBuild pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978349534400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01555e63-70c3-44a9-8ae5-a709e28dde93", "name": "ijkplayer : default@PreBuild end {\n  rss: 194199552,\n  heapTotal: 127025152,\n  heapUsed: 99848032,\n  external: 1086216,\n  arrayBuffers: 120860\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978349688000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "885557f0-c171-43e2-a19a-d585747a9090", "name": "UP-TO-DATE :ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978335798300, "endTime": 20978349810300}, "additional": {"logType": "info", "children": [], "durationId": "968f4352-6c35-44fc-85ee-37fe652c3050"}}, {"head": {"id": "1e3ae62a-c968-4393-8608-6cb1344803e3", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978354307200, "endTime": 20980149047000}, "additional": {"children": ["84a034a1-9fb4-47f6-be00-d51f168167ac", "1237e71f-c651-40b2-9f11-52ebfbc55155"], "state": "success", "detailId": "662c8574-7c42-4f81-9afc-1b953770ca3e", "logId": "ed5fa7db-d6a1-4df7-8fb6-6978bea355cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "662c8574-7c42-4f81-9afc-1b953770ca3e", "name": "create ijkplayer:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978353077500}, "additional": {"logType": "detail", "children": [], "durationId": "1e3ae62a-c968-4393-8608-6cb1344803e3"}}, {"head": {"id": "544e4680-ebce-4981-8ec1-9a37ebfcbfda", "name": "ijkplayer : default@BuildNativeWithCmake start {\n  rss: 194674688,\n  heapTotal: 127287296,\n  heapUsed: 100324040,\n  external: 1086216,\n  arrayBuffers: 120860\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978354265200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e23735be-e959-431d-b11c-4668173b265d", "name": "Executing task :ijkplayer:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978354331100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b0a4faf-68e9-41ec-979a-0a1475b02252", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978361671400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea36d545-c403-48d8-baa7-4dd33117d1e8", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978364349600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a034a1-9fb4-47f6-be00-d51f168167ac", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 24240, "tid": "Worker0", "startTime": 20978367186200, "endTime": 20980146830100}, "additional": {"children": [], "state": "success", "parent": "1e3ae62a-c968-4393-8608-6cb1344803e3", "logId": "5121f4e8-05b7-474d-a4d6-112acea11b34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "48ff122b-a3bd-4ab6-a40d-32640f7cd8a5", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978366118000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303b4adb-3bcf-4840-b9d6-9d4a9f5d6583", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978367276800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51a40af7-d38a-4174-a483-b08f1224bad5", "name": "default@BuildNativeWithCmake work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978367592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a995afa0-feda-4219-93e7-c893aabb8a10", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978370384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f3ba868-f796-41e9-a5e7-907d132a18b6", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978371356700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1237e71f-c651-40b2-9f11-52ebfbc55155", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 24240, "tid": "Worker1", "startTime": 20978371856600, "endTime": 20980148923500}, "additional": {"children": [], "state": "success", "parent": "1e3ae62a-c968-4393-8608-6cb1344803e3", "logId": "348808bb-309f-47d9-aad7-f8064ebd1d58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f7ea20b1-26ce-444d-908f-e895052dd2cb", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978371773700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa07c0f8-e935-427c-b678-bbc1b824c101", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978371789900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ebdc66-bb9b-41b5-bebe-4e7eb3eef711", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978371866400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c028a7ab-ce38-427a-993a-2fd4f409e836", "name": "default@BuildNativeWithCmake work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978371886000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c45119ee-a7ff-46e7-be8f-020f9d573e34", "name": "ijkplayer : default@BuildNativeWithCmake end {\n  rss: 195608576,\n  heapTotal: 127287296,\n  heapUsed: 100771816,\n  external: 1086216,\n  arrayBuffers: 120860\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978371955100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af3f4606-1792-410e-a6b8-b0024e81d1fd", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980147320100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5121f4e8-05b7-474d-a4d6-112acea11b34", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 24240, "tid": "Worker0", "startTime": 20978367186200, "endTime": 20980146830100}, "additional": {"logType": "info", "children": [], "durationId": "84a034a1-9fb4-47f6-be00-d51f168167ac", "parent": "ed5fa7db-d6a1-4df7-8fb6-6978bea355cc"}}, {"head": {"id": "5f418a8e-b8cb-46e9-85e2-88eee22b06a0", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980148828200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e015b525-53cb-4d79-a30a-0b1aafba63ab", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980148949300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "348808bb-309f-47d9-aad7-f8064ebd1d58", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 24240, "tid": "Worker1", "startTime": 20978371856600, "endTime": 20980148923500}, "additional": {"logType": "info", "children": [], "durationId": "1237e71f-c651-40b2-9f11-52ebfbc55155", "parent": "ed5fa7db-d6a1-4df7-8fb6-6978bea355cc"}}, {"head": {"id": "ed5fa7db-d6a1-4df7-8fb6-6978bea355cc", "name": "Finished :ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978354307200, "endTime": 20980149047000}, "additional": {"logType": "info", "children": ["5121f4e8-05b7-474d-a4d6-112acea11b34", "348808bb-309f-47d9-aad7-f8064ebd1d58"], "durationId": "1e3ae62a-c968-4393-8608-6cb1344803e3"}}, {"head": {"id": "dbdbf74f-0e5d-4dfe-96ec-113f3b02b878", "name": "ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980152351000, "endTime": 20980152555300}, "additional": {"children": [], "state": "success", "detailId": "56d4a87c-bc3f-4f9b-bb4e-caed66734c8c", "logId": "f57584a7-2cc5-4875-a094-93798574a8b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "56d4a87c-bc3f-4f9b-bb4e-caed66734c8c", "name": "create ijkplayer:compileNative task", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980152152500}, "additional": {"logType": "detail", "children": [], "durationId": "dbdbf74f-0e5d-4dfe-96ec-113f3b02b878"}}, {"head": {"id": "1cd6ca08-61dc-4a1a-8d21-f75fe3745d10", "name": "ijkplayer : compileNative start {\n  rss: 293941248,\n  heapTotal: 127287296,\n  heapUsed: 100945416,\n  external: 1086216,\n  arrayBuffers: 120860\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980152299400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3500509-c495-4b75-b93a-c6d058f24c1f", "name": "Executing task :ijkplayer:compileNative", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980152389500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54253f79-8a55-497b-8d01-dfaa196100f3", "name": "ijkplayer : compileNative end {\n  rss: 293941248,\n  heapTotal: 127287296,\n  heapUsed: 100954624,\n  external: 1086216,\n  arrayBuffers: 120860\n}", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980152528300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f57584a7-2cc5-4875-a094-93798574a8b8", "name": "Finished :ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980152351000, "endTime": 20980152555300}, "additional": {"logType": "info", "children": [], "durationId": "dbdbf74f-0e5d-4dfe-96ec-113f3b02b878"}}, {"head": {"id": "3c365373-c504-44da-b53a-d9d5f79e3eb5", "name": "BUILD SUCCESSFUL in 1 s 967 ms ", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980153099300}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "8b572612-b86e-4cbd-a652-1aa4b211ac8f", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20978186858500, "endTime": 20980153740100}, "additional": {"time": {"year": 2025, "month": 7, "day": 22, "hour": 14, "minute": 18}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "2322c3f4-53b1-47fc-abf4-1569b15fadc2", "name": "There is no need to refresh cache, since the incremental task ijkplayer:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 24240, "tid": "Main Thread", "startTime": 20980154028400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}