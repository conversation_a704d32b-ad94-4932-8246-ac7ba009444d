#
# Makefile.ohos
#
#
# If you compile, change the name to Makefile.in.
#
#
 
#-----------------------------------------------------------------------------
# Shell
#-----------------------------------------------------------------------------

SHELL = /bin/sh

#-----------------------------------------------------------------------------
# Platform
#-----------------------------------------------------------------------------

PLAT = ohos

#-----------------------------------------------------------------------------
# Libraries and includs
#-----------------------------------------------------------------------------
 
BLLIB = libblas.a
CBLIB = ../lib/cblas_$(PLAT).a

#-----------------------------------------------------------------------------
# Compilers
#-----------------------------------------------------------------------------

CC =
FC =
LOADER = $(FC)

#-----------------------------------------------------------------------------
# Flags for Compilers
#-----------------------------------------------------------------------------

CFLAGS = -O3 -DADD_
FFLAGS = -O3  

#-----------------------------------------------------------------------------
# Archive programs and flags
#-----------------------------------------------------------------------------

ARCH = ar
ARCHFLAGS = r
RANLIB = echo
