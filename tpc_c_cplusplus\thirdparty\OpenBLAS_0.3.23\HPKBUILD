# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=OpenBLAS_0.3.23
pkgver=0.3.23
pkgrel=0
pkgdesc="OpenBLAS is an optimized BLAS (Basic Linear Algebra Subprograms) library based on GotoBLAS2 1.13 BSD version."
url="https://www.openblas.net"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD-3-Clause license")
depends=()
makedepends=()

# 源source="https://github.com/xianyi/${pkgname:0:8}/releases/download/v$pkgver/${pkgname:0:8}-$pkgver.tar.gz"
source="https://gitee.com/mirrors/${pkgname:0:8}/repository/archive/v${pkgver}.zip"

autounpack=true
downloadpackage=true
buildtools="cmake"

builddir=${pkgname:0:8}-v${pkgver}
packagename=v$pkgver.zip
patchflag=true

prepare() {
    if $patchflag
    then
        cd $builddir
        # patch 增加使用软件浮点运算指令集
        patch -p1 < `pwd`/../OpenBLAS_oh_pkg.patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DCMAKE_Fortran_COMPILER=OFF -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    echo "Clean build diretory!"
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}