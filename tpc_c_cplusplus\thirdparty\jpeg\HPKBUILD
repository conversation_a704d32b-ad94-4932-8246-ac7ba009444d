# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>
pkgname=jpeg
pkgver=v9e
pkgrel=0
pkgdesc="IJG is an informal group that writes and distributes a widely used free library for JPEG image compression."
url="http://www.ijg.org/"
archs=("armeabi-v7a" "arm64-v8a")
license=("Independent JPEG Group License")
depends=()
makedepends=()

source="http://www.ijg.org/files/"$pkgname"src.$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

source envset.sh
host=

prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
}

build() {
    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host > `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    unset host
}

check() {
    # 恢复环境
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi

    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # make check-local
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
