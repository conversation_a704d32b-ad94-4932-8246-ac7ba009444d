# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
result_linux=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/sonicout.wav   # 在linux执行生成的结果
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
openharmonycheck() {
    res=0    

    cd $builddir-$ARCH-build
    ./sonic -s 2.0 samples/sonic.wav out.wav > ${logfile} 2>&1
    res=$?
    if [ $res -eq 0 ]
    then
        ret=`cmp out.wav $result_linux`
        if [ ! -z "$ret" ]
        then
            res=1
            echo "the failed result:" >> ${logfile}
            echo $ret >> ${logfile}
        fi
    fi

    cd $OLDPWD

    return $res
}
