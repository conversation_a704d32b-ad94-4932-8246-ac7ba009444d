#Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd $LYCIUM_THIRDPARTY_ROOT/$pkgname/$pkgname-$pkgver/$ARCH-build
    # 该export是必选项，因为要链接本地bin文件
    export LD_LIBRARY_PATH=$LYCIUM_THIRDPARTY_ROOT/$pkgname/$pkgname-$pkgver/$ARCH-build/bin/:$LD_LIBRARY_PATH
    # 创建软链接
    mv /tmp /data/tmp/
    ln -s /data/tmp /tmp
    mkdir -p /usr/local/bin
    ln -s /usr/bin/cmake /usr/local/bin
    mkdir -p /usr/local/share
    ln -s /usr/share/cmake-3.26 /usr/local/share
    ctest > ${logfile} 2>&1
    res=$?
    if [ $res != 0 ]
    then
        mkdir ${LYCIUM_FAULT_PATH}/${pkgname}
        cp Testing/Temporary/LastTest.log ${LYCIUM_FAULT_PATH}/${pkgname}/
    fi
    unset LD_LIBRARY_PATH
    rm -r /usr/local
    cd $OLDPWD
    return $res
}