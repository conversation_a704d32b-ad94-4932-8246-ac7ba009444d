{"version": "1.0", "events": [{"head": {"id": "154efb2b-e488-41d2-9a5e-7712547818d9", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331662254200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d14dd87-ea9c-47be-92dd-e70165582394", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331676590100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12d4333f-3c9c-46a8-a7f5-412227d91578", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331676854500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76527d4d-b3ac-4b93-8d87-3bcde0a561ff", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331685364300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa1d31e9-2bce-48ac-9491-67993321dc76", "name": "hvigor daemon: Socket will be closed. socketId=hQEq1aKtUX9CKAJ-AAAD, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331686444800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97db829-8475-4e39-b714-efd01b2be5b4", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":3716,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097759877,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331687437500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a8f4fa9-9788-4ae9-ab9f-8b1f2bdd6a73", "name": "hvigor daemon: Check daemon process existed in registry, daemonStatus=idle.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25351124946100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea06a50b-80b5-43f4-bdf1-794cbfa04d14", "name": "hvigor daemon: Check daemon process existed in registry, daemonStatus=idle.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25381126187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4ca3827-a2dd-40e6-8707-bf81c5141248", "name": "hvigor daemon: Socket is connected. socketId=cC0mKccluLQ8-FjDAAAF, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387052439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "266464ad-5e40-4e54-b7cf-a10cb71454c3", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"42809c4110d90f682938597528c2556b5103a105\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\onlyreceiver\\\\editVersion\\\\update11\\\\globalstateUse\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5140,\"state\":\"idle\",\"lastUsedTime\":1752805626314,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"0000005035465bbb6f3d5c2c33fe4747ba2a5898fffda2e8e55ad25942bc459b9580a8b060cccfa52a7bd36d99a0b7e4b75bddd3d7c99c1bde55cbdd6b43f8cbd601fcaff5420efe69552e27c20ca2364d8aa9c3b0c8eea4159258819fbe6195\"},{\"keyId\":\"277ad167b90cc80ce976dbd6afbe358bd99b7f7e\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":2148,\"state\":\"stopped\",\"lastUsedTime\":1753090153399,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050838d1ad01bfd1550ead51f6ed433a227255c04f73eb6d56f22bd2763fe725e2b83fbcf4fd60a26c845a2c602755158018cba8b725381b1441e800bf943ef6f387c5623ba06c8adf19ae16119669eddff1cd39b24422ab96c15d49f8f\"},{\"keyId\":\"893216d240f25a41562b9bcbc7a74ef1fd3fa8ed\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\release\\\\0718\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45003,\"pid\":18640,\"state\":\"stopped\",\"lastUsedTime\":1753085265437,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050cac0116da7771717d59e7bc8faf998fc155484d3cd0d66298ab3323e5f33a8c26ed7d084a225d981022c8ea0220538a0a54723b401e094f07324ce4a503482a754aac107da85885404f88621f172c9c61d553975ef4a47330cc4a6cb\"},{\"keyId\":\"b9ddb8cea31f249c8b2536ebf131cc68ae6be44d\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":17592,\"state\":\"stopped\",\"lastUsedTime\":1753084933680,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005070f328dae46fd03c7719b268053842e81e407fb9e7f97683a5c3f4144b92f225f5b56019abca2a185e5cb5b31eb96548737440c854b4d374ea335b821a28fa1b5c8f9980b8a71ae27ef25131c90ce745e53355b29e37100e8ec29f49\"},{\"pid\":3716,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097759907,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387054059800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "552dde18-60c2-4f92-b126-a475710bf566", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":3716,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097759907,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387055323500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21382270-f075-4d68-a81a-139db46b01d6", "name": "set active socket. socketId=cC0mKccluLQ8-FjDAAAF", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387064008700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "918c84ec-1c77-446c-85af-22e5f0d24892", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":3716,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097815275,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387065496100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce26f5e-aa06-46af-b302-419288322f3b", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'product=default' ],\n  incremental: true,\n  _: [ 'assembleHap' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387069075200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02de74c5-547c-4037-9d46-b35c6018fdf9", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387069702000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0403a8b3-266e-4b95-a45f-b8edfe66996a", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":3716,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097815284,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387070624800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6616f569-481b-481f-bfdd-b678d21d4e76", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387076616400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ef9edea-4f21-4691-b123-c8cea01812c8", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387080536900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dfe91e4-305c-44d2-bf41-5df4a9f581d0", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387094071600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd3947a-83df-481f-a420-9229b6bc8efc", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387105595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b69ab2f-d2d7-4245-a5d9-48e35d210a83", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387105645800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd04377a-5407-4a87-bc19-dd6ee6a20b9d", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387118868400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da1a796-763e-4791-9ef9-25f04be3ff94", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387121878100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e5c879-aac7-4c03-8118-8118e035b661", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387131702700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da8e3e3-45b5-4194-a59e-af2d438ec9f3", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387131771400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e1e8eb-37dd-4013-85af-42fc7e882350", "name": "Module entry Collected Dependency: D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387151349500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "959dc0c8-b42a-41a8-b591-2a10f21d9f92", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387151398500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48a27e14-583a-40e1-b6b0-f0154fe7cb76", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387159266000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37faf44-21a6-4617-b46a-757559a8b7c0", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387159325600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a301928-fdeb-4240-9995-15ce900de1f9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387159445600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763639e5-def2-48ad-9bd8-df2f1128d325", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387159533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa5ae666-1fd2-45b7-9d37-e67f32af832b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387159547500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11092206-0977-4a65-b685-93e365e7af9b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387159556300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207b773a-8272-48e7-8f4e-c39bcc59d51d", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387159596500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b85b66b-42e6-40ec-8fc6-5b6bc87b5770", "name": "require SDK: toolchains,ArkTS; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387161376200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f19d27e-c37d-4bdd-89c8-7136a5f91e87", "name": "Module entry task initialization takes 8 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387171372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ebd7202-9c62-40a4-9418-a753eb289b03", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387171432100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abe71d73-806c-48c2-b878-8ecb1595d51b", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387179063500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e67cad3-e761-4fbd-ab24-6d5c17258a5d", "name": "hvigorfile, require result:  { harTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387196458800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47198efa-a062-45ce-bfd0-c5ab08bc9cc0", "name": "hvigorfile, binding system plugins { harTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387196507500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b35a614a-4ab8-4909-877d-9d12d35dee58", "name": "<PERSON><PERSON><PERSON> ijkplayer Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387213295100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0494c1f0-5245-4344-9d53-b921c90fb741", "name": "<PERSON><PERSON><PERSON> ijkplayer's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387213324100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e23293e6-5467-4e5a-8a57-cf49252b5955", "name": "Start initialize module-target build option map, moduleName=ijkplayer, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387216553400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "282338d5-3d1c-4e12-9649-a9ceb108f42e", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387217092500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e781ef-318f-4a4b-92f2-b0fed339a236", "name": "Module 'ijkplayer' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387218167900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67a018e2-52a7-404a-8321-b1c4cd4930a3", "name": "End initialize module-target build option map, moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387218199000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb864b4-13ca-458c-8824-01ba0c8ca0af", "name": "Module 'ijkplayer' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387218240600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33d9f99d-ab75-435a-b3d5-31baa545c408", "name": "require SDK: toolchains,ArkTS,native; moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387219543800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4438ef-9da1-4828-9525-d35344d23b75", "name": "<PERSON><PERSON><PERSON> ijkplayer task initialization takes 4 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387225837900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a949bc9-40d3-40d9-8338-0c95c62d51e7", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387226652600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22255be1-2bfb-4032-a8d8-108075787159", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387226779600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba0e7ec8-3aa1-49b2-b97f-9b7b6b3a3f77", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387226805800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e549e74f-316a-4782-80df-cd93f285f80b", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387226850600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712dbc0b-d13e-4835-b3a2-d100df6c9544", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387226866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f23971-e4ba-4b1c-b35f-9f3bd26dc9f4", "name": "<PERSON><PERSON><PERSON>_ijkplayer-2.0.3 Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387227841000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf5a4a8-0b8b-43db-8ed0-b817b068af59", "name": "<PERSON><PERSON><PERSON> ohos_ijkplayer-2.0.3's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387227865900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d80670-a3c3-48b9-869d-dc471f60f7d3", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387230561400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aeec657-59ec-473e-948a-8c68e7c0430f", "name": "Sdk init in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387243056500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f769d288-8e1b-4b01-8b52-f1566da8febe", "name": "project has submodules:entry,ijkplayer", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387273186200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a2dfd7c-5b5e-41c0-aa42-11dcf6d09d2a", "name": "module:ijkplayer no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387277019900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b019a7-9b28-4730-a875-475bf834c7c4", "name": "Project task initialization takes 37 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387279646400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e01877a-5d6e-45b7-9bc3-bc4c74a2ea23", "name": "Sdk init in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387284544100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c01a89-2258-4c9b-91de-588eb78fd4b5", "name": "Sdk init in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387294338200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "278350de-1326-45a1-a220-3fc508cd2dcd", "name": "Configuration phase cost:215 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387295309700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c803990c-9961-4464-9a02-040e43545ee1", "name": "Configuration task cost before running: 222 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387296555700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "500dd603-039a-4d31-83f8-3397644c5c19", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387303590400, "endTime": 25387403147200}, "additional": {"children": [], "state": "success", "detailId": "ec391074-06bf-473e-830b-70438cb4c9f8", "logId": "e89ef326-76a4-46f2-8fa1-6404a498333e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "ec391074-06bf-473e-830b-70438cb4c9f8", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387297710000}, "additional": {"logType": "detail", "children": [], "durationId": "500dd603-039a-4d31-83f8-3397644c5c19"}}, {"head": {"id": "09c5c7a2-43fb-447d-8838-93b34fb411bc", "name": "entry : default@PreBuild start {\n  rss: 155271168,\n  heapTotal: 88809472,\n  heapUsed: 83593352,\n  external: 1060359,\n  arrayBuffers: 87943\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387303551700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61dcc2e1-d5e5-4b60-8ba4-89b6aaca51a4", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387303605000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef0f16a-c1dc-41c9-9579-6522c4fb9ee4", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'D:\\\\harmonyFor\\\\DevEco Studio\\\\jbr' },\n  {\n    CLASSPATH: '.;D:\\\\houduan\\\\java1.8\\\\jdk\\\\lib\\\\dt.jar;D:\\\\houduan\\\\java1.8\\\\jdk\\\\lib\\\\tools.jar'\n  }\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387402995500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8df9b359-c6db-469a-9936-26caf5e81a8b", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'D:\\\\harmonyFor\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387403058200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3749648f-4364-4b6d-a393-04389ca56bab", "name": "entry : default@PreBuild end {\n  rss: 165695488,\n  heapTotal: 99033088,\n  heapUsed: 87114728,\n  external: 1068551,\n  arrayBuffers: 104327\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387403120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e89ef326-76a4-46f2-8fa1-6404a498333e", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387303590400, "endTime": 25387403147200}, "additional": {"logType": "info", "children": [], "durationId": "500dd603-039a-4d31-83f8-3397644c5c19"}}, {"head": {"id": "1dea075a-241e-437e-9991-3dc17903a2cd", "name": "ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387408942200, "endTime": 25387415101700}, "additional": {"children": [], "state": "success", "detailId": "49820e66-14be-4706-9530-ab610fe260d7", "logId": "01adb376-9e68-44e1-b906-9ba163f1c37a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "49820e66-14be-4706-9530-ab610fe260d7", "name": "create ijkplayer:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387405347900}, "additional": {"logType": "detail", "children": [], "durationId": "1dea075a-241e-437e-9991-3dc17903a2cd"}}, {"head": {"id": "dc94f516-4a83-4b0d-90fa-b23c937ff9e3", "name": "ijkplayer : default@PreBuild start {\n  rss: 165265408,\n  heapTotal: 99033088,\n  heapUsed: 87595960,\n  external: 1068551,\n  arrayBuffers: 104327\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387408916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3627bd8-78d6-45a7-b81b-a616ab7c7432", "name": "Executing task :ijkplayer:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387408952000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be04613f-5355-420f-82f2-3ae17c5d995f", "name": "runTaskFromQueue task cost before running: 335 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387409523300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3b584dc-6bb8-40f8-8eda-a8dee1fa1110", "name": "Incremental task ijkplayer:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387414783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88cb38ba-a23f-42c1-94e7-785794cd968b", "name": "ijkplayer : default@PreBuild end {\n  rss: 165339136,\n  heapTotal: 99033088,\n  heapUsed: 87710760,\n  external: 1068551,\n  arrayBuffers: 104327\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387414926800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01adb376-9e68-44e1-b906-9ba163f1c37a", "name": "UP-TO-DATE :ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387408942200, "endTime": 25387415101700}, "additional": {"logType": "info", "children": [], "durationId": "1dea075a-241e-437e-9991-3dc17903a2cd"}}, {"head": {"id": "af0f4e0a-4f41-40e3-89fd-a233c8bc2327", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387418553300, "endTime": 25387433254600}, "additional": {"children": [], "state": "success", "detailId": "d93d1e37-7298-4956-ac0f-9bfd24cd2206", "logId": "1c61fa6a-1c68-4658-acec-e29015c0c451"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "d93d1e37-7298-4956-ac0f-9bfd24cd2206", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387417751900}, "additional": {"logType": "detail", "children": [], "durationId": "af0f4e0a-4f41-40e3-89fd-a233c8bc2327"}}, {"head": {"id": "76813178-45d5-41f3-a177-40893f9bcfcf", "name": "entry : default@GenerateMetadata start {\n  rss: 165380096,\n  heapTotal: 99033088,\n  heapUsed: 87919960,\n  external: 1068551,\n  arrayBuffers: 104327\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387418539200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "238ecbb0-31b6-45bd-8404-a0ef83fd1f34", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387418567100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c77723-d468-46f6-bfca-cf77a01b80f9", "name": "entry : default@GenerateMetadata end {\n  rss: 165498880,\n  heapTotal: 99033088,\n  heapUsed: 88513336,\n  external: 1093127,\n  arrayBuffers: 128903\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387433197800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c61fa6a-1c68-4658-acec-e29015c0c451", "name": "Finished :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387418553300, "endTime": 25387433254600}, "additional": {"logType": "info", "children": [], "durationId": "af0f4e0a-4f41-40e3-89fd-a233c8bc2327"}}, {"head": {"id": "a9c9d522-d956-4f54-8c9d-75eb9a252e04", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387438400400, "endTime": 25387442198700}, "additional": {"children": [], "state": "success", "detailId": "1b23b4b6-0cad-44b5-ba75-263d0fef9e4b", "logId": "cf4d83e8-a5f7-49d4-9ecf-2ab0549ecafb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "1b23b4b6-0cad-44b5-ba75-263d0fef9e4b", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387437580900}, "additional": {"logType": "detail", "children": [], "durationId": "a9c9d522-d956-4f54-8c9d-75eb9a252e04"}}, {"head": {"id": "ae30ec6a-0c87-4873-b4e9-4bb78ee94538", "name": "entry : default@CreateBuildProfile start {\n  rss: 165904384,\n  heapTotal: 99033088,\n  heapUsed: 88875176,\n  external: 1093127,\n  arrayBuffers: 128903\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387438379300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c45a3de4-9a24-4da6-8de1-6f105058280c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387438422300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "694bac6c-065d-4db6-a705-2925dc48c8c9", "name": "runTaskFromQueue task cost before running: 365 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387439402500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66f152f-c993-404b-89d9-43ba2f5a4fd5", "name": "entry : default@CreateBuildProfile end {\n  rss: 165982208,\n  heapTotal: 99033088,\n  heapUsed: 88942944,\n  external: 1093127,\n  arrayBuffers: 128903\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387442148200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf4d83e8-a5f7-49d4-9ecf-2ab0549ecafb", "name": "Finished :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387438400400, "endTime": 25387442198700}, "additional": {"logType": "info", "children": [], "durationId": "a9c9d522-d956-4f54-8c9d-75eb9a252e04"}}, {"head": {"id": "41a58b2f-d655-4f6f-b06b-055ecb9690ad", "name": "ijkplayer:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387445511300, "endTime": 25387446762300}, "additional": {"children": [], "state": "success", "detailId": "dacc5e7b-f845-446d-a5e9-7d210a122239", "logId": "ebe8a988-5d9d-4b5e-91e8-9f9df8f3c9cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "dacc5e7b-f845-446d-a5e9-7d210a122239", "name": "create ijkplayer:default@CreateHarBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387444134800}, "additional": {"logType": "detail", "children": [], "durationId": "41a58b2f-d655-4f6f-b06b-055ecb9690ad"}}, {"head": {"id": "36ffb08a-f4bd-4a89-882f-dc4ef9737a76", "name": "ijkplayer : default@CreateHarBuildProfile start {\n  rss: 166264832,\n  heapTotal: 99033088,\n  heapUsed: 89420088,\n  external: 1101319,\n  arrayBuffers: 137095\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387445497300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4c0147b-f18c-4bb2-9f31-58047b1f6bab", "name": "Executing task :ijkplayer:default@CreateHarBuildProfile", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387445529700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "394f37fd-0ec6-4444-bb6b-4d9af814ba1d", "name": "runTaskFromQueue task cost before running: 371 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387445917500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb231c7f-a2db-43a0-a705-ac97981db4e7", "name": "ijkplayer : default@CreateHarBuildProfile end {\n  rss: 166301696,\n  heapTotal: 99033088,\n  heapUsed: 89474200,\n  external: 1101319,\n  arrayBuffers: 137095\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387446723800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe8a988-5d9d-4b5e-91e8-9f9df8f3c9cb", "name": "Finished :ijkplayer:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387445511300, "endTime": 25387446762300}, "additional": {"logType": "info", "children": [], "durationId": "41a58b2f-d655-4f6f-b06b-055ecb9690ad"}}, {"head": {"id": "5134c6d7-3bb2-46e8-ac3f-c68c6b95e801", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387452766800, "endTime": 25387459122200}, "additional": {"children": [], "state": "success", "detailId": "2e088741-0dbe-4264-a815-5f6f465a8494", "logId": "f148ffe8-e4cf-4523-84d3-cf7db8151457"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "2e088741-0dbe-4264-a815-5f6f465a8494", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387448230000}, "additional": {"logType": "detail", "children": [], "durationId": "5134c6d7-3bb2-46e8-ac3f-c68c6b95e801"}}, {"head": {"id": "6c9cba22-0b77-41aa-aeea-5b78aa56d8b3", "name": "Module 'ijkplayer' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387450032600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e7d76e4-88f5-4a70-8b47-246aea17a11d", "name": "entry : default@GenerateLoaderJson start {\n  rss: 166674432,\n  heapTotal: 99033088,\n  heapUsed: 89809664,\n  external: 1101319,\n  arrayBuffers: 137095\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387452750200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "488d740d-796f-4725-956c-3965b7681277", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387452786600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab6d4ac2-271a-42a6-b153-3835dca9cd16", "name": "runTaskFromQueue task cost before running: 379 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387453841800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1598a25f-acc3-4e5c-8c3a-6ebf7a112205", "name": "entry : default@GenerateLoaderJson end {\n  rss: 167047168,\n  heapTotal: 99033088,\n  heapUsed: 90131064,\n  external: 1101319,\n  arrayBuffers: 137095\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387459086400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f148ffe8-e4cf-4523-84d3-cf7db8151457", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387452766800, "endTime": 25387459122200}, "additional": {"logType": "info", "children": [], "durationId": "5134c6d7-3bb2-46e8-ac3f-c68c6b95e801"}}, {"head": {"id": "b4a25956-b541-4ccf-ab13-7e9d7e90735a", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387462134500, "endTime": 25389084403300}, "additional": {"children": ["c6d3a83b-1919-4d46-8264-5818a45ecd8d", "e26b8b1e-2ef3-46af-997b-967e7ccf5310"], "state": "success", "detailId": "0f08a3f3-0284-4387-9202-8b6da16f53c5", "logId": "9365d197-62ee-46bb-a410-8a11e97fa0e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "0f08a3f3-0284-4387-9202-8b6da16f53c5", "name": "create ijkplayer:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387461117200}, "additional": {"logType": "detail", "children": [], "durationId": "b4a25956-b541-4ccf-ab13-7e9d7e90735a"}}, {"head": {"id": "5eb73b27-37e3-406e-8b62-8dd5e19eab48", "name": "ijkplayer : default@BuildNativeWithCmake start {\n  rss: 167362560,\n  heapTotal: 99033088,\n  heapUsed: 90394968,\n  external: 1101319,\n  arrayBuffers: 137095\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387462116000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "105b5ad9-0ef0-4b60-b528-59f7cef9912d", "name": "Executing task :ijkplayer:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387462156500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c40a79-0376-4864-97f4-36a66986a6b0", "name": "runTaskFromQueue task cost before running: 388 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387462243600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab511156-1b89-4422-b10f-bb78ec057421", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387464452500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3449859-ce13-4122-a44d-54434d82bd78", "name": "default@BuildNativeWithCmake work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387465948800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6d3a83b-1919-4d46-8264-5818a45ecd8d", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Worker0", "startTime": 25387468611900, "endTime": 25389081747200}, "additional": {"children": [], "state": "success", "parent": "b4a25956-b541-4ccf-ab13-7e9d7e90735a", "logId": "0a14d682-e129-40e5-aa2a-8e52974383b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "4623a8bf-366b-4d62-8e55-bfaca4c827dd", "name": "default@BuildNativeWithCmake work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387468434000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f2c05c6-d5c0-4bd5-9cf9-da52c2aef691", "name": "default@BuildNativeWithCmake work[2] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387468641700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "162e9e18-45cf-4f1d-8ff0-de3fc1d46aa0", "name": "default@BuildNativeWithCmake work[2] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387468687400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fb25ea7-8f6a-4263-80ee-8c7eb504c205", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387470097000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b868786-f988-4519-a650-4924ae687978", "name": "default@BuildNativeWithCmake work[3] is submitted.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387471165800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e26b8b1e-2ef3-46af-997b-967e7ccf5310", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Worker1", "startTime": 25387471844200, "endTime": 25389084319500}, "additional": {"children": [], "state": "success", "parent": "b4a25956-b541-4ccf-ab13-7e9d7e90735a", "logId": "b8841f3b-d929-4ac1-8450-143c640e5d82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "994519c3-fe04-4cc5-90ae-181e32a46cff", "name": "default@BuildNativeWithCmake work[3] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387471732000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "904b58ab-d08a-43fb-b290-1089dd0df281", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387471750700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277190b3-472e-47f2-9323-7db90999ae69", "name": "default@BuildNativeWithCmake work[3] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387471862000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77ddcaee-e437-4441-ad98-db6cd5c22797", "name": "default@BuildNativeWithCmake work[3] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387471895300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef480e4-6760-4e38-ae9d-b8e522b88fdf", "name": "ijkplayer : default@BuildNativeWithCmake end {\n  rss: 168300544,\n  heapTotal: 99295232,\n  heapUsed: 87347304,\n  external: 1101319,\n  arrayBuffers: 112519\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387471984400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4853d7c3-b2b6-46f8-bede-3e7d45de375c", "name": "ijkplayer:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387477678500, "endTime": 25387482549200}, "additional": {"children": [], "state": "success", "detailId": "de16c277-5e81-48d7-84bc-9c4dc200a686", "logId": "79403d8f-81b4-428f-8cd4-9d9a1ad0d18a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "de16c277-5e81-48d7-84bc-9c4dc200a686", "name": "create ijkplayer:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387475529100}, "additional": {"logType": "detail", "children": [], "durationId": "4853d7c3-b2b6-46f8-bede-3e7d45de375c"}}, {"head": {"id": "1501f66a-4f42-42aa-b905-e603d3beec6c", "name": "ijkplayer : default@MergeProfile start {\n  rss: 168714240,\n  heapTotal: 99295232,\n  heapUsed: 87503528,\n  external: 1101319,\n  arrayBuffers: 112519\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387477634400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d4cd041-ddba-4c8d-bb3d-009b2ac4538e", "name": "Executing task :ijkplayer:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387477732200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf744b18-c6d3-43ed-8a9c-d1cdf696bc5e", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387478685800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f392f1-9fb7-4662-b559-ff4739d84681", "name": "Change app compile API version with '********'", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387478958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d085c4-03d9-4617-a7b3-47b398426846", "name": "Change app target API version with '11'", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387478979700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e548b12-2a13-4adf-87a3-974c4a5de56c", "name": "Change app minimum API version with '11'", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387478987900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e64c341f-aa5c-4d67-a7d1-f6f5444fa5c0", "name": "ijkplayer : default@MergeProfile end {\n  rss: 168796160,\n  heapTotal: 99295232,\n  heapUsed: 87550816,\n  external: 1101319,\n  arrayBuffers: 112519\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387482419000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79403d8f-81b4-428f-8cd4-9d9a1ad0d18a", "name": "Finished :ijkplayer:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387477678500, "endTime": 25387482549200}, "additional": {"logType": "info", "children": [], "durationId": "4853d7c3-b2b6-46f8-bede-3e7d45de375c"}}, {"head": {"id": "478cf911-313c-4546-8b89-30801faa927d", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387487263400, "endTime": 25387493201400}, "additional": {"children": [], "state": "success", "detailId": "ed14c5cf-4db9-41e0-a976-3b343027cf01", "logId": "4b50d73d-8c19-4cad-b2d9-9a1179727e5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "ed14c5cf-4db9-41e0-a976-3b343027cf01", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387484447000}, "additional": {"logType": "detail", "children": [], "durationId": "478cf911-313c-4546-8b89-30801faa927d"}}, {"head": {"id": "ec609602-d475-439f-afac-960424424c1a", "name": "entry : default@MergeProfile start {\n  rss: 168886272,\n  heapTotal: 99295232,\n  heapUsed: 88006552,\n  external: 1101319,\n  arrayBuffers: 112519\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387487240300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d7134d-9c2c-4452-a7e3-a35133909729", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387487277400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd3e437-3338-48a4-bc84-72a947f59f84", "name": "runTaskFromQueue task cost before running: 413 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387487571000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23acfdf5-9224-421d-ac89-311fcb314d5f", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387488094800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea738cc-930d-4a3e-bea8-8e6c180633ce", "name": "Change app compile API version with '********'", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387488144900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04212e20-6c58-4249-81b0-8122d8ba94fd", "name": "Change app target API version with '11'", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387488157300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd12164b-e8bd-4c28-a056-633b05376ed2", "name": "Change app minimum API version with '11'", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387488166500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e2054e-f3d2-4f92-8242-5915f7c4f53b", "name": "entry : default@MergeProfile end {\n  rss: 168935424,\n  heapTotal: 99295232,\n  heapUsed: 88113736,\n  external: 1084935,\n  arrayBuffers: 120711\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387493145200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b50d73d-8c19-4cad-b2d9-9a1179727e5a", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387487263400, "endTime": 25387493201400}, "additional": {"logType": "info", "children": [], "durationId": "478cf911-313c-4546-8b89-30801faa927d"}}, {"head": {"id": "117a4e51-0904-4d03-9936-ae0b60a00e67", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387496640200, "endTime": 25387514711100}, "additional": {"children": [], "state": "success", "detailId": "fa33a6b7-4447-4a9b-b794-53f025fc18b4", "logId": "318f87e1-6d15-4b05-9434-a85139d48924"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "fa33a6b7-4447-4a9b-b794-53f025fc18b4", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387495593400}, "additional": {"logType": "detail", "children": [], "durationId": "117a4e51-0904-4d03-9936-ae0b60a00e67"}}, {"head": {"id": "99c1cd00-6d78-4b05-a16e-ee07a1356dfd", "name": "entry : default@MakePackInfo start {\n  rss: 168988672,\n  heapTotal: 99295232,\n  heapUsed: 88320608,\n  external: 1084935,\n  arrayBuffers: 120711\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387496599600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16627c88-c0d9-48be-889f-67bae699d346", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387496666500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e49564-b6aa-4569-bc6d-4332ffa14b28", "name": "runTaskFromQueue task cost before running: 427 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387501814800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6baf781c-fe36-43bd-824b-69eb115e4d5e", "name": "Module Pack Info:  {\n  summary: {\n    app: {\n      bundleName: 'com.example.ijkplayer',\n      bundleType: undefined,\n      version: [Object]\n    },\n    modules: [ [Object] ]\n  },\n  packages: [\n    {\n      deviceType: [Array],\n      moduleType: 'entry',\n      deliveryWithInstall: true,\n      name: 'entry-default'\n    }\n  ]\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387512740200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41f1535e-150e-43ea-8ec8-e0f68053915e", "name": "entry : default@MakePackInfo end {\n  rss: 169771008,\n  heapTotal: 99295232,\n  heapUsed: 88920224,\n  external: 1109511,\n  arrayBuffers: 145287\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387514603800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318f87e1-6d15-4b05-9434-a85139d48924", "name": "Finished :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387496640200, "endTime": 25387514711100}, "additional": {"logType": "info", "children": [], "durationId": "117a4e51-0904-4d03-9936-ae0b60a00e67"}}, {"head": {"id": "cf624aa5-eb59-4408-84fb-ea34d0ed397a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387527176400, "endTime": 25387864852300}, "additional": {"children": [], "state": "success", "detailId": "d83aa670-f645-4e4b-9470-86efad1f8906", "logId": "9f7d5558-4340-4f48-857b-993ae920cc49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "d83aa670-f645-4e4b-9470-86efad1f8906", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387520855700}, "additional": {"logType": "detail", "children": [], "durationId": "cf624aa5-eb59-4408-84fb-ea34d0ed397a"}}, {"head": {"id": "f7e06325-7614-4882-9a75-b2f9039eb1a1", "name": "entry : default@ProcessProfile start {\n  rss: 171048960,\n  heapTotal: 99295232,\n  heapUsed: 89258920,\n  external: 1109511,\n  arrayBuffers: 145287\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387527120000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c5730e5-b113-4119-9652-d288bbee48af", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387527205900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f017ee6-dcfd-46cd-8f7e-85669b69f1ec", "name": "runTaskFromQueue task cost before running: 453 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387527496800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c48e99e4-a357-4609-b31a-b08e17e9f0c0", "name": "********", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387863053000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34e7bb9-476f-47ec-a4bd-04fd5c20d1b1", "name": "entry : default@ProcessProfile end {\n  rss: 260972544,\n  heapTotal: 99819520,\n  heapUsed: 88536184,\n  external: 1076767,\n  arrayBuffers: 112543\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387864772000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f7d5558-4340-4f48-857b-993ae920cc49", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387527176400, "endTime": 25387864852300}, "additional": {"logType": "info", "children": [], "durationId": "cf624aa5-eb59-4408-84fb-ea34d0ed397a"}}, {"head": {"id": "e57e9f4d-8445-4e26-8d2e-60200db9c7f3", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387870745500, "endTime": 25387880721600}, "additional": {"children": [], "state": "success", "detailId": "f23d9bab-4fea-497c-a80c-1f2454378b2c", "logId": "a5a75afb-b570-4359-9315-849fcd0a924a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f23d9bab-4fea-497c-a80c-1f2454378b2c", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387866925900}, "additional": {"logType": "detail", "children": [], "durationId": "e57e9f4d-8445-4e26-8d2e-60200db9c7f3"}}, {"head": {"id": "398c8b9c-0a05-490d-8571-6313ffb624a9", "name": "restool module names: entry,ijkplayer; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387869854300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b918a0e-b330-49a3-8b68-37bc726c9dc3", "name": "entry : default@ProcessResource start {\n  rss: 262451200,\n  heapTotal: 99819520,\n  heapUsed: 88756912,\n  external: 1076767,\n  arrayBuffers: 112543\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387870724800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f40a04c4-26cc-465f-aadf-0f92af3715fd", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387876235900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d068560-8568-40af-9f95-866521ca0492", "name": "runTaskFromQueue task cost before running: 802 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387876409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d931a3e-9b60-402a-99cb-e939d3cce03b", "name": "entry : default@ProcessResource end {\n  rss: 265588736,\n  heapTotal: 99819520,\n  heapUsed: 88953856,\n  external: 1084959,\n  arrayBuffers: 120735\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387877883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5a75afb-b570-4359-9315-849fcd0a924a", "name": "Finished :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387870745500, "endTime": 25387880721600}, "additional": {"logType": "info", "children": [], "durationId": "e57e9f4d-8445-4e26-8d2e-60200db9c7f3"}}, {"head": {"id": "d93a5c8a-1b4f-4e71-bd58-de1eca190d53", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387889113300, "endTime": 25388117644900}, "additional": {"children": [], "state": "success", "detailId": "74055144-56db-4f96-bc53-67f6967f9232", "logId": "62a205e7-9eef-4d52-86e3-9421f9a298b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "74055144-56db-4f96-bc53-67f6967f9232", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387885343600}, "additional": {"logType": "detail", "children": [], "durationId": "d93a5c8a-1b4f-4e71-bd58-de1eca190d53"}}, {"head": {"id": "afe44426-30c5-4661-8e89-7ea57b9a8110", "name": "restool module names: entry,ijkplayer; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387886893000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3f25db3-1922-4e3d-9ba7-b28a6f64f6bc", "name": "entry : default@CompileResource start {\n  rss: 271593472,\n  heapTotal: 99819520,\n  heapUsed: 89615232,\n  external: 1093151,\n  arrayBuffers: 128927\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387888946400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60236ff7-2686-4fa6-971b-ec9eac06ff43", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387889721400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bbeec9c-9b12-4c14-9421-9de031a7ebca", "name": "runTaskFromQueue task cost before running: 816 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387890682200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28554f7e-6efa-4100-89be-e2d924016ab2", "name": "Use tool [D:\\harmonyFor\\openSDK\\11\\toolchains\\restool.exe]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe',\n  '-l',\n  'D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387895616900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30dc2832-3361-44c6-a253-73d914ecb2b8", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388104242000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6397537-f955-433e-bbdf-c3f158927632", "name": "entry : default@CompileResource end {\n  rss: 234004480,\n  heapTotal: 100605952,\n  heapUsed: 79802000,\n  external: 1019525,\n  arrayBuffers: 55301\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388117460200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62a205e7-9eef-4d52-86e3-9421f9a298b5", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387889113300, "endTime": 25388117644900}, "additional": {"logType": "info", "children": [], "durationId": "d93a5c8a-1b4f-4e71-bd58-de1eca190d53"}}, {"head": {"id": "53f9ce43-3a2a-44f9-98ac-974ef5794193", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388128700400}, "additional": {"children": ["2f230eef-9055-487d-a4a5-1e253d8eb9ff"], "state": "running", "detailId": "115d4d79-d493-49bb-8408-64cd430088df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "115d4d79-d493-49bb-8408-64cd430088df", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388121376000}, "additional": {"logType": "detail", "children": [], "durationId": "53f9ce43-3a2a-44f9-98ac-974ef5794193"}}, {"head": {"id": "122024b2-4477-47b1-bbfe-29660d2694b8", "name": "entry : default@CompileArkTS start {\n  rss: 234090496,\n  heapTotal: 100605952,\n  heapUsed: 80153904,\n  external: 1027717,\n  arrayBuffers: 63493\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388128656800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04665f4a-1fb7-4782-9630-da506fee1c42", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388128718500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "109dbae1-d3d3-4b5a-adcb-1d6020328896", "name": "Obfuscation config only effect in release mode.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388135496200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a187d5e-6618-47bd-96b9-8d7afb301e50", "name": "runTaskFromQueue task cost before running: 1 s 61 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388135566900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c670104-cfb2-40a7-9542-890692090efb", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388153980000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a59c1c-79cb-4ddb-921a-643f44b646dc", "name": "default@CompileArkTS work[4] is submitted.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388156287100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d92d6b-3eea-4acb-b302-30a11275ab20", "name": "default@CompileArkTS work[4] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388158510600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f409a272-fa16-4d0b-91c9-3b9ebeea3d54", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388158540300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c649d4-d7b1-49f7-8205-4700c60c3fd9", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388158553400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31a4f47d-558c-4886-acb8-cc12afd87bdd", "name": "default@CompileArkTS work[4] has been dispatched to worker[2].", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388159703600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58eaffea-016d-42b4-8fc7-9131f4ec9bcf", "name": "default@CompileArkTS work[4] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388159735000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb5a44da-f48a-4bd4-a08c-749db174e0b6", "name": "entry : default@CompileArkTS end {\n  rss: 238772224,\n  heapTotal: 100605952,\n  heapUsed: 80999080,\n  external: 1035909,\n  arrayBuffers: 71685\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388159875600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8f38761-17d7-480e-8fe7-453e96c5fdf5", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388166509000, "endTime": 25388178659200}, "additional": {"children": [], "state": "success", "detailId": "30a2153e-9951-457d-9b96-8da7dcaa18da", "logId": "a1e0fd63-335c-449d-a1cb-557cea60bddc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "30a2153e-9951-457d-9b96-8da7dcaa18da", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388161017900}, "additional": {"logType": "detail", "children": [], "durationId": "b8f38761-17d7-480e-8fe7-453e96c5fdf5"}}, {"head": {"id": "0a159cd2-eaa2-4fd4-b9eb-43af1ef36530", "name": "entry : default@BuildJS start {\n  rss: 240320512,\n  heapTotal: 100605952,\n  heapUsed: 81297296,\n  external: 1035909,\n  arrayBuffers: 71685\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388166432400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16147ba-20d5-4a77-bae5-2269bf1f5007", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388166527600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f03d3f78-7434-4ee2-990d-f7d557156e1c", "name": "entry : default@BuildJS end {\n  rss: 245534720,\n  heapTotal: 100605952,\n  heapUsed: 81641840,\n  external: 1035909,\n  arrayBuffers: 71685\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388178603000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1e0fd63-335c-449d-a1cb-557cea60bddc", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388166509000, "endTime": 25388178659200}, "additional": {"logType": "info", "children": [], "durationId": "b8f38761-17d7-480e-8fe7-453e96c5fdf5"}}, {"head": {"id": "d407cb67-eefd-461d-9898-f473b57c50e8", "name": "runTaskFromQueue task cost before running: 1 s 104 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25388178960100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75cb391f-0d14-459a-80f3-db7e558a7b67", "name": "default@BuildNativeWithCmake work[2] done.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389082511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a14d682-e129-40e5-aa2a-8e52974383b3", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Worker0", "startTime": 25387468611900, "endTime": 25389081747200}, "additional": {"logType": "info", "children": [], "durationId": "c6d3a83b-1919-4d46-8264-5818a45ecd8d", "parent": "9365d197-62ee-46bb-a410-8a11e97fa0e6"}}, {"head": {"id": "00f8bf72-6a03-4d87-9360-8425198a307e", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389084227800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeb34e54-c566-4b9b-9d24-b0fcac0ef940", "name": "default@BuildNativeWithCmake work[3] done.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389084349500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8841f3b-d929-4ac1-8450-143c640e5d82", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Worker1", "startTime": 25387471844200, "endTime": 25389084319500}, "additional": {"logType": "info", "children": [], "durationId": "e26b8b1e-2ef3-46af-997b-967e7ccf5310", "parent": "9365d197-62ee-46bb-a410-8a11e97fa0e6"}}, {"head": {"id": "9365d197-62ee-46bb-a410-8a11e97fa0e6", "name": "Finished :ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387462134500, "endTime": 25389084403300}, "additional": {"logType": "info", "children": ["0a14d682-e129-40e5-aa2a-8e52974383b3", "b8841f3b-d929-4ac1-8450-143c640e5d82"], "durationId": "b4a25956-b541-4ccf-ab13-7e9d7e90735a"}}, {"head": {"id": "2fbd6091-9948-4b4e-8355-6670749e385c", "name": "ijkplayer:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389087948500, "endTime": 25389202368700}, "additional": {"children": ["a7a3be2e-e160-46cc-9088-67c0e32542d3", "1f623664-43af-491a-9590-c19d9d6e4b54"], "state": "failed", "detailId": "dd5933ab-4aa3-47bb-b5f6-aa8f29a8e752", "logId": "595e3a98-3e41-4bab-8800-8f43ca1c33bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "dd5933ab-4aa3-47bb-b5f6-aa8f29a8e752", "name": "create ijkplayer:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389086825200}, "additional": {"logType": "detail", "children": [], "durationId": "2fbd6091-9948-4b4e-8355-6670749e385c"}}, {"head": {"id": "b21ca308-1015-4991-9381-32a07efd5bd7", "name": "ijkplayer : default@BuildNativeWithNinja start {\n  rss: 319905792,\n  heapTotal: 100605952,\n  heapUsed: 81901904,\n  external: 1035909,\n  arrayBuffers: 71685\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389087928300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "582678d3-b338-462a-a820-e9c2265f2042", "name": "Executing task :ijkplayer:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389087960300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f6b40f-1013-4ba6-91fe-392a934bf9ec", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389090909300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a6cc2b-b6ee-4592-87c2-66dda8b1a353", "name": "Use tool [Ninja]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '-C',\n  'D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\arm64-v8a'\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389116306500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "361b469d-1661-4b40-a13c-7b3abc9e6cc8", "name": "default@BuildNativeWithNinja work[5] is submitted.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389117138700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7a3be2e-e160-46cc-9088-67c0e32542d3", "name": "ijkplayer:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Worker0", "startTime": 25389117958500, "endTime": 25389200834500}, "additional": {"children": [], "state": "failed", "parent": "2fbd6091-9948-4b4e-8355-6670749e385c", "logId": "2206b7d4-a4fe-424d-979e-dc690871bd39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "6b8a0b6f-9052-454c-b942-8167deda6842", "name": "default@BuildNativeWithNinja work[5] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389117814100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b5a8cfd-5d88-4c05-bc24-017184c2c0c1", "name": "default@BuildNativeWithNinja work[5] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389117973600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72b58c18-1af4-4b03-ab75-0567b789f7c8", "name": "default@BuildNativeWithNinja work[5] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389118033300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3116dc31-4431-4f2f-8507-e38745522788", "name": "Use tool [Ninja]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '-C',\n  'D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\x86_64'\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389153873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f42a7a9-22c5-4d89-99a2-f243ba896275", "name": "default@BuildNativeWithNinja work[6] is submitted.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389154735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f623664-43af-491a-9590-c19d9d6e4b54", "name": "ijkplayer:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389154777000, "endTime": 25389202385700}, "additional": {"children": [], "state": "failed", "parent": "2fbd6091-9948-4b4e-8355-6670749e385c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "6c7352bd-667a-4c76-9669-b01f4ea075e2", "name": "default@BuildNativeWithNinja work[6] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389155212000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70cbfed1-ab82-456f-b809-84be2a7cb816", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389155227300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cacb5705-e296-4e2b-8862-e995e002d1fc", "name": "default@BuildNativeWithNinja work[6] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389155355900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "952fad5a-7abc-49a6-8c8d-8c6fc6895ada", "name": "default@BuildNativeWithNinja work[6] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389155418200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa578bc-3bc9-4b53-a001-825ef376804b", "name": "ijkplayer : default@BuildNativeWithNinja end {\n  rss: 322605056,\n  heapTotal: 100605952,\n  heapUsed: 82346424,\n  external: 1072700,\n  arrayBuffers: 108476\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389164034100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31f27275-1707-4a8b-99b7-3140c421f074", "name": "default@BuildNativeWithNinja work[5] failed.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389201107700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2206b7d4-a4fe-424d-979e-dc690871bd39", "name": "ijkplayer:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Worker0", "startTime": 25389117958500, "endTime": 25389200834500}, "additional": {"logType": "error", "children": [], "durationId": "a7a3be2e-e160-46cc-9088-67c0e32542d3", "parent": "595e3a98-3e41-4bab-8800-8f43ca1c33bc"}}, {"head": {"id": "595e3a98-3e41-4bab-8800-8f43ca1c33bc", "name": "Failed :ijkplayer:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389087948500, "endTime": 25389202368700}, "additional": {"logType": "error", "children": ["2206b7d4-a4fe-424d-979e-dc690871bd39"], "durationId": "2fbd6091-9948-4b4e-8355-6670749e385c"}}, {"head": {"id": "5c805e8e-9f51-44a5-88b8-0ef80b4f25b8", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389203468700}, "additional": {"logType": "debug", "children": [], "durationId": "2fbd6091-9948-4b4e-8355-6670749e385c"}}, {"head": {"id": "001d7de0-89ec-4737-b928-c5102bc303b1", "name": "Tools execution failed.\r\nninja: error: 'D:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/yuv/arm64-v8a/lib/libyuv.a', needed by 'D:/new/ohos_ijkplayer-2.0.3/ijkplayer/build/default/intermediates/cmake/default/obj/arm64-v8a/libijksdl.so', missing and no known rule to make it\r\n\t Detail: Please check the message from tools.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389203577900}, "additional": {"logType": "error", "children": [], "durationId": "2fbd6091-9948-4b4e-8355-6670749e385c"}}, {"head": {"id": "18ca329e-011b-4960-ae4d-b63010edffe7", "name": "ERROR: stacktrace = Error: Tools execution failed.\r\nninja: error: 'D:/new/ohos_ijkplayer-2.0.3/ijkplayer/src/main/cpp/third_party/yuv/arm64-v8a/lib/libyuv.a', needed by 'D:/new/ohos_ijkplayer-2.0.3/ijkplayer/build/default/intermediates/cmake/default/obj/arm64-v8a/libijksdl.so', missing and no known rule to make it\r\n\t Detail: Please check the message from tools.\n    at OhosLogger.errorMessageExit (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\log\\hvigor-log.js:1:2347)\n    at OhosLogger._printErrorAndExit (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\utils\\log\\ohos-logger.js:1:1878)\n    at ProcessUtils.handleException (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\utils\\process-utils.js:1:5035)\n    at ProcessUtils.execute (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\utils\\process-utils.js:1:3335)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MessagePort.<anonymous> (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\internal\\pool\\worker-manager\\worker-action.js:1:2067)", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389203822700}, "additional": {"logType": "debug", "children": [], "durationId": "2fbd6091-9948-4b4e-8355-6670749e385c"}}, {"head": {"id": "b3ff568e-413d-4c1c-b58f-6e6913837a88", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25387075043200, "endTime": 25389205929600}, "additional": {"time": {"year": 2025, "month": 7, "day": 21, "hour": 19, "minute": 36}, "markType": "history", "category": "build", "state": "failed"}}, {"head": {"id": "57e5cece-9072-42ad-a1f5-c7856108a9cd", "name": "BUILD FAILED in 2 s 130 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389206379600}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "0b7e88cb-e774-4102-98f9-975307aa8d37", "name": "Update task entry:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389209743700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f576826-80e9-4e46-bb16-a30919ea1cf6", "name": "Update task entry:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389212001400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e53f3f5-cf59-4f34-b467-3339e0b6bb99", "name": "Update task entry:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389212855600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c544c3eb-36a5-4f49-a697-07ae0bb80b30", "name": "Update task entry:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389213737000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe82fdd7-dff9-4cc0-8c31-d3c4ed345be3", "name": "Update task entry:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389214731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac0fc1de-288b-4070-a82c-605e21a6a9d5", "name": "Update task entry:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389215652600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "969aaea3-d38b-4760-99e8-645efed7f167", "name": "Incremental task entry:default@PreBuild post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389217349300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4323b99d-0bd1-4292-93db-348b0f3106c4", "name": "There is no need to refresh cache, since the incremental task ijkplayer:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389217387100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e814169-af5a-4a40-baa1-87355e390c50", "name": "Update task entry:default@GenerateMetadata input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389218798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05844d93-77f6-4985-8723-0d03b32f239c", "name": "Update task entry:default@GenerateMetadata output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389220712300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0e03c0b-5e83-4d28-9125-a304362add4d", "name": "Incremental task entry:default@GenerateMetadata post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389221261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75d8317-ec05-45f3-beb7-6056d1c69f1a", "name": "Update task entry:default@CreateBuildProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389221304300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a6198da-7b36-416a-9617-5fee567fb415", "name": "Update task entry:default@CreateBuildProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389221594700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f2bbbd-be9f-4ec5-9f43-0fed4e574d30", "name": "Update task entry:default@CreateBuildProfile output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389223263200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a64e179-e58c-48a1-9889-9cdc0db9ea8d", "name": "Incremental task entry:default@CreateBuildProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389224136000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "521ec4dc-46ed-4584-8fab-a68298eaa020", "name": "Update task ijkplayer:default@CreateHarBuildProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389224184100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4caeeae-1f58-4d3a-a38f-a9cb64f665d7", "name": "Update task ijkplayer:default@CreateHarBuildProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389224496600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "327ee08f-267a-449a-88ee-7acfa531beb8", "name": "Update task ijkplayer:default@CreateHarBuildProfile output file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389224777400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c282d73-efec-442d-92f4-a49b97b5c684", "name": "Incremental task ijkplayer:default@CreateHarBuildProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389225126800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed3dc023-02ca-4466-bf9c-dbf2fcbcccb5", "name": "Update task entry:default@GenerateLoaderJson output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader\\default\\loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389225452500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ff91918-368e-4acf-aa60-be9b4f137175", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389226019600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25186301-081f-4c8a-af79-839092ef7c72", "name": "Update task ijkplayer:default@MergeProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389226163200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b91ee8-12fa-4186-a528-f06ff939be22", "name": "Update task ijkplayer:default@MergeProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389227118300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d562e453-4d3c-4ea4-b7ef-f8b1e127ac5e", "name": "Update task ijkplayer:default@MergeProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389227483300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a07a675-d26a-4540-9ca3-1e417473b3b7", "name": "Update task ijkplayer:default@MergeProfile output file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\merge_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389227818300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f5fef0c-9a5e-413b-8ab0-454729e3be07", "name": "Incremental task ijkplayer:default@MergeProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389228229300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d37b525a-a2b8-4f22-ac2c-4896a01c660a", "name": "Update task entry:default@MergeProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389228468100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e50a86b-d212-4017-914e-f31d619821d8", "name": "Update task entry:default@MergeProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389228809200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5910b370-5f71-433e-a230-9b36602f662a", "name": "Update task entry:default@MergeProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389229089300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3692ce05-cd43-401f-b08a-6f3568627f27", "name": "Update task entry:default@MergeProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389230465600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab2e2f1-772c-4bcc-ae51-19cb11855965", "name": "Update task entry:default@MergeProfile output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389231150100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "893b9650-0dba-4b41-92bf-6e7e2409d484", "name": "Incremental task entry:default@MergeProfile post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389233182600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "903e33ef-17bf-4b9c-a620-8d6a9c25fb15", "name": "Update task entry:default@MakePackInfo input file:D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389234325400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e06d8b6-d091-4d84-bf73-f21873b40208", "name": "Update task entry:default@MakePackInfo input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389234777400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b2e5d28-0e5a-4e04-ad9f-5060d7b85e54", "name": "Update task entry:default@MakePackInfo input file:D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389235091400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f6063c6-5dcd-4942-864e-dc92e4349849", "name": "Update task entry:default@MakePackInfo output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\outputs\\default\\pack.info cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389237198000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b90b50e6-3a6e-4dd3-86fc-6337974fd1ac", "name": "Incremental task entry:default@MakePackInfo post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389237515000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6559b744-f029-425b-8a2e-4b2dbb2a398e", "name": "Update task entry:default@ProcessProfile input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389237557100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de179b9d-ba8a-4477-8cd1-840e8b9707fe", "name": "Update task entry:default@ProcessProfile output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389237808100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aba32a5-a7f4-41f7-93d0-9e2937d0c6c0", "name": "Incremental task entry:default@ProcessProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389238127300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b68e0209-1f58-4383-b17e-79a3e997faf8", "name": "Update task entry:default@ProcessResource output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389238178800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "443fd52f-4744-4248-97da-315600ab269f", "name": "Incremental task entry:default@ProcessResource post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389238942100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "488a1c2b-ef62-49b3-8c51-0990a8cd2a5e", "name": "Update task entry:default@CompileResource input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389239156200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9819fa22-46ee-4add-83c2-888f596c13fd", "name": "Update task entry:default@CompileResource input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389319782700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "213113f9-72dc-4080-9739-84456cf2dbfb", "name": "Update task entry:default@CompileResource input file:D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389328142600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ee5fad-5de7-498d-ba95-2a96aee3c209", "name": "Update task entry:default@CompileResource input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389331627200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63f80d69-739b-4a73-ab2b-640c816fb20a", "name": "Update task entry:default@CompileResource input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389333174700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3530c896-0e9f-48ca-8f09-e8f1dd62f297", "name": "Update task entry:default@CompileResource output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389334319300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98759bd-597f-419b-ac51-5275f07b10d9", "name": "Update task entry:default@CompileResource output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389342015800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1f997b1-f42a-41ad-a9bf-01a6d3978bb2", "name": "Update task entry:default@CompileResource output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389342403300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81a18e5a-974f-476a-a521-b2a519b640ab", "name": "Incremental task entry:default@CompileResource post-execution cost:105 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389343636100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78992c8e-8b5d-4b6b-950f-b5dc0cd3b8ee", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389347078000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "383e3df5-ecc3-4c51-955e-425e76d6283c", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389347841400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69397dde-34d9-4219-88d5-4b0c16b206ac", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389348846500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa4562b-26c5-4d7d-9110-2ac50ab0a219", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389356254200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d42e83-cf89-44c7-8eb0-f1590f31fa27", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389356879600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9de803a2-53ba-489b-a890-eeb0f4d5c536", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389357443100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df51ab1d-6549-40e6-a6f6-79879913a443", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389358279200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85e636b6-2c83-4b51-95ce-d579e09f372c", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389363067300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3583867b-474f-4872-a0b0-472545a2f559", "name": "Update task entry:default@CompileArkTS input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389363302000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee48e333-ecc9-4941-9176-d601481542c9", "name": "Update task entry:default@CompileArkTS output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389364158100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc3483d-eb74-4126-8a71-6b9b9a1ef2a6", "name": "Incremental task entry:default@CompileArkTS post-execution cost:21 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389364574000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23565857-37a6-43c1-b45f-e181e2c5c5af", "name": "Update task entry:default@BuildJS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389368392600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d777eb34-44ac-489f-9cf4-919de3aa7224", "name": "Update task entry:default@BuildJS input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389369141000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b91eaa4-0df9-40b4-b3fa-a0450da138ae", "name": "Update task entry:default@BuildJS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389369389500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba30a016-a751-4e9e-8e7d-9ca2986a6e60", "name": "Update task entry:default@BuildJS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389369621900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b753e449-c19b-4dee-80d7-10532cd77df0", "name": "Update task entry:default@BuildJS input file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389369861900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253e5197-7cfa-4ec4-9c25-a52956e31042", "name": "Update task entry:default@BuildJS output file:D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389374495100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30556d0e-f48b-43b0-9f19-99fdaa3f9b0a", "name": "Incremental task entry:default@BuildJS post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25389375256800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}