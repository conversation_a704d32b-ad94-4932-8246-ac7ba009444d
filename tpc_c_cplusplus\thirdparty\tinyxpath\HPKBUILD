# Contributor: huang<PERSON><PERSON><PERSON> <<EMAIL>>
# Maintainer: huang<PERSON><PERSON><PERSON> <<EMAIL>>
pkgname=tinyxpath
pkgver=1.3.1
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL-2.1" "GPL-2.0")
depends=()
makedepends=()

source="https://sourceforge.net/projects/tinyxpath/files/TinyXPath%20%28Linux%20-%20tar.gz%29/TinyXPath%201.3.1/tinyxpath_1_3_1.tgz"
downloadpackage=true
autounpack=false
buildDir=$pkgname-$pkgver
packagename=tinyxpath_1_3_1.tgz  # 压缩包名
decompFlag=true

prepare() {
    if [ $decompFlag == true ];then
        mkdir -p $buildDir
        tar xvf $packagename -C $buildDir > /dev/null
        decompFlag=false
        cd $buildDir
        patch -p1 < `pwd`/../tinyxpath_oh_pkg.patch
        cd ${OLDPWD}
    fi
    mkdir -p $buildDir/$ARCH-build
}

build() {
    cd $buildDir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DBUILD_SAMPLE=ON -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $buildDir/$ARCH-build
    make install >> build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$buildDir #${PWD}/$buildDir
}
