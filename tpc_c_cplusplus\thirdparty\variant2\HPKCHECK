# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

checkprepare() {
    # 添加lib子目录variant2下的库路径到环境变量
    cd $LYCIUM_THIRDPARTY_ROOT/$pkgname/$ARCH-build
    find . -type f -name "*.so*" -exec cp {} ./ \;
    export LD_LIBRARY_PATH=$LYCIUM_THIRDPARTY_ROOT/$pkgname/$pkgname-$ARCH-build/:$LD_LIBRARY_PATH
    cd $OLDPWD
}

# 在OH环境执行测试的接口
openharmonycheck() {
    res=0                  
    cd $LYCIUM_THIRDPARTY_ROOT/$pkgname/$builddir-$ARCH-build/test/
    sh variant2_test.sh > ${logfile} 2>&1
    res=$?      
    cd $OLDPWD                         
    return $res  
}