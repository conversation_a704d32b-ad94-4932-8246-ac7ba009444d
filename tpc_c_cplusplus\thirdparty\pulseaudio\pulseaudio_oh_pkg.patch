diff --git a/meson.build b/meson.build
index d4cdbd6..d8d1cc8 100644
--- a/meson.build
+++ b/meson.build
@@ -149,7 +149,8 @@ cdata.set_quoted('PA_MACHINE_ID_FALLBACK', join_paths(localstatedir, 'lib', 'dbu
 cdata.set_quoted('PA_SRCDIR', join_paths(meson.current_source_dir(), 'src'))
 cdata.set_quoted('PA_BUILDDIR', meson.current_build_dir())
 cdata.set_quoted('PA_SOEXT', '.so')
-cdata.set_quoted('PA_DEFAULT_CONFIG_DIR', pulsesysconfdir)
+# 配置文件路径保持与OHOS系统一致，方便用户直接使用
+cdata.set_quoted('PA_DEFAULT_CONFIG_DIR', '/etc/pulse')
 cdata.set('PA_DEFAULT_CONFIG_DIR_UNQUOTED', pulsesysconfdir)
 cdata.set_quoted('PA_BINARY', join_paths(bindir, 'pulseaudio'))
 cdata.set_quoted('PA_SYSTEM_RUNTIME_PATH', join_paths(localstatedir, 'run', 'pulse'))
diff --git a/src/meson.build b/src/meson.build
index 96dcec3..1cb7509 100644
--- a/src/meson.build
+++ b/src/meson.build
@@ -198,7 +198,7 @@ else
     libpulsecommon_headers,
     include_directories : [configinc, topinc],
     c_args : [pa_c_args],
-    link_args : [nodelete_link_args],
+    link_args : [nodelete_link_args, '-static'],# 函数gethostid找不到，使用libc.a静态链接
     install : true,
     install_dir : privlibdir,
     dependencies : [
