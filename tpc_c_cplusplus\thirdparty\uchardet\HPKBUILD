# Contributor: <PERSON> <hanjin<PERSON><PERSON>@foxmail.com>
# Maintainer: <PERSON> <hanjin<PERSON><EMAIL>>
pkgname=uchardet
pkgver=v0.0.8
pkgrel=0
pkgdesc="uchardet is an encoding detector library, which takes a sequence of bytes in an unknown character encoding without any additional information, and attempts to determine the encoding of the text. Returned encoding names are iconv-compatible."
url="https://www.freedesktop.org/wiki/Software/uchardet/"
archs=("armeabi-v7a" "arm64-v8a")
license=("MOZILLA PUBLIC LICENSE" "GPLV2" "LGPLV2")
depends=()
makedepends=()

source="https://gitlab.freedesktop.org/$pkgname/$pkgname/-/archive/$pkgver/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd "$builddir"
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
