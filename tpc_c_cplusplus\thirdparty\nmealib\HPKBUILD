# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, Sunjiamei<<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>


pkgname=nmealib
pkgver=0.6.5
pkgrel=0
pkgdesc="Nmealib is an open-source C++library used to parse data from the NMEA 0183 protocol. NMEA 0183 is a standard data exchange protocol used for Global Positioning System (GPS) navigation devices. By using nmealib, developers can easily process data from GPS receivers or other NMEA devices to obtain the required location, speed, and other navigation information. This is very useful for developing navigation applications, location tracking systems, or other geolocation related applications."
url="https://github.com/Paulxia/nmealib"
archs=("armeabi-v7a" "arm64-v8a")
license=("GNU Lesser General Public License v2.1")
depends=()
makedepends=()

source="https://github.com/Paulxia/$pkgname/archive/refs/tags/v$pkgver.zip"

autounpack=true
downloadpackage=true
buildtools="make"

builddir=$pkgname-${pkgver}
packagename=$builddir.zip

cc=
ar=
cXX=
source envset.sh
patchflag=true

# nmealib 采用makefile编译构建，为了保留构建环境(方便测试)。因此同一份源码在解压后分为两份,各自编译互不干扰
prepare() {
    if [ $patchflag == true ]
    then
        cd $builddir
        patch -p1 < `pwd`/../nmealib_oh_pkg.patch > $publicbuildlog 2>&1
        patchflag=false
        cd $OLDPWD
    fi

    cp -rf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        cXX=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang++
        cc=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang
        ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        cXX=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang++
        cc=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang
        ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
    fi
}

build() {
    cd $builddir-$ARCH-build
    $MAKE LIB="$ar cr" CC="$cc" > $buildlog 2>&1 # 
    $MAKE LIB="$ar cr" CC="$cXX" >> $buildlog 2>&1 # 存在需要clang++编译的文件，上一个make报错终止编译，已经编译过的文件生成.o文件
    $MAKE LIB="$ar cr" CC="$cc" >> $buildlog 2>&1 # 存在需要clang编译的文件，上一个make报错终止编译，已经编译过的文件生成.o文件，当前make可完成剩余文件编译
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    install_dir=${LYCIUM_ROOT}/usr/${pkgname}/${ARCH}
    mkdir -p ${install_dir}/lib
    cd ${builddir}-${ARCH}-build/
    cp -arf lib/libnmea.so ${install_dir}/lib/
    mkdir -p ${install_dir}/include
    cp -arf ./include/nmea ${install_dir}/include
    cd ${OLDPWD}
}

check() {
    cd $builddir-$ARCH-build/samples
    $MAKE LIB="$ar cr" CC="$cc" >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    unset cc
    unset ar
    unset cXX
    echo "The test must be on an OpenHarmony device!"
    return $ret
}

cleanbuild() {
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build
}
