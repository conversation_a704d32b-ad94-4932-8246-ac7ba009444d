# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=speechd
pkgver=0.11.5
pkgrel=0
pkgdesc="This is the Speech Dispatcher project (speech-dispatcher). "
url="https://github.com/brailcom/speechd"
archs=("armeabi-v7a" "arm64-v8a")
license=("GNU LESSER GENERAL PUBLIC LICENSE")
depends=("espeak" "pulseaudio" "glib" "pcre2" "libsndfile" "dotconf")
makedepends=()
# 原仓地址：source="https://github.com/brailcom/speechd/releases/download/0.11.5/speech-dispatcher-0.11.5.tar.gz" 因网络原因shi'y使用镜像地址
source="https://gitee.com/lycium_pkg_mirror/$pkgname/repository/archive/$pkgver.zip"

autounpack=true
downloadpackage=true
buildtools="configure"

patchflag=true

builddir=${pkgname}-${pkgver}
packagename=$builddir.zip

source envset.sh
host=
prepare() {
    if $patchflag
    then
        cd $builddir
        # pthread_cancel等函数openharmony目前不支持，将pthread_join修改成pthread_detach
        # setvbuf接口导致pipe使用异常
        patch -p1 < ../speechd_ohos.patch > $buildlog 2>&1
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi

    cp -rf ${builddir} $builddir-${ARCH}-build

    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    else
        echo "${ARCH} not support"
        return -1
    fi

    cd $builddir-$ARCH-build
    autoreconf -ifv >> $buildlog 2>&1
    cd $OLDPWD
    
}

build() {
    cd $builddir-$ARCH-build
    PKG_CONFIG_LIBDIR=$pkgconfigpath ./configure "$@" --host=$host ac_cv_func_malloc_0_nonnull=yes \
    ac_cv_func_realloc_0_nonnull=yes  gt_func_gnugettext_libintl=no --with-espeak \
    LDFLAGS="-L$LYCIUM_ROOT/usr/espeak/$ARCH/lib" \
    CFLAGS="-I$LYCIUM_ROOT/usr/libsndfile/$ARCH/include -I$LYCIUM_ROOT/usr/dotconf/$ARCH/include \
    -I$LYCIUM_ROOT/usr/espeak/$ARCH/include -Wno-int-conversion" \
    CXXFLAGS="-I$LYCIUM_ROOT/usr/libsndfile/$ARCH/include -I$LYCIUM_ROOT/usr/dotconf/$ARCH/include \
    -I$LYCIUM_ROOT/usr/espeak/$ARCH/include -Wno-int-conversion" >> $buildlog 2>&1
    sed -i '$a\ # define gettext(Msgid) ((const char *) (Msgid))' config.h
    ${MAKE} V=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    ${MAKE} install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    cd $builddir-$ARCH-build
    $MAKE check >> $buildlog 2>&1
    cd $OLDPWD
    echo "The test must be on an OpenHarmony device!"
    # how to run test
    # cd $builddir-$ARCH-build/src/tests
    # ../../run-speechd && ./clibrary
}

recoverpkgbuildenv() {
    unset host
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    else
        echo "${ARCH} not support"
        return -1
    fi
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir ${PWD}/$builddir-armeabi-v7a-build ${PWD}/$builddir-arm64-v8a-build #${PWD}/$packagename
}
