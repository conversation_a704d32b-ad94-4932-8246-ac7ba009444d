# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>, zhaoxu <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=DLib
pkgver=v1.1-free
pkgrel=0
pkgdesc="C++ library with several utilities"
url="https://github.com/dorian3d/DLib"
archs=("armeabi-v7a" "arm64-v8a")
license=("Copyright (c) 2015 <PERSON>. http://doriangalvez.com All rights reserved.")
depends=("boost" "opencv_3.4.1")
makedepends=()
source="https://github.com/dorian3d/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="cmake"
patchflag=true

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
    cd $builddir

    if $patchflag
    then
        # 添加boost依赖库头文件查询路径
        patch -p1 < ../DLib_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
    fi

    cd $OLDPWD
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make VERBOSE=1 -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # TODO
}

cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packageName
}
