# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=glib
pkgver=2.77.1
pkgrel=0
pkgdesc="GLib is a general-purpose, portable utility library, which provides many useful data types, macros, type conversions, string utilities, file utilities, a mainloop abstraction, and so on."
url="https://docs.gtk.org/glib/"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache 2.0" "CC0-1.0" "GPL-2.0" "LGPL-2.1" "LLVM" "old-glib" "MIT")
depends=("libffi" "pcre2")
makedepends=("meson" "ninja") 

# 原仓位置: https://github.com/GNOME/${pkgname}/archive/refs/tags/$pkgver.tar.gz, 因网络原因使用镜像
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"

downloadpackage=true
autounpack=true
buildtools="meson"

builddir=$pkgname-$pkgver
packagename=$pkgname-$pkgver.zip
pkgconfigpath=
clonesubmodule=true

prepare() {
    # 下载子模块
    if $clonesubmodule
    then
        # 下载gvdb,因网络原因使用镜像， 原仓地址： https://github.com/GNOME/gvdb.git
        git clone https://gitee.com/lycium_pkg_mirror/gvdb.git $builddir/subprojects/gvdb
        if [ $? -ne 0 ];then
            return -1
        fi
        cd $builddir/subprojects/gvdb
        git checkout --detach 0854af0fdb6d527a8d1999835ac2c5059976c210
        cd $OLDPWD
        # 下载 libintl, meson 下载会失败, 原仓地址: https://github.com/frida/proxy-libintl.git 
        git  clone --depth 1 --branch 0.4 https://gitee.com/lycium_pkg_mirror/proxy-libintl.git $builddir/subprojects/proxy-libintl
        if [ $? -ne 0 ];then
            return -2
        fi
        clonesubmodule=false
    fi

    # 依赖库加入 pkg_config_path 路径
    for depend in ${depends[@]}
    do
        dependpath=$LYCIUM_ROOT/usr/$depend/$ARCH/lib/pkgconfig
        if [ ! -d ${dependpath} ]
        then
            continue
        fi
        pkgconfigpath=$pkgconfigpath"${dependpath}:"
    done
    pkgconfigpath=${pkgconfigpath%:*}

    cp $ARCH-cross-file.txt $builddir
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ohos_sdk_path=${OHOS_SDK//\//\\\/}
    sed -i 's/ohos_sdk/'"$ohos_sdk_path"'/g' $ARCH-cross-file.txt

    # 需要设置pkg路径
    meson setup $ARCH-build --cross-file $ARCH-cross-file.txt \
        --pkg-config-path=$pkgconfigpath \
        --prefix=$LYCIUM_ROOT/usr/$pkgname/$ARCH > $ARCH-build/build.log 2>&1
    ninja -C $ARCH-build -v >> $ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    ninja -v -C $ARCH-build install >> build.log 2>&1
    # 还原
    unset pkgconfigpath
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # TODO
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir
}
