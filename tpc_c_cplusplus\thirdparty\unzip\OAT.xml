﻿<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <oatconfig>
        <filefilterlist>
            <filefilter name="copyrightPolicyFilter" desc="Filters for compatibility，license header policies">
                <filteritem type="filename" name="HPKBUILD" desc="自己编写的shell脚本，不涉及版权问题"/>
                <filteritem type="filename" name="SHA512SUM" desc="自己编写的shell脚本，不涉及版权问题"/>
            </filefilter>
            <filefilter name="defaultPolicyFilter" desc="Filters for compatibility，license header policies">
                <filteritem type="filename" name="HPKBUILD" desc="自己编写的shell脚本，不涉及版权问题"/>
                <filteritem type="filename" name="SHA512SUM" desc="自己编写的shell脚本，不涉及版权问题"/>
            </filefilter>
            <filefilter name="defaultPolicyFilter" desc="Filters for copyright header policies"></filefilter>
            <filefilter name="binaryFileTypePolicyFilter" desc="Filters for binary file policies">
                <filteritem type="filename" name="unzip_install.png" desc="测试截图文件，不涉及版权问题"/>
				<filteritem type="filename" name="unzip_test.png" desc="测试截图文件，不涉及版权问题"/>
				<filteritem type="filename" name="unzip_usage.png" desc="测试截图文件，不涉及版权问题"/>
            </filefilter>
        </filefilterlist>
        <policylist>
            <policy name="projectPolicy" desc=""></policy>
        </policylist>
    </oatconfig>
</configuration>