# 寻找预编译的OpenHarmony依赖库

## 概述
由于空间限制，寻找预编译的依赖库是最实用的解决方案。

## 可能的来源

### 1. OpenHarmony官方仓库
- **TPC仓库**: https://gitee.com/openharmony-sig/tpc_c_cplusplus
- **预编译库**: 可能包含已编译的依赖库
- **查找路径**: `tpc_c_cplusplus/prebuilts/` 或 `tpc_c_cplusplus/lycium/usr/`

### 2. 开发者社区
- **Gitee搜索**: 搜索 "openharmony ffmpeg" "openharmony ijkplayer"
- **GitHub搜索**: 搜索相关的预编译库
- **开发者论坛**: OpenHarmony开发者论坛

### 3. 商业解决方案
- **华为开发者联盟**: 可能提供预编译库
- **第三方供应商**: 专门提供OpenHarmony组件的公司

## 下载预编译库的步骤

### 步骤1: 检查TPC仓库
```bash
# 克隆TPC仓库（只下载预编译部分）
git clone --depth=1 --filter=blob:none https://gitee.com/openharmony-sig/tpc_c_cplusplus.git tpc_prebuilt

# 查找预编译库
find tpc_prebuilt -name "*ffmpeg*" -type d
find tpc_prebuilt -name "*soundtouch*" -type d  
find tpc_prebuilt -name "*yuv*" -type d
find tpc_prebuilt -name "*openssl*" -type d
```

### 步骤2: 检查lycium usr目录
```bash
# 如果lycium已下载，检查usr目录
ls -la tpc_c_cplusplus/lycium/usr/ 2>/dev/null || echo "lycium未下载"
```

### 步骤3: 手动搜索和下载
在浏览器中搜索：
- "openharmony ffmpeg prebuilt"
- "openharmony ijkplayer dependencies"
- "harmonyos native libraries"

## 预编译库的目录结构

正确的预编译库应该包含：

### FFmpeg
```
ffmpeg/
├── include/
│   ├── libavcodec/
│   ├── libavformat/
│   ├── libavutil/
│   └── ...
└── lib/
    ├── libavcodec.a
    ├── libavformat.a
    ├── libavutil.a
    └── ...
```

### soundtouch
```
soundtouch/
├── include/
│   └── soundtouch/
└── lib/
    └── libsoundtouch.a
```

### libyuv
```
yuv/
├── include/
│   └── libyuv/
└── lib/
    └── libyuv.a
```

### OpenSSL
```
openssl/
├── include/
│   └── openssl/
└── lib/
    ├── libssl.a
    └── libcrypto.a
```

## 验证预编译库

下载后验证库文件：
```bash
# 检查库文件架构
file path/to/lib/*.a

# 应该显示类似：
# libavcodec.a: current ar archive
# 或包含 ARM/AArch64 等OpenHarmony支持的架构
```

## 安装预编译库

```bash
# 复制到项目目录
cp -r downloaded_ffmpeg/* ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/
cp -r downloaded_soundtouch/* ijkplayer/src/main/cpp/third_party/soundtouch/
cp -r downloaded_yuv/* ijkplayer/src/main/cpp/third_party/yuv/
cp -r downloaded_openssl/* ijkplayer/src/main/cpp/third_party/openssl/
```

## 联系渠道

如果找不到预编译库，可以：

### 1. 提交Issue
在项目仓库提交Issue询问预编译库

### 2. 联系维护者
- 项目README中的联系方式
- 开发者邮箱
- 技术支持

### 3. 开发者社区
- OpenHarmony开发者论坛
- 华为开发者社区
- 相关QQ群/微信群

## 临时解决方案

如果暂时找不到预编译库：

### 1. 使用源码头文件
```bash
# 只复制头文件，用于编译通过
./simple_build.sh
# 选择"源码复制方案"
```

### 2. 创建空的库文件
```bash
# 创建空的.a文件（仅用于链接测试）
mkdir -p ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/lib
touch ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/lib/libavcodec.a
# ... 创建其他必要的空库文件
```

### 3. 修改CMakeLists.txt
暂时注释掉依赖库的链接，先让项目编译通过

## 长期解决方案

1. **购买云服务器**进行编译
2. **使用朋友的Linux机器**
3. **等待官方提供预编译库**
4. **参与社区贡献**，帮助提供预编译库
