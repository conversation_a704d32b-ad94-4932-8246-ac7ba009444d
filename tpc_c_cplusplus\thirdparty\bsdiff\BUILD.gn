# Copyright (c) 2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

declare_args() {
  enable_bsdiff_test = false
}

ohos_shared_library("bsdiff_bspatch") {
  sources = [
    "bsdiff/bsdiff.c",
    "bsdiff/bspatch.c",
  ]

  include_dirs = [ "./bsdiff" ]

  part_name = "bsdiff"
}

ohos_executable("bsdiff_example") {
  sources = [ "bsdiff/bsdiff.c" ]
  include_dirs = [
    "./bsdiff",
    "//third_party/bzip2",
  ]

  deps = [ "//third_party/bzip2:libbz2" ]
  cflags = [ "-DBSDIFF_EXECUTABLE" ]

  part_name = "bsdiff"
}

ohos_executable("bspatch_example") {
  sources = [ "bsdiff/bspatch.c" ]
  include_dirs = [
    "./bsdiff",
    "//third_party/bzip2",
  ]

  deps = [ "//third_party/bzip2:libbz2" ]
  cflags = [ "-DBSPATCH_EXECUTABLE" ]
  part_name = "bsdiff"
}

group("bsdiff") {
  deps = [ ":bsdiff_bspatch" ]
  if (enable_bsdiff_test) {
    deps += [
      ":bsdiff_example",
      ":bspatch_example",
    ]
  }
}
