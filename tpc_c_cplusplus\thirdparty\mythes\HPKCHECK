# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
linuxresult=$LYCIUM_ROOT/main/$pkgname/mythes_linux_result.txt
openharmonycheck() {
    res=0
    cd $pkgname-$ARCH-build
    ./.libs/example th_en_US_new.idx th_en_US_new.dat checkme.lst > $logfile 2>&1
    res=$?
    if [ $res -eq 0 ]
    then
        ./.libs/example morph.idx morph.dat morph.lst morph.aff morph.dic >> $logfile 2>&1
        res=$?
    fi

    if [ $res -eq 0 ]
    then
        ret=`diff $linuxresult $logfile`
        if [ ! -z "$ret" ]
        then
            res=1
            echo "check $pkgname failed" >> $logfile
            echo "$ret" >> $logfile
        fi
    fi

    cd $OLDPWD

    return $res
}
