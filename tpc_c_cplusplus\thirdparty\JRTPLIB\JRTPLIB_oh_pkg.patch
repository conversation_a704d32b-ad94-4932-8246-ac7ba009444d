--- JRTPLIB-3.11.2/tests/tcptest.cpp	2020-04-18 17:44:45.000000000 +0800
+++ JRTPLIB-3.11.2/tests/tcptest2.cpp	2024-06-11 11:08:27.432850300 +0800
@@ -154,7 +154,7 @@
 	servAddr.sin_family = AF_INET;
 	servAddr.sin_port = htons(atoi(argv[1]));
 
-	if (bind(listener, (struct sockaddr *)&servAddr, sizeof(servAddr)) != 0)
+	if (::bind(listener, (struct sockaddr *)&servAddr, sizeof(servAddr)) != 0)
 	{
 		cerr << "Can't bind listener socket" << endl;
 		return -1;
