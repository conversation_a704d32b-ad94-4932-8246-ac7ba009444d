#!/bin/bash

# 快速检查必要工具是否安装

echo "========================================"
echo "快速工具检查"
echo "========================================"

# 定义必要工具列表
ESSENTIAL_TOOLS=(
    "gcc:GCC编译器"
    "make:Make构建工具"
    "cmake:CMake构建系统"
    "git:Git版本控制"
    "wget:下载工具"
    "unzip:解压工具"
    "tar:打包工具"
    "zip:压缩工具"
    "autoconf:自动配置工具"
    "automake:自动化Make工具"
    "libtool:库工具"
    "pkg-config:包配置工具"
)

OPTIONAL_TOOLS=(
    "python3:Python3"
    "curl:下载工具(备选)"
    "ninja:Ninja构建系统"
)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 检查单个工具
check_tool() {
    local tool_info="$1"
    local tool_name="${tool_info%%:*}"
    local tool_desc="${tool_info##*:}"
    
    if command -v "$tool_name" &> /dev/null; then
        local version=$($tool_name --version 2>/dev/null | head -n1 | cut -d' ' -f1-3)
        echo -e "✓ ${GREEN}[已安装]${NC} $tool_desc ($version)"
        return 0
    else
        echo -e "✗ ${RED}[缺失]${NC} $tool_desc"
        return 1
    fi
}

# 检查必要工具
echo "检查必要工具..."
echo "----------------------------------------"
missing_essential=0
for tool in "${ESSENTIAL_TOOLS[@]}"; do
    if ! check_tool "$tool"; then
        missing_essential=$((missing_essential + 1))
    fi
done

echo ""
echo "检查可选工具..."
echo "----------------------------------------"
missing_optional=0
for tool in "${OPTIONAL_TOOLS[@]}"; do
    if ! check_tool "$tool"; then
        missing_optional=$((missing_optional + 1))
    fi
done

echo ""
echo "========================================"
echo "检查结果汇总"
echo "========================================"

if [ $missing_essential -eq 0 ]; then
    echo -e "${GREEN}✓ 所有必要工具都已安装！${NC}"
else
    echo -e "${RED}✗ 缺少 $missing_essential 个必要工具${NC}"
fi

if [ $missing_optional -gt 0 ]; then
    echo -e "${YELLOW}⚠ 缺少 $missing_optional 个可选工具${NC}"
fi

echo ""

# 提供安装建议
if [ $missing_essential -gt 0 ] || [ $missing_optional -gt 0 ]; then
    echo "========================================"
    echo "安装建议"
    echo "========================================"
    echo "在MSYS2中运行以下命令安装缺失的工具："
    echo ""
    echo "# 更新包管理器"
    echo "pacman -Syu"
    echo ""
    echo "# 安装基础开发工具"
    echo "pacman -S --needed base-devel mingw-w64-x86_64-toolchain"
    echo ""
    echo "# 安装具体工具"
    echo "pacman -S git wget unzip cmake make autoconf automake libtool pkg-config"
    echo ""
    echo "# 安装可选工具"
    echo "pacman -S python3 curl ninja"
fi

# 检查MSYS2环境
echo "========================================"
echo "环境检查"
echo "========================================"

if [[ "$OSTYPE" == "msys" ]]; then
    echo -e "✓ ${GREEN}[正确]${NC} 当前在MSYS2环境中"
else
    echo -e "✗ ${RED}[错误]${NC} 当前不在MSYS2环境中"
    echo "请使用MSYS2终端运行此脚本"
fi

# 检查包管理器
if command -v pacman &> /dev/null; then
    echo -e "✓ ${GREEN}[可用]${NC} pacman包管理器"
else
    echo -e "✗ ${RED}[不可用]${NC} pacman包管理器"
fi

echo ""

# 最终建议
if [ $missing_essential -eq 0 ]; then
    echo "========================================"
    echo "下一步"
    echo "========================================"
    echo "工具检查通过！您可以继续进行以下操作："
    echo ""
    echo "1. 设置OpenHarmony SDK环境变量："
    echo "   export OHOS_SDK=/path/to/your/ohos-sdk"
    echo ""
    echo "2. 运行完整环境检查："
    echo "   ./check_environment.sh"
    echo ""
    echo "3. 开始编译依赖库："
    echo "   ./build_msys2.sh"
else
    echo "========================================"
    echo "需要先安装缺失的工具"
    echo "========================================"
    echo "请先安装上述缺失的工具，然后重新运行此检查脚本"
fi
