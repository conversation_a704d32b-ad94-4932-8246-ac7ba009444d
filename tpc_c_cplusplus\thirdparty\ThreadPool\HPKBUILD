# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, Sunjiamei<<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>


pkgname=ThreadPool
pkgver=master
pkgrel=0
pkgdesc="A simple C++11 Thread Pool implementation."
url="https://github.com/progschj/threadpool"
archs=("armeabi-v7a" "arm64-v8a")
license=("zlib License")
depends=()
makedepends=()

commitid=9a42ec1329f259a5f4881a291db1dcb8f2ad9040
source="https://github.com/progschj/$pkgname/archive/$commitid.zip"

autounpack=true
downloadpackage=true
buildtools="make"

builddir=$pkgname-${commitid}
packagename=$builddir.zip

ar=
cXX=
source envset.sh
patchflag=true

# ThreadPool 采用makefile编译构建，为了保留构建环境(方便测试)。因此同一份源码在解压后分为两份,各自编译互不干扰
prepare() {
    if [ $patchflag == true ]
    then
        cd $builddir
        patch -p1 < `pwd`/../ThreadPool_oh_pkg.patch > $publicbuildlog 2>&1
        patchflag=false
        cd $OLDPWD
    fi

    cp -rf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        cXX=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang++
        ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        cXX=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang++
        ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
    fi
}

# ThreadPool库仅有.h文件
build() {
    cd $builddir-$ARCH-build/
    $MAKE LIB="$ar cr" CXX="$cXX" > $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    install_dir=${LYCIUM_ROOT}/usr/${pkgname}/${ARCH}
    cd $builddir-$ARCH-build/
    mkdir -p ${install_dir}/include
    cp -arf ./ThreadPool.h ${install_dir}/include
    cd ${OLDPWD}
}

check() {
    if [ $ARCH == "armeabi-v7a" ]; then
        # 拷贝shared库到测试程序运行目录
        cp $OHOS_SDK/native/llvm/lib/arm-linux-ohos/libc++_shared.so $builddir-$ARCH-build
    elif [ $ARCH == "arm64-v8a" ]; then
        # 拷贝shared库到测试程序运行目录
        cp $OHOS_SDK/native/llvm/lib/aarch64-linux-ohos/libc++_shared.so $builddir-$ARCH-build
    else
        echo "Not support ${ARCH} yet"
        return -1
    fi
    echo "The test must be on an OpenHarmony device!"
    unset ar
    unset cXX
}

cleanbuild() {
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build
}
