# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=erlang-libdecaf
pkgver=2.1.1
pkgrel=0
pkgdesc="The libdecaf library is for elliptic curve research and practical application."
url="https://github.com/potatosalad/erlang-libdecaf"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT")
depends=()
makedepends=()
source="https://github.com/potatosalad/$pkgname/archive/refs/tags/$pkgver.tar.gz"
buildtools="cmake"

autounpack=true
downloadpackage=true

builddir=$pkgname-$pkgver
packagename=$pkgname-$pkgver.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" \
        -DOHOS_ARCH=$ARCH -B$ARCH-build -Sc_deps/ed448goldilocks -L  > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$? 
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf $builddir
}