# Contributor: wuping<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=libxls
pkgver=v1.6.2
pkgrel=0
pkgdesc="This is libxls, a C library for reading Excel files in the nasty old binary OLE format, plus a command-line tool for converting XLS to CSV (named, appropriately enough, xls2csv)."
url="https://github.com/libxls/libxls"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD")
depends=()
makedepends=()
install=
source="https://github.com/$pkgname/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"
builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz
source envset.sh
host=

prepare() {
    if $patchflag
    then
       cd $builddir
       # 由于configure.ac中的函数引起编译报错以下：
       # AX_CXX_COMPILE_STDCXX_11：syntax error near unexpected token `,'
       # AC_FUNC_MALLOC：undefined reference to malloc
       # AC_FUNC_REALLOC：undefined reference to realloc
       patch -p1 < `pwd`/../libxls_oh_pkg.patch
       patchflag=false
       cd $OLDPWD
    fi
    cp -r $builddir $pkgname-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
    cd $pkgname-$ARCH-build
    autoreconf -i > `pwd`/build.log 2>&1
    cd $OLDPWD
}

build() {
    cd $pkgname-$ARCH-build
    ./configure "$@" --host=$host --enable-static >> `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $pkgname-$ARCH-build
    make install >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    unset host
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 在OpenHarmony开发板中执行用例
    # make check    
}

cleanbuild(){
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build #${PWD}/$packagename
}
