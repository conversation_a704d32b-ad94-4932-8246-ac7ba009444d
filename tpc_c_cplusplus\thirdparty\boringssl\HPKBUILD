# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL> luozhu <<EMAIL>>
# <AUTHOR> <EMAIL> 

pkgname=boringssl
pkgver=fips-20220613
pkgrel=0
pkgdesc="BoringSSL is a fork of OpenSSL that is designed to meet Google's needs."
url="https://boringssl.googlesource.com/boringssl"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache License 2.0 and ISC License") # under OpenSSL licensing
depends=()
makedepends=("perl" "go")

# 官方下载地址https://github.com/google/$pkgname/archive/refs/tags/$pkgver.tar.gz受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"

autounpack=true
downloadpackage=true

builddir=${pkgname}-${pkgver}
packagename=$builddir.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
    # golang官网由于网络原因访问失败，设置golang国内代理
    export GO111MODULE=on
    export GOPROXY=https://goproxy.cn
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DCMAKE_C_FLAGS="-Wno-unused-command-line-argument" \
        -DCMAKE_CXX_FLAGS="-Wno-unused-command-line-argument" -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    if [ $ARCH == "armeabi-v7a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/arm-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    elif [ $ARCH == "arm64-v8a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/aarch64-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    else
        echo "${ARCH} not support"
        return -1
    fi
    echo "The test must be on an OpenHarmony device!"
    # real test cmd
    # ./crypto/crypto_test
    # ./crypto/urandom_test
    # ./ssl/ssl_test --gtest_filter=*-SSLTest.HostMatching
    # ./decrepit/decrepit_test
}

recoverpkgbuildenv(){
    unset GOPROXY GO111MODULE
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}

