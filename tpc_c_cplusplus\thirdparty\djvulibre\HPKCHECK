# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
sourcefile1=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/a.pbm
sourcefile2=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/b.pbm
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
openharmonycheck() {
    res=0
    cd ${pkgname}-${ARCH}-build/tools/
    ./c44 $sourcefile1 a.djuv > ${logfile} 2>&1
    res=$?
    if [ $res -eq 0 ]
    then
        ./c44 $sourcefile2 b.djuv >> ${logfile} 2>&1
        res=$?
    fi

    if [ $res -eq 0 ]
    then
        ./djvm -c merge.djuv a.djuv b.djuv >> ${logfile} 2>&1
        res=$?
    fi

    cd $OLDPWD

    return $res
}
