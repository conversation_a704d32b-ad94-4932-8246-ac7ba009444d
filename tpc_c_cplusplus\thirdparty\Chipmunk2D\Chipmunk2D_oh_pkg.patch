diff --git a/Chipmunk2D_test.c b/Chipmunk2D_test.c
new file mode 100644
index 0000000..9f32bf5
--- /dev/null
+++ b/Chipmunk2D_test.c
@@ -0,0 +1,68 @@
+/* The Hello Chipmunk Example source code comes from the official chipmunk documentation:
+ * http://chipmunk-physics.net/release/ChipmunkLatest-Docs/
+ */
+
+#include <stdio.h>
+#include <chipmunk.h>
+
+int main(void){
+  // cpVect is a 2D vector and cpv() is a shortcut for initializing them.
+  cpVect gravity = cpv(0, -100);
+
+  // Create an empty space.
+  cpSpace *space = cpSpaceNew();
+  cpSpaceSetGravity(space, gravity);
+
+  // Add a static line segment shape for the ground.
+  // We'll make it slightly tilted so the ball will roll off.
+  // We attach it to a static body to tell Chipmunk it shouldn't be movable.
+  cpShape *ground = cpSegmentShapeNew(cpSpaceGetStaticBody(space), cpv(-20, 5), cpv(20, -5), 0);
+  cpShapeSetFriction(ground, 1);
+  cpSpaceAddShape(space, ground);
+
+  // Now let's make a ball that falls onto the line and rolls off.
+  // First we need to make a cpBody to hold the physical properties of the object.
+  // These include the mass, position, velocity, angle, etc. of the object.
+  // Then we attach collision shapes to the cpBody to give it a size and shape.
+
+  cpFloat radius = 5;
+  cpFloat mass = 1;
+
+  // The moment of inertia is like mass for rotation
+  // Use the cpMomentFor*() functions to help you approximate it.
+  cpFloat moment = cpMomentForCircle(mass, 0, radius, cpvzero);
+
+  // The cpSpaceAdd*() functions return the thing that you are adding.
+  // It's convenient to create and add an object in one line.
+  cpBody *ballBody = cpSpaceAddBody(space, cpBodyNew(mass, moment));
+  cpBodySetPosition(ballBody, cpv(0, 15));
+
+  // Now we create the collision shape for the ball.
+  // You can create multiple collision shapes that point to the same body.
+  // They will all be attached to the body and move around to follow it.
+  cpShape *ballShape = cpSpaceAddShape(space, cpCircleShapeNew(ballBody, radius, cpvzero));
+  cpShapeSetFriction(ballShape, 0.7);
+
+  // Now that it's all set up, we simulate all the objects in the space by
+  // stepping forward through time in small increments called steps.
+  // It is *highly* recommended to use a fixed size time step.
+  cpFloat timeStep = 1.0/60.0;
+  for(cpFloat time = 0; time < 2; time += timeStep){
+    cpVect pos = cpBodyGetPosition(ballBody);
+    cpVect vel = cpBodyGetVelocity(ballBody);
+    printf(
+      "Time is %5.2f. ballBody is at (%5.2f, %5.2f). It's velocity is (%5.2f, %5.2f)\n",
+      time, pos.x, pos.y, vel.x, vel.y
+    );
+
+    cpSpaceStep(space, timeStep);
+  }
+
+  // Clean up our objects and exit!
+  cpShapeFree(ballShape);
+  cpBodyFree(ballBody);
+  cpShapeFree(ground);
+  cpSpaceFree(space);
+
+  return 0;
+}
diff --git a/src/cpHastySpace.c b/src/cpHastySpace.c
index 8dca425..a6b86ac 100644
--- a/src/cpHastySpace.c
+++ b/src/cpHastySpace.c
@@ -8,7 +8,7 @@
 
 //#include <sys/param.h >
 #ifndef _WIN32
-#include <sys/sysctl.h>
+#include <linux/sysctl.h>
 #include <pthread.h>
 #else
 #ifndef WIN32_LEAN_AND_MEAN
