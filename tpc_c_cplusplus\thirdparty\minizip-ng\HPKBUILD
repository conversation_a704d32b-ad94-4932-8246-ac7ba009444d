# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
pkgname=minizip-ng
pkgver=4.0.0
pkgrel=0
pkgdesc="minizip-ng is a zip manipulation library written in C that is supported on Windows, macOS, and Linux."
url="https://github.com/zlib-ng/minizip-ng"
archs=("armeabi-v7a" "arm64-v8a")
license=("zlib")
depends=("openssl" "xz" "bzip2" "zstd" "googletest")
makedepends=()
source="https://github.com/zlib-ng/${pkgname}/archive/refs/tags/${pkgver}.tar.gz"

autounpack=true
downloadpackage=true
buildtools="cmake"
builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    PKG_CONFIG_PATH="${pkgconfigpath}" cmake "$@" -DMZ_BUILD_TESTS=ON -DMZ_BUILD_UNIT_TESTS=ON -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd "$builddir"
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test cmd
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
