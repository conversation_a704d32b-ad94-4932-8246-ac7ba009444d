# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# Contributor: <PERSON> <<EMAIL>>
# Maintainer:  <PERSON> <<EMAIL>>
pkgname=OpenBLAS
pkgver="v0.3.5"
pkgrel=0
pkgdesc="OpenBLAS is an optimized BLAS library based on GotoBLAS2 1.13 BSD version."
url="https://www.openblas.net"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD-3-Clause license")
depends=()
makedepends=("gcc")

# source="https://github.com/xianyi/$pkgname/archive/refs/tags/$pkgver.tar.gz"
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"

downloadpackage=true
autounpack=true
buildtools="make"

builddir=$pkgname-$pkgver
packagename=$builddir.zip

source envset.sh
openblastarget=

prepare() {
    cp -rf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        openblastarget="ARMV7"
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        openblastarget="ARMV8"
    else
        echo "${ARCH} not support"
        return -1
    fi
}

build() {
    cd $builddir-$ARCH-build
    make TARGET=${openblastarget} AR=${AR} RANLIB=echo ARM_SOFTFP_ABI=1 ONLY_CBLAS=1 HOSTCC=gcc NUM_THREADS=32 USE_THREAD=0 libs netlib shared VERBOSE=1 \
    > `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    make PREFIX=$LYCIUM_ROOT/usr/$pkgname/$ARCH install VERBOSE=1 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    unset openblastarget
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    else
        echo "${ARCH} not support"
        return -1
    fi
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir ${PWD}/$builddir-armeabi-v7a-build ${PWD}/$builddir-arm64-v8a-build  # ${PWD}/$packagename
}
