# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
pkgname=geos
pkgver=3.11.2
pkgrel=0
pkgdesc="GEOS is a C++ library for performing operations on two-dimensional vector geometries. It is primarily a port of the JTS Topology Suite Java library. It provides many of the algorithms used by PostGIS, the Shapely package for Python, the sf package for R, and others."
url="https://github.com/libgeos/geos"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL v2.1")
depends=()
makedepends=()
install=
source="https://github.com/libgeos/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build      
}

build() {
    cd $builddir
    if [ $ARCH == "armeabi-v7a" ]
    then
        ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DBUILD_SHARED_LIBS=OFF -DCMAKE_CXX_FLAGS="-D__ARM_PCS_VFP=1" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DBUILD_SHARED_LIBS=OFF -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    fi
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret    
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 在OpenHarmony开发板中执行用例
    # ctest    
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
