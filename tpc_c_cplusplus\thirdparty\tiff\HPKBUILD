# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, cheng<PERSON> <<EMAIL>>, 城meto <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=tiff
pkgver=v4.5.0
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license="LibTIFF license"
depends=("zstd" "libjpeg-turbo" "xz" "libwebp" "libdeflate" "jbigkit") # TODO LERC 循环依赖：libwebp 与 tiff 互相依赖, 这里为了不阻塞自动化构建，采用 libwebp 不依赖 tiff，tiff 依赖 libwebp
makedepends=()

# 原仓地址: http://download.osgeo.org/libtiff/$pkgname-$pkgver.tar.xz, 因网络原因使用镜像地址
source="https://gitee.com/mirrors/lib$pkgname/repository/archive/$pkgver.zip"

autounpack=true
downloadpackage=true

builddir=lib$pkgname-$pkgver
packagename=$builddir.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make VERBOSE=1 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd "$builddir"
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    cd $builddir/$ARCH-build
    # 强制使用单板cmake程序进行测试
    find . -name "CTestTestfile.cmake" | xargs sed -i 's#".*/cmake"#"/usr/bin/cmake"#g'
    cd $OLDPWD

    echo "The test must be on an OpenHarmony device!"
    # 将测试用例使用的cmake改为CI工具的cmake
    sed -i "s/\".*\/cmake\"/\"cmake\"/g" $builddir/$ARCH-build/test/CTestTestfile.cmake
    # 设置LD_LIBRARY_PATH环境变量
    # ctest 测试
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
