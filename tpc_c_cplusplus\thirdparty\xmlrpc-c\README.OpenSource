[{"Name": "xmlrpc-c", "License": " BSD license", "License File": "COPYING", "Version Number": "1.54.06", "Owner": "hanjin<PERSON><EMAIL>", "Upstream URL": "https://sourceforge.net/projects/xmlrpc-c/files/Xmlrpc-c%20Super%20Stable/1.54.06/xmlrpc-c-1.54.06.tgz", "Description": "XML-RPC is a quick-and-easy way to make procedure calls over the Internet. It converts the procedure call into an XML document, sends it to a remote server using HTTP, and gets back the response as XML."}]