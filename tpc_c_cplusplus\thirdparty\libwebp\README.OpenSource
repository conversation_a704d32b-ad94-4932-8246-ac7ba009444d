[{"Name": "libwebp", "License": "BSD-3-Clause license", "License File": "https://github.com/webmproject/libwebp/blob/main/COPYING", "Version Number": "v1.3.1", "Owner": "<EMAIL>", "Upstream URL": "https://github.com/webmproject/libwebp/archive/refs/tags/v1.3.1.tar.gz", "Description": "WebP codec is a library to encode and decode images in WebP format. This package contains the library that can be used in other programs to add WebP support, as well as the command line tools 'cwebp' and 'dwebp' to compress and decompress images respectively."}]