#!/bin/bash

# VP8 支持检查脚本
# 用于验证编译好的 FFmpeg 库是否支持 VP8

echo "=== VP8 支持检查脚本 ==="

FFMPEG_DIR="ijkplayer/src/main/cpp/third_party/ffmpeg"

if [ ! -d "$FFMPEG_DIR" ]; then
    echo "❌ 错误：FFmpeg 目录不存在: $FFMPEG_DIR"
    echo "请确保您在正确的项目根目录下运行此脚本"
    exit 1
fi

echo "✅ 找到 FFmpeg 目录: $FFMPEG_DIR"

# 检查各个架构
for arch in armeabi-v7a arm64-v8a x86_64; do
    echo ""
    echo "=== 检查架构: $arch ==="
    
    LIBAVCODEC="$FFMPEG_DIR/$arch/lib/libavcodec.a"
    
    if [ -f "$LIBAVCODEC" ]; then
        echo "✅ 找到 libavcodec.a: $LIBAVCODEC"
        
        # 检查文件大小
        SIZE=$(ls -lh "$LIBAVCODEC" | awk '{print $5}')
        echo "📦 文件大小: $SIZE"
        
        # 检查 VP8 符号
        echo "🔍 检查 VP8 相关符号..."
        VP8_SYMBOLS=$(strings "$LIBAVCODEC" | grep -i vp8 | head -10)
        
        if [ -n "$VP8_SYMBOLS" ]; then
            echo "✅ 发现 VP8 相关符号:"
            echo "$VP8_SYMBOLS"
        else
            echo "❌ 未发现 VP8 相关符号"
        fi
        
        # 检查解码器符号
        echo "🔍 检查解码器符号..."
        DECODER_SYMBOLS=$(strings "$LIBAVCODEC" | grep -i "vp8.*decode" | head -5)
        
        if [ -n "$DECODER_SYMBOLS" ]; then
            echo "✅ 发现 VP8 解码器符号:"
            echo "$DECODER_SYMBOLS"
        else
            echo "❌ 未发现 VP8 解码器符号"
        fi
        
    else
        echo "❌ 未找到 libavcodec.a: $LIBAVCODEC"
    fi
done

# 检查头文件
echo ""
echo "=== 检查头文件 ==="
HEADER_DIR="$FFMPEG_DIR/arm64-v8a/include"

if [ -d "$HEADER_DIR" ]; then
    echo "✅ 找到头文件目录: $HEADER_DIR"
    
    # 检查 VP8 相关定义
    VP8_HEADERS=$(grep -r "AV_CODEC_ID_VP8\|VP8" "$HEADER_DIR" 2>/dev/null | head -5)
    
    if [ -n "$VP8_HEADERS" ]; then
        echo "✅ 发现 VP8 相关头文件定义:"
        echo "$VP8_HEADERS"
    else
        echo "❌ 未发现 VP8 相关头文件定义"
    fi
else
    echo "❌ 未找到头文件目录: $HEADER_DIR"
fi

echo ""
echo "=== 检查完成 ==="
echo ""
echo "💡 如果看到 VP8 相关符号，说明 VP8 支持已编译进库中"
echo "💡 您可以在应用中尝试播放 VP8 格式的视频来进一步验证"
