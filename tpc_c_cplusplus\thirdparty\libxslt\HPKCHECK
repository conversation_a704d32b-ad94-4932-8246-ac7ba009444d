# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
hassed=false
checkprepare(){
    export LIBXSLT_PLUGINS_PATH=`pwd`/$builddir-$ARCH-build/tests/.libs

    # 检查/usr/bin目录下是否存在sed软连接
    if [ ! -f "/usr/bin/sed" ]; then
        # 创建软连接
        ln -s /usr/bin/busybox /usr/bin/sed
    else
        hassed = true
    fi        
}
openharmonycheck() {
    res=0
    cd $builddir-$ARCH-build/tests
    /usr/bin/make check_test >> ${logfile} 2>&1
    res=$?
    
    unset LIBXSLT_PLUGINS_PATH
    cd $OLDPWD

    if [ $hassed == false ];then
        rm -rf /usr/bin/sed
    fi    
    
    return $res
}
