# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>    modify : lpzhong <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=openjpeg
pkgver=v2.5.0
pkgrel=0
pkgdesc="OpenJPEG is an open-source JPEG 2000 codec written in C language."
url="https://github.com/uclouvain/openjpeg"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD")
depends=("libpng" "lcms2" "tiff" "zlib")
makedepends=()

# 官方下载地址https://github.com/uclouvain/$pkgname/archive/refs/tags/$pkgver.tar.gz受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"
test_source="https://gitee.com/lycium_pkg_mirror/openjpeg-data/repository/archive/master.zip"

autounpack=true
downloadpackage=true

builddir=$pkgname-${pkgver}
packagename=$builddir.zip
test_data=openjpeg-data
test_ver=master

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    # -DCMAKE_C_FLAGS="-ffp-contract=off" 禁用 floating-point expression contraction.否则会因为浮点精度问题造成测试用例失败。
    PKG_CONFIG_LIBDIR="" ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DBUILD_UNIT_TESTS=ON -DBUILD_TESTING=ON \
    -DOPJ_DATA_ROOT=${PKGBUILD_ROOT}/openjpeg-data -DCMAKE_C_FLAGS="-ffp-contract=off" -B$ARCH-build -S./ \
    > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    # 下载测试数据
    if [ ! -f "$test_data.zip" ]
    then
        curl -f -L $test_source --output $test_data.zip
        if [ $? -ne 0 ]
        then
            rm -f $test_data.zip
            echo "download openjpeg data failed!"
            return 1
        fi
        unzip -q $test_data.zip
        mv ${test_data}-${test_ver} ${test_data}
    fi

    # 删除测试中cmake的路径
    cd $builddir/$ARCH-build
    grep -rn "\".*\/cmake\"" | while read line;
    do
    {
        name=${line%%:*}
        sed -i "s/\".*\/cmake\"/\"cmake\"/g" $name
    }
    done
    cd $OLDPWD

    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # TODO
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
