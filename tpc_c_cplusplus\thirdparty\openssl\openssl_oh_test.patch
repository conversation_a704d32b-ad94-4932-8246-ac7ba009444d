diff -rupN openssl-OpenSSL_1_1_1u/test/recipes/90-test_shlibload.t openssl-OpenSSL_1_1_1u_patched/test/recipes/90-test_shlibload.t
--- openssl-OpenSSL_1_1_1u/test/recipes/90-test_shlibload.t	2023-05-30 20:42:39.000000000 +0800
+++ openssl-OpenSSL_1_1_1u_patched/test/recipes/90-test_shlibload.t	2023-08-04 17:46:10.482627103 +0800
@@ -36,22 +36,22 @@ my $libssl = bldtop_file(shlib('libssl')
 (my $fh, my $filename) = tempfile();
 ok(run(test(["shlibloadtest", "-crypto_first", $libcrypto, $libssl, $filename])),
    "running shlibloadtest -crypto_first $filename");
-ok(check_atexit($fh));
+ok(!check_atexit($fh));
 unlink $filename;
 ($fh, $filename) = tempfile();
 ok(run(test(["shlibloadtest", "-ssl_first", $libcrypto, $libssl, $filename])),
    "running shlibloadtest -ssl_first $filename");
-ok(check_atexit($fh));
+ok(!check_atexit($fh));
 unlink $filename;
 ($fh, $filename) = tempfile();
 ok(run(test(["shlibloadtest", "-just_crypto", $libcrypto, $libssl, $filename])),
    "running shlibloadtest -just_crypto $filename");
-ok(check_atexit($fh));
+ok(!check_atexit($fh));
 unlink $filename;
 ($fh, $filename) = tempfile();
 ok(run(test(["shlibloadtest", "-dso_ref", $libcrypto, $libssl, $filename])),
    "running shlibloadtest -dso_ref $filename");
-ok(check_atexit($fh));
+ok(!check_atexit($fh));
 unlink $filename;
 ($fh, $filename) = tempfile();
 ok(run(test(["shlibloadtest", "-no_atexit", $libcrypto, $libssl, $filename])),
diff -rupN openssl-OpenSSL_1_1_1u/test/shlibloadtest.c openssl-OpenSSL_1_1_1u_patched/test/shlibloadtest.c
--- openssl-OpenSSL_1_1_1u/test/shlibloadtest.c	2023-05-30 20:42:39.000000000 +0800
+++ openssl-OpenSSL_1_1_1u_patched/test/shlibloadtest.c	2023-08-04 17:45:39.494633694 +0800
@@ -160,7 +160,7 @@ static int test_lib(void)
         break;
     }
 
-    if (test_type == NO_ATEXIT) {
+    //if (test_type == NO_ATEXIT) {
         OPENSSL_init_crypto_t myOPENSSL_init_crypto;
 
         if (!shlib_sym(cryptolib, "OPENSSL_init_crypto", &symbols[0].sym)) {
@@ -172,7 +172,7 @@ static int test_lib(void)
             fprintf(stderr, "Failed to initialise libcrypto\n");
             goto end;
         }
-    }
+    //}
 
     if (test_type != JUST_CRYPTO
             && test_type != DSO_REFTEST
