diff -rupN openfst-1.8.2/configure openfst-1.8.2_patched/configure
--- openfst-1.8.2/configure	2022-02-04 01:27:24.000000000 +0800
+++ openfst-1.8.2_patched/configure	2023-07-19 14:28:27.539390345 +0800
@@ -17978,10 +17978,10 @@ libfstdir=$with_libfstdir
 # or when someone does not run `make check`.
 if test "$cross_compiling" = yes
 then :
-  { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
-printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
-as_fn_error $? "cannot run test program while cross compiling
-See \`config.log' for more details" "$LINENO" 5; }
+# { { printf "%s\n" "$as_me:${as_lineno-$LINENO}: error: in \`$ac_pwd':" >&5
+#printf "%s\n" "$as_me: error: in \`$ac_pwd':" >&2;}
+#as_fn_error $? "cannot run test program while cross compiling
+#See \`config.log' for more details" "$LINENO" 5; }
 else $as_nop
   cat confdefs.h - <<_ACEOF >conftest.$ac_ext
 /* end confdefs.h.  */
diff -rupN openfst-1.8.2/src/test/Makefile.am openfst-1.8.2_patched/src/test/Makefile.am
--- openfst-1.8.2/src/test/Makefile.am	2022-02-04 00:48:53.000000000 +0800
+++ openfst-1.8.2_patched/src/test/Makefile.am	2023-07-19 14:28:53.807874051 +0800
@@ -1,7 +1,9 @@
 AM_CPPFLAGS = -I$(srcdir)/../include $(ICU_CPPFLAGS)
 LDADD = ../lib/libfst.la -lm $(DL_LIBS)
 
-check_PROGRAMS = fst_test weight_test
+bin_PROGRAMS = fst_test 
+
+bin_PROGRAMS += weight_test
 
 fst_test_SOURCES = fst_test.cc
 
@@ -9,24 +11,24 @@ weight_test_SOURCES = weight_test.cc
 
 algo_test_SOURCES = algo_test.cc
 
-check_PROGRAMS += algo_test_log
+bin_PROGRAMS += algo_test_log
 algo_test_log_SOURCES = $(algo_test_SOURCES)
 algo_test_log_CPPFLAGS = -DTEST_LOG $(AM_CPPFLAGS)
 
-check_PROGRAMS += algo_test_tropical
+bin_PROGRAMS += algo_test_tropical
 algo_test_tropical_SOURCES = $(algo_test_SOURCES)
 algo_test_tropical_CPPFLAGS = -DTEST_TROPICAL $(AM_CPPFLAGS)
 
-check_PROGRAMS += algo_test_minmax
+bin_PROGRAMS += algo_test_minmax
 algo_test_minmax_SOURCES = $(algo_test_SOURCES)
 algo_test_minmax_CPPFLAGS = -DTEST_MINMAX $(AM_CPPFLAGS)
 
-check_PROGRAMS += algo_test_lexicographic
+bin_PROGRAMS += algo_test_lexicographic
 algo_test_lexicographic_SOURCES = $(algo_test_SOURCES)
 algo_test_lexicographic_CPPFLAGS = -DTEST_LEXICOGRAPHIC $(AM_CPPFLAGS)
 
-check_PROGRAMS += algo_test_power
+bin_PROGRAMS += algo_test_power
 algo_test_power_SOURCES = $(algo_test_SOURCES)
 algo_test_power_CPPFLAGS = -DTEST_POWER $(AM_CPPFLAGS)
 
-TESTS = $(check_PROGRAMS)
+TESTS = $(bin_PROGRAMS)
