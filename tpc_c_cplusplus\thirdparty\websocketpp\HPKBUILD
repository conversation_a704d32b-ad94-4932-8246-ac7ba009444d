# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <hanjin<PERSON><EMAIL>>
pkgname=websocketpp
pkgver=0.8.2
pkgrel=0
pkgdesc="C++ websocket client/server library"
url="https://github.com/zaphoyd/websocketpp"
archs=("armeabi-v7a" "arm64-v8a")
license=("<PERSON>. All rights reserved")
depends=("openssl" "boost")
makedepends=()

source="https://github.com/zaphoyd/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
patchflag=true

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

prepare() {
    if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../websocketpp_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DBoost_NO_BOOST_CMAKE=ON -DBUILD_EXAMPLES=ON -DBUILD_TESTS=ON -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd "$builddir"
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
