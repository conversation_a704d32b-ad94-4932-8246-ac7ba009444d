# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, wen fan <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname="capnproto"
pkgver="v1.0.2"
pkgrel="0"
pkgdesc="Cap'n Proto is an insanely fast data interchange format and capability-based RPC system"
url="https://github.com/${pkgname}/${pkgname}"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT License")
depends=("zlib")
makedepends=()

source="https://github.com/${pkgname}/${pkgname}/archive/refs/tags/${pkgver}.tar.gz"

autounpack=true
downloadpackage=true
buildtools="cmake"
cmaketestfalg=true

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build    
    # 先cmake一份linux环境下的
    if ${maketestfalg}
    then
        mkdir -p $builddir/build-test
        cd $builddir/build-test
        cmake .. > $buildlog 2>&1
        ${MAKE} >> $buildlog 2>&1
        cd $OLDPWD
        cmaketestfalg=false
    fi
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DEXTERNAL_CAPNP=ON -DCAPNPC_CXX_EXECUTABLE="../../../../build-test/c++/src/capnp/capnpc-c++" -DCAPNP_EXECUTABLE="../../../../build-test/c++/src/capnp/capnp" -B$ARCH-build -S./ -L > $buildlog 2>&1
    ${MAKE} -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    ${MAKE} -C $ARCH-build install >> $buildlog 2>&1
    ret=$?
    cap_dir=${LYCIUM_ROOT}/usr/${pkgname}/${ARCH}/lib
    zlib_dir=${LYCIUM_ROOT}/usr/${depends}/${ARCH}/lib
    cp -f ${zlib_dir}/lib* ${cap_dir}/
    cd $OLDPWD
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

cleanbuild(){
    rm -rf ${PWD}/$builddir
}
