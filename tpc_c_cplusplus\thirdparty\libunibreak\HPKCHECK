#Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd builddir-$ARCH-build/breaktext/DebugDir
    export LD_LIBRARY_PATH=$LYCIUM_THIRDPARTY_ROOT/$pkgname/builddir-$ARCH-build/$builddir/src/.libs
    # 该测试用例需要手动创建一个txt文本文件，内容需要以换行结束
    echo -e "\n1234567890-=qwertyuiop[]8asdfghjkl;'zxcvbnm,.88@#$%^&*()_+QWERTYUIOP{}}8ASDFGHJKL:ZXCVBNM<>8\n" > input.txt
    ./breaktext input.txt output.txt
    res=$?
    if [ $res != 0 ]
    then
        return res
    fi
    cp input.txt $LYCIUM_ROOT/check_result/manual_confirm/
    cp output.txt $LYCIUM_ROOT/check_result/manual_confirm/
    cd $OLDPWD
}