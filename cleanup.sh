#!/bin/bash

# OpenHarmony ijkplayer 清理脚本
# 用于清除编译过程中生成的所有文件，还原到原始状态

echo "========================================"
echo "OpenHarmony ijkplayer 清理工具"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 获取项目根目录
ROOT_DIR=$(pwd)

echo "当前目录: $ROOT_DIR"
echo ""

# 确认是否在项目根目录
if [ ! -f "prebuild.sh" ] || [ ! -f "README.md" ] || [ ! -d "ijkplayer" ]; then
    echo -e "${RED}错误: 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 显示将要清理的内容
echo "将要清理以下内容："
echo "----------------------------------------"

# 检查并列出要清理的目录和文件
items_to_clean=()

if [ -d "temp_downloads" ]; then
    echo "📁 temp_downloads/ (临时下载目录)"
    items_to_clean+=("temp_downloads")
fi

if [ -d "tpc_c_cplusplus" ]; then
    echo "📁 tpc_c_cplusplus/ (lycium工具链)"
    items_to_clean+=("tpc_c_cplusplus")
fi

if [ -d "lycium" ]; then
    echo "📁 lycium/ (lycium工具)"
    items_to_clean+=("lycium")
fi

# 检查third_party目录中的依赖
third_party_dir="ijkplayer/src/main/cpp/third_party"
if [ -d "$third_party_dir" ]; then
    echo "📁 $third_party_dir/ 中的编译结果:"
    
    # 检查ffmpeg目录（保留config.h）
    if [ -d "$third_party_dir/ffmpeg" ]; then
        file_count=$(find "$third_party_dir/ffmpeg" -type f ! -name "config.h" | wc -l)
        if [ $file_count -gt 0 ]; then
            echo "   - ffmpeg/ (保留config.h，清理其他 $file_count 个文件)"
            items_to_clean+=("ffmpeg_content")
        fi
    fi
    
    # 检查其他依赖目录
    for dep in soundtouch yuv openssl; do
        if [ -d "$third_party_dir/$dep" ]; then
            file_count=$(find "$third_party_dir/$dep" -type f | wc -l)
            echo "   - $dep/ ($file_count 个文件)"
            items_to_clean+=("$dep")
        fi
    done
fi

# 检查临时文件
temp_files=$(find . -maxdepth 1 -name "*.tmp" -o -name "*.log" -o -name "*~" 2>/dev/null)
if [ -n "$temp_files" ]; then
    echo "📄 临时文件:"
    echo "$temp_files" | sed 's/^/   - /'
    items_to_clean+=("temp_files")
fi

echo ""

# 如果没有要清理的内容
if [ ${#items_to_clean[@]} -eq 0 ]; then
    echo -e "${GREEN}✓ 项目已经是干净状态，无需清理${NC}"
    exit 0
fi

# 确认清理操作
echo -e "${YELLOW}警告: 此操作将删除上述所有文件和目录${NC}"
echo -e "${YELLOW}这个操作不可逆转！${NC}"
echo ""
read -p "确定要继续清理吗？(y/N): " confirm

if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "清理操作已取消"
    exit 0
fi

echo ""
echo "开始清理..."
echo "========================================"

# 执行清理操作
cleanup_success=0
cleanup_total=0

# 清理函数
cleanup_item() {
    local item="$1"
    local description="$2"
    cleanup_total=$((cleanup_total + 1))
    
    echo -n "清理 $description... "
    
    case "$item" in
        "temp_downloads")
            if rm -rf temp_downloads/; then
                echo -e "${GREEN}完成${NC}"
                cleanup_success=$((cleanup_success + 1))
            else
                echo -e "${RED}失败${NC}"
            fi
            ;;
        "tpc_c_cplusplus")
            if rm -rf tpc_c_cplusplus/; then
                echo -e "${GREEN}完成${NC}"
                cleanup_success=$((cleanup_success + 1))
            else
                echo -e "${RED}失败${NC}"
            fi
            ;;
        "lycium")
            if rm -rf lycium/; then
                echo -e "${GREEN}完成${NC}"
                cleanup_success=$((cleanup_success + 1))
            else
                echo -e "${RED}失败${NC}"
            fi
            ;;
        "ffmpeg_content")
            # 保留config.h，删除其他文件
            cd "$third_party_dir/ffmpeg"
            if find . -type f ! -name "config.h" -delete && find . -type d -empty -delete; then
                cd "$ROOT_DIR"
                echo -e "${GREEN}完成${NC}"
                cleanup_success=$((cleanup_success + 1))
            else
                cd "$ROOT_DIR"
                echo -e "${RED}失败${NC}"
            fi
            ;;
        "soundtouch"|"yuv"|"openssl")
            if rm -rf "$third_party_dir/$item/"; then
                echo -e "${GREEN}完成${NC}"
                cleanup_success=$((cleanup_success + 1))
            else
                echo -e "${RED}失败${NC}"
            fi
            ;;
        "temp_files")
            if find . -maxdepth 1 \( -name "*.tmp" -o -name "*.log" -o -name "*~" \) -delete; then
                echo -e "${GREEN}完成${NC}"
                cleanup_success=$((cleanup_success + 1))
            else
                echo -e "${RED}失败${NC}"
            fi
            ;;
    esac
}

# 执行所有清理操作
for item in "${items_to_clean[@]}"; do
    case "$item" in
        "temp_downloads") cleanup_item "$item" "临时下载目录" ;;
        "tpc_c_cplusplus") cleanup_item "$item" "lycium工具链" ;;
        "lycium") cleanup_item "$item" "lycium工具" ;;
        "ffmpeg_content") cleanup_item "$item" "ffmpeg编译结果" ;;
        "soundtouch") cleanup_item "$item" "soundtouch依赖" ;;
        "yuv") cleanup_item "$item" "yuv依赖" ;;
        "openssl") cleanup_item "$item" "openssl依赖" ;;
        "temp_files") cleanup_item "$item" "临时文件" ;;
    esac
done

echo ""
echo "========================================"
echo "清理完成！"
echo "========================================"
echo -e "总计: $cleanup_total 项"
echo -e "${GREEN}成功: $cleanup_success 项${NC}"
echo -e "${RED}失败: $((cleanup_total - cleanup_success)) 项${NC}"

if [ $cleanup_success -eq $cleanup_total ]; then
    echo ""
    echo -e "${GREEN}✓ 项目已成功还原到原始状态${NC}"
    echo ""
    echo "现在您可以："
    echo "1. 重新运行编译脚本: ./build_msys2.sh"
    echo "2. 或者进行其他操作"
else
    echo ""
    echo -e "${YELLOW}⚠ 部分清理操作失败，请检查权限或手动删除${NC}"
fi

echo ""
