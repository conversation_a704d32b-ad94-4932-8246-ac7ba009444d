# rapidjson集成到应用hap

本库是在RK3568开发板上基于OpenHarmony3.2 Release版本的镜像验证的，如果是从未使用过RK3568，可以先查看[润和RK3568开发板标准系统快速上手](https://gitee.com/openharmony-sig/knowledge_demo_temp/tree/master/docs/rk3568_helloworld)。

## 开发环境

- [开发环境准备](../../../docs/hap_integrate_environment.md)

## 编译三方库

- 下载本仓库

  ```shell
  git clone https://gitee.com/openharmony-sig/tpc_c_cplusplus.git --depth=1
  ```

- 三方库目录结构

  ```shell
  tpc_c_cplusplus/thirdparty/rapidjson #三方库rapidjson的目录结构如下
  ├── docs                             #三方库相关文档的文件夹
  ├── HPKBUILD                         #构建脚本
  ├── HPKCHECK                         #测试脚本
  ├── README.OpenSource                #说明三方库源码的下载地址，版本、license等信息
  ├── README_zh.md  
  ```
  
- 在lycium目录下编译三方库

  编译环境的搭建参考[准备三方库构建环境](../../../lycium/README.md#1编译环境准备)

  ```shell
  cd lycium
  ./build.sh rapidjson
  ```

- 三方库头文件及生成的库

  在lycium目录下会生成usr目录，该目录下存在已编译完成的32位和64位三方库

  ```shell
  rapidjson/arm64-v8a   rapidjson/armeabi-v7a
  ```
  
- [测试三方库](#测试三方库)

## 应用中使用三方库

- 在IDE的cpp目录下新增thirdparty目录，将编译生成的arm64-v8a和armeabi-v7a库拷贝到该目录下， 如下图所示,由于rapidjson是头文件库，所以并不会生成.a和.so文件，特此说明  

  ![3](.\pic\3.PNG)

  

- 在最外层（cpp目录下）CMakeLists.txt中添加如下语句

  ![image-20231228202528676](..\docs\pic\1.png)

  ```cmake
  #将三方库的头文件加入工程中      
  target_include_directories(entry PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/rapidjson/${OHOS_ARCH}/include)  
  ```


## 测试三方库

三方库的测试使用原库自带的测试用例来做测试，[准备三方库测试环境](../../../lycium/README.md#3ci环境准备)

将准备好的文件推送到开发板，在windows命令行进行如下操作

```shell
hdc file send /home/<USER>/tool/tpc_c_cplusplus-master/thirdparty/rapidjson.tar /home/<USER>/tool/tpc_c_cplusplus-master/thirdparty                 #将测试文件推入开发板thirdparty目录 
tar -xvf rapidjson.tar            #解压测试文件
export LD_LIBRARY_PATH="/data/archermind/SDK"/  #设置环境变量
```

进入到构建目录,执行如下命令

```
ctest -v
```

（arm64-v8a-build为构建64位的目录，armeabi-v7a-build为构建32位的目录）

下图为ubuntu测试结果

![](..\docs\pic\2.png)

开发板上测试，由于valgrind_unittest测试需要借助valgrind工具，其余测试用例结果与ubuntu上测试一致

![](..\docs\pic\4.PNG)



## 参考资料

- [润和RK3568开发板标准系统快速上手](https://gitee.com/openharmony-sig/knowledge_demo_temp/tree/master/docs/rk3568_helloworld)
- [OpenHarmony三方库地址](https://gitee.com/openharmony-tpc)
- [OpenHarmony知识体系](https://gitee.com/openharmony-sig/knowledge)
- [通过DevEco Studio开发一个NAPI工程](https://gitee.com/openharmony-sig/knowledge_demo_temp/blob/master/docs/napi_study/docs/hello_napi.md)
