# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    cd $builddir/${ARCH}-build/test
    # 测试项1
    ./test_connections ${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/$builddir/test/test-suite/images/sample.jpg tmp.png > ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        echo "test_connections test failed" >> ${logfile} 2>&1
        res = -1
    else
        echo "test_connections test pass" >> ${logfile} 2>&1
    fi

    # 测试项2
    ./test_descriptors ${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/$builddir/test/test-suite/images/sample.jpg >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        echo "test_descriptors test failed" >> ${logfile} 2>&1
        res = -1
    else
        echo "test_descriptors test pass" >> ${logfile} 2>&1
    fi

    # 测试项3
    ./test_timeout_webpsave >> ${logfile} 2>&1
    if [ $? -ne 0 ]
    then
        echo "test_timeout_webpsave test failed" >> ${logfile} 2>&1
        res = -1
    else
        echo "test_timeout_webpsave test pass" >> ${logfile} 2>&1
    fi

    cd $OLDPWD
    return $res
}