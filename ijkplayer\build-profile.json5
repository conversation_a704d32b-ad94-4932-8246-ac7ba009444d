{
  "apiType": "stageMode",
  "buildOption": {
    "napiLibFilterOption": {
      "enableOverride": true
    },
    "externalNativeOptions": {
      "path": "./src/main/cpp/CMakeLists.txt",
      "arguments": "-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'",
      "abiFilters": [
        "arm64-v8a",
        "x86_64"
      ],
      "cppFlags": ""
    },
  }
}
