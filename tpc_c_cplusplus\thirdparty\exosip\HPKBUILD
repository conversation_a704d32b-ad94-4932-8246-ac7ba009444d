# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
pkgname=exosip
pkgver=5.3.0
pkgrel=0
pkgdesc="eXosip is a library that hides the complexity of using the SIP protocol for mutlimedia session establishement"
url="http://savannah.nongnu.org/projects/exosip"
archs=("armeabi-v7a" "arm64-v8a")
license=("GPL-2.0-or-later")
depends=("openssl" "libosip2" "c-ares")
makedepends=()

source="https://git.savannah.nongnu.org/cgit/exosip.git/snapshot/$pkgname-$pkgver.tar.gz"
autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

source envset.sh
host=
prepare() {
    cp -arf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]; then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ]; then
        setarm64ENV        
        host=aarch64-linux
    else
        echo "$ARCH not support!"
        return -1
    fi 

    cd $builddir-$ARCH-build
    ./autogen.sh > `pwd`/build.log 2>&1
    cd $OLDPWD    
}

build() {
    cd $builddir-$ARCH-build    
    PKG_CONFIG_LIBDIR="${pkgconfigpath}" ./configure "$@" --enable-static --host=$host >> `pwd`/build.log 2>&1
    make -j4 VERBOSE=1 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
     
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    unset host  

    if [ $ARCH == "armeabi-v7a" ]; then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]; then
        unsetarm64ENV
    else
        echo "$ARCH not support!"
    fi
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 需要通过./sip_monitor 连接服务器测试
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir  ${PWD}/$builddir-${archs[0]}-build ${PWD}/$builddir-${archs[1]}-build #${PWD}/$packagename 
}
