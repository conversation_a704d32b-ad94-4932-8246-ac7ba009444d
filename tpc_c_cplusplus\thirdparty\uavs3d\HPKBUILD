# Contributor: ding<PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=uavs3d
pkgver=1fd04917cff50fac72ae23e45f82ca6fd9130bd8
pkgrel=0
pkgdesc="AVS3 decoder which supports AVS3-P2 baseline profile."
url="https://github.com/uavs3/uavs3d"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD-3-Clause license")
depends=()
makedepends=("wget")

source="https://github.com/uavs3/$pkgname.git"

autounpack=false
downloadpackage=false
buildtools="cmake"

builddir=$pkgname-${pkgver}
host=
cloneflag=true
patchflag=true
gettestresourceflag=true

prepare() {
    if [ $cloneflag == true ]
    then
        mkdir $builddir
        # 发布的v1.1版本中没有支持arm架构,支持arm架构版本在发布之后,在master分支上
        git clone -b master $source $builddir
        if [ $? != 0 ]
        then
            return -1
        fi
        cd $builddir
        git reset --hard $pkgver
        if [ $? != 0 ]
        then
            return -2
        fi
        cd $OLDPWD
        cloneflag=false
    fi
    if $patchflag
    then
        cd $builddir
        # 由于三方库中cmake存在bug，以及汇编指令问题导致openharmony不支持，所以打patch
        patch -p1 < `pwd`/../uavs3d_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    if [ $gettestresourceflag == true ]
    then
        cd $builddir
        # 测试资源下载
        wget https://raw.githubusercontent.com/uavs3/avs3stream/master/ES/City_1280x720_60_ra/test.avs3 --no-check-certificate
        if [ $? != 0 ]
        then
            return -3
        fi
        gettestresourceflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DCOMPILE_10BIT=0 -DBUILD_SHARED_LIBS=1 \
        -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make VERBOSE=1 -j4 -C $ARCH-build LOSCFG_COMPILER_ARM_NONE_EABI=y >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 测试方法
    # ./uavs3dec -i test.avs3 -o output.yuv -t 8 -l 2 -s 1
}

cleanbuild(){
   rm -rf ${PWD}/$builddir
}

