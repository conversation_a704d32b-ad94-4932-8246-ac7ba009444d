#!/bin/bash

# 手动下载设置脚本

echo "========================================"
echo "手动下载设置向导"
echo "========================================"

# 确认当前目录
current_dir=$(pwd)
echo "当前目录: $current_dir"

# 检查是否在项目根目录
if [ ! -f "prebuild.sh" ] || [ ! -f "README.md" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    echo "项目根目录应该包含 prebuild.sh 和 README.md 文件"
    exit 1
fi

# 创建temp_downloads目录
echo "创建 temp_downloads 目录..."
mkdir -p temp_downloads

echo ""
echo "========================================"
echo "下载目录信息"
echo "========================================"
echo "Windows路径: $(cygpath -w "$current_dir/temp_downloads" 2>/dev/null || echo "$current_dir/temp_downloads")"
echo "MSYS2路径: $current_dir/temp_downloads"
echo ""

echo "========================================"
echo "需要下载的文件"
echo "========================================"
echo "请在浏览器中下载以下文件到上述目录："
echo ""
echo "1. FFmpeg源码:"
echo "   https://github.com/bilibili/FFmpeg/archive/refs/tags/ff4.0--ijk0.8.8--20210426--001.tar.gz"
echo "   保存为: ff4.0--ijk0.8.8--20210426--001.tar.gz"
echo ""
echo "2. soundtouch源码 (ijk-r0.1.2-dev分支):"
echo "   https://github.com/bilibili/soundtouch/archive/refs/heads/ijk-r0.1.2-dev.zip"
echo "   保存为: ijk-r0.1.2-dev.zip"
echo "   注意: 确保下载的是ijk-r0.1.2-dev分支版本"
echo ""
echo "3. libyuv源码:"
echo "   https://github.com/bilibili/libyuv/archive/refs/heads/ijk-r0.2.1-dev.zip"
echo "   保存为: ijk-r0.2.1-dev.zip"
echo ""
echo "4. OpenSSL源码:"
echo "   https://github.com/openssl/openssl/archive/refs/tags/OpenSSL_1_1_1w.zip"
echo "   保存为: OpenSSL_1_1_1w.zip"
echo ""

echo "========================================"
echo "下载完成后的操作"
echo "========================================"
echo "下载完成后，请运行以下命令解压文件："
echo ""
echo "cd temp_downloads"
echo ""
echo "# 解压FFmpeg"
echo "tar -xzf ff4.0--ijk0.8.8--20210426--001.tar.gz"
echo "mv FFmpeg-ff4.0--ijk0.8.8--20210426--001 ffmpeg_src"
echo ""
echo "# 解压soundtouch"
echo "unzip ijk-r0.1.2-dev.zip"
echo "mv soundtouch-ijk-r0.1.2-dev soundtouch_src"
echo ""
echo "# 解压libyuv"
echo "unzip ijk-r0.2.1-dev.zip"
echo "mv libyuv-ijk-r0.2.1-dev libyuv_src"
echo ""
echo "# 解压OpenSSL"
echo "unzip OpenSSL_1_1_1w.zip"
echo "mv openssl-OpenSSL_1_1_1w openssl_src"
echo ""
echo "cd .."
echo ""

echo "========================================"
echo "或者运行解压脚本"
echo "========================================"
echo "下载完成后，也可以直接运行："
echo "./extract_downloads.sh"
echo ""

# 创建解压脚本
cat > extract_downloads.sh << 'EOF'
#!/bin/bash

echo "解压下载的源码包..."

cd temp_downloads

# 检查文件是否存在并解压
if [ -f "ff4.0--ijk0.8.8--20210426--001.tar.gz" ]; then
    echo "解压FFmpeg..."
    tar -xzf ff4.0--ijk0.8.8--20210426--001.tar.gz
    mv FFmpeg-ff4.0--ijk0.8.8--20210426--001 ffmpeg_src
    echo "FFmpeg解压完成"
else
    echo "警告: FFmpeg压缩包不存在"
fi

if [ -f "ijk-r0.1.2-dev.zip" ]; then
    echo "解压soundtouch..."
    unzip -q ijk-r0.1.2-dev.zip
    mv soundtouch-ijk-r0.1.2-dev soundtouch_src
    echo "soundtouch解压完成"
else
    echo "警告: soundtouch压缩包不存在"
fi

if [ -f "ijk-r0.2.1-dev.zip" ]; then
    echo "解压libyuv..."
    unzip -q ijk-r0.2.1-dev.zip
    mv libyuv-ijk-r0.2.1-dev libyuv_src
    echo "libyuv解压完成"
else
    echo "警告: libyuv压缩包不存在"
fi

if [ -f "OpenSSL_1_1_1w.zip" ]; then
    echo "解压OpenSSL..."
    unzip -q OpenSSL_1_1_1w.zip
    mv openssl-OpenSSL_1_1_1w openssl_src
    echo "OpenSSL解压完成"
else
    echo "警告: OpenSSL压缩包不存在"
fi

cd ..

echo ""
echo "解压完成！现在可以运行编译脚本："
echo "./build_msys2.sh"
EOF

chmod +x extract_downloads.sh

echo "已创建解压脚本: extract_downloads.sh"
echo ""
echo "========================================"
echo "下一步"
echo "========================================"
echo "1. 在浏览器中下载上述4个文件到 temp_downloads 目录"
echo "2. 运行: ./extract_downloads.sh"
echo "3. 运行: ./build_msys2.sh"
echo ""
