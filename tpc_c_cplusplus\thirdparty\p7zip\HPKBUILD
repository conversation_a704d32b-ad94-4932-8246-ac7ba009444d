# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <hanjin<PERSON><EMAIL>>
pkgname=p7zip
pkgver=v17.05
pkgrel=0
pkgdesc="The original intention of p7zip is to make 7z run on unix-like systems. The latest 7zip already supports unix systems, so the new version of this project is not just a derivative of p7zip. "
url="https://github.com/p7zip-project/p7zip"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL")
depends=()
makedepends=()

source="https://github.com/p7zip-project/$pkgname/archive/refs/tags/$pkgver.tar.gz"

downloadpackage=true
autounpack=true
patchflag=true

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../p7zip_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S`pwd`/CPP/7zip/CMAKE/ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin/Codecs
    cp -rf `pwd`/$ARCH-build/bin/7z_ $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin
    cp -rf `pwd`/$ARCH-build/bin/7za $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin
    cp -rf `pwd`/$ARCH-build/bin/7zCon.sfx $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin
    cp -rf `pwd`/$ARCH-build/bin/7zr $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin
    cp -rf `pwd`/$ARCH-build/bin/7z.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin
    cp -rf `pwd`/$ARCH-build/bin/Codecs/Rar.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin/Codecs
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
