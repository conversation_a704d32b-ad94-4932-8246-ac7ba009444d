# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=libharu
pkgver=v2.4.4
pkgrel=0
pkgdesc="Haru is a free, cross platform, open-sourced software library for generating PDF."
url="https://github.com/libharu/libharu"
archs=("armeabi-v7a" "arm64-v8a")
license=("zlib License")
depends=("libpng" "zlib")
makedepends=()

source="https://github.com/$pkgname/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

patch_flag=true

prepare() {
    if [ "$patch_flag" == true ]
    then
        cd $builddir
        # 修改line_demo内存溢出问题
        patch -p1 <  ../libharu_ohos_test.patch > $publicbuildlog 2>&1
        cd $OLDPWD
        patch_flag=false
    fi

    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DLIBHPDF_SHARED=ON -DLIBHPDF_STATIC=ON -DLIBHPDF_EXAMPLES=ON -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd "$builddir"
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    # 复制测试所需文件
    cp -R $builddir/demo/images $builddir/${ARCH}-build/demo/
    cp -R $builddir/demo/mbtext $builddir/${ARCH}-build/demo/
    cp -R $builddir/demo/pngsuite $builddir/${ARCH}-build/demo/
    cp -R $builddir/demo/rawimage $builddir/${ARCH}-build/demo/
    cp -R $builddir/demo/ttfont $builddir/${ARCH}-build/demo/
    cp -R $builddir/demo/type1 $builddir/${ARCH}-build/demo/
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
