{"version": "1.0", "events": [{"head": {"id": "6c4409fe-14b1-41aa-a3cb-a6c30b6717d1", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25324549750500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f47289-f1cf-41af-b911-6073f119253f", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25324551929400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42d7329c-e579-4eaf-bc62-0e0e5ddb99c8", "name": "hvigor daemon: Socket will be closed. socketId=4flA5kHoJjfpJ-7uAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25324553276200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91b06d4a-7c1e-4dda-9d4f-57923ba42ffa", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":3716,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097752761,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25324554277400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf6e097c-b054-4149-a405-32d99410d7dd", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25324570254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2edd10f9-705d-4e64-a043-ee94a6442b22", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25324570459400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "693d0b95-3470-40f6-85d6-f0340e658299", "name": "hvigor daemon: Socket is connected. socketId=hQEq1aKtUX9CKAJ-AAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325292562300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "163d2bd5-b9c4-42c4-abde-0c8af530fad6", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"42809c4110d90f682938597528c2556b5103a105\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\onlyreceiver\\\\editVersion\\\\update11\\\\globalstateUse\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5140,\"state\":\"idle\",\"lastUsedTime\":1752805626314,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"0000005035465bbb6f3d5c2c33fe4747ba2a5898fffda2e8e55ad25942bc459b9580a8b060cccfa52a7bd36d99a0b7e4b75bddd3d7c99c1bde55cbdd6b43f8cbd601fcaff5420efe69552e27c20ca2364d8aa9c3b0c8eea4159258819fbe6195\"},{\"keyId\":\"277ad167b90cc80ce976dbd6afbe358bd99b7f7e\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":2148,\"state\":\"stopped\",\"lastUsedTime\":1753090153399,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050838d1ad01bfd1550ead51f6ed433a227255c04f73eb6d56f22bd2763fe725e2b83fbcf4fd60a26c845a2c602755158018cba8b725381b1441e800bf943ef6f387c5623ba06c8adf19ae16119669eddff1cd39b24422ab96c15d49f8f\"},{\"keyId\":\"893216d240f25a41562b9bcbc7a74ef1fd3fa8ed\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\release\\\\0718\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45003,\"pid\":18640,\"state\":\"stopped\",\"lastUsedTime\":1753085265437,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050cac0116da7771717d59e7bc8faf998fc155484d3cd0d66298ab3323e5f33a8c26ed7d084a225d981022c8ea0220538a0a54723b401e094f07324ce4a503482a754aac107da85885404f88621f172c9c61d553975ef4a47330cc4a6cb\"},{\"keyId\":\"b9ddb8cea31f249c8b2536ebf131cc68ae6be44d\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":17592,\"state\":\"stopped\",\"lastUsedTime\":1753084933680,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005070f328dae46fd03c7719b268053842e81e407fb9e7f97683a5c3f4144b92f225f5b56019abca2a185e5cb5b31eb96548737440c854b4d374ea335b821a28fa1b5c8f9980b8a71ae27ef25131c90ce745e53355b29e37100e8ec29f49\"},{\"pid\":3716,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097752774,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325293470400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f6bcb4b-b1e6-46f1-9f8b-ee2da3d0b4d6", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":3716,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097752774,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325294453000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a23259d6-b4da-4e82-bec4-d6184ae553d0", "name": "set active socket. socketId=hQEq1aKtUX9CKAJ-AAAD", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325298938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cd05741-c6a3-496f-88e1-adecea6e853e", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":3716,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097753513,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325299681500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98dde2b-c4b8-4ce2-84cb-e6eec694c066", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=ijkplayer', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325302482300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9638dace-dae8-49d0-b7b4-20f882213c4e", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325303139800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07b51168-dc2f-4e73-8775-764bcd4c3dc4", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":3716,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9bdc4e91fab22a33097a6798a6205ba2b218af0d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753097753518,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a656d67f7482586ad130a3694e8b27ca79554add75b5f7e40e386242cce251c906eaffaf4246b1511e258833c2d36c5b08a9226ae9ce1748caf88db0d7b416613a8b64d4cf53955ad974f11cfa0e549ca6d9fed263ebb4f2413cce57\"}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325303953700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9c2e575-4b66-4963-9529-f54735eba8a2", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325308283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bac4770-03b8-4997-a556-112379766792", "name": "Cache service initialization finished in 1 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325308559300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74e35b46-c6ed-48b4-ae89-70d10cdc3634", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325316333000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c26485d-5686-46b1-bbd0-b7f34b1140ab", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325324840500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "607193e3-8689-463f-ba96-080a26495906", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325324873400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ebfd81-ffb0-4bb2-b056-e35e4878e70a", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325329357200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "401b3e0c-75e4-41c2-9140-5e7bf9396c10", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325331146500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da4de079-be87-4db8-b0b7-fea15debfacd", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325337396300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a036ab05-1575-4d3e-b1f9-f9f0615bca2f", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325337455000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3926bec-aa16-4e2d-9cb2-4d84488ca371", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325346609000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12a43a4-22d8-4421-8c12-1a8243707457", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325346636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9130a2d0-1744-4fe2-935b-47cdc3469f0e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325350300800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e292f65c-edcf-4883-9e7c-309f23cfcd5f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325350342300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "554e3d04-1263-4626-b740-333e728ddf80", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325350419600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22928cea-1471-455d-a142-84e1e4608ccc", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325350463100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86510a91-209e-4ce7-9ef0-0a3a55cbd73e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325350473000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "307315b5-7deb-4950-b2c2-3b9c9be5c8ec", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325350479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de31ed8c-e2ec-49e0-9b24-d662b17af7c7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325350503100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "125ee36e-945d-4a93-b815-57fb6fcf39e3", "name": "require SDK: toolchains,ArkTS; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325351961800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2764bf50-07be-4f01-8d6a-d55d63df2763", "name": "Module entry task initialization takes 8 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325361691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f25d48eb-2246-4ec2-b6b7-05ae2b81dd5f", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325361742000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d80f8aa-29b0-460f-be9c-a14e75d83bb2", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325364574800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cdcd564-257b-4f23-85af-56b0fec7f4ce", "name": "hvigorfile, require result:  { harTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325370202200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7442da49-d5ee-41a3-8577-da1e695135a9", "name": "hvigorfile, binding system plugins { harTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325370247500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f24f512a-1ac2-4a05-9fc0-bfa78ad75bd8", "name": "<PERSON><PERSON><PERSON> ijkplayer Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325376508100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebb7bba3-dfc5-49bb-b203-c677bf8c41ab", "name": "<PERSON><PERSON><PERSON> ijkplayer's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325376535900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b95bde9d-4e22-4801-88c4-63c52937f6ee", "name": "Start initialize module-target build option map, moduleName=ijkplayer, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325377488100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6146db48-116f-4234-a2cd-d7e91781d7a5", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325377514900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8122a327-4848-451c-b2fc-2a98796fa9f0", "name": "Module 'ijkplayer' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325377937400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60002f62-b1b6-4c02-80fd-29088f7e945b", "name": "End initialize module-target build option map, moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325377948400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09dc8436-90e9-47eb-8c49-3d20f278959b", "name": "Module 'ijkplayer' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325377968900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25beb8d6-71b8-4868-9439-7ca2f53e5018", "name": "require SDK: toolchains,ArkTS,native; moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325379031100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "395fdd9a-abf6-4069-86a5-9f2e89f8d727", "name": "Mo<PERSON><PERSON> ijkplayer task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325381488700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8723af70-8715-4f19-98c6-eef8c9ee1f1e", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325381826700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e64975a-0b4d-42df-a607-48d3abf96be6", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325381874600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcfd860d-b63d-4de5-a09d-c11ba109ef11", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325381886800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff9aead-841c-4cef-af43-5ba2d82a2d15", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325381913500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b475cb5c-73d6-4d6e-b73b-ceed791c2a67", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325381919000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d731d93-2d27-4ef8-90a0-eebd48eca33d", "name": "<PERSON><PERSON><PERSON>_ijkplayer-2.0.3 Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325382363000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2e38d1b-352d-4e66-abac-bd5db4c10573", "name": "<PERSON><PERSON><PERSON> ohos_ijkplayer-2.0.3's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325382372700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e734c239-98eb-43ff-a298-81c1486aa3d7", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325383564400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2ed0f9b-67e3-43b1-b4e7-8c4d6987ed4d", "name": "Sdk init in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325389957700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bad4040-260a-4dad-ba61-d6cb5970ef3e", "name": "project has submodules:entry,ijkplayer", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325412346200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d754dca-cbf6-4a6a-b22e-e98fd74059ea", "name": "module:ijkplayer no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325414659300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595976d6-19ed-4ced-8981-a4e569a6ff0e", "name": "Project task initialization takes 28 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325417078600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d5ff191-5b82-4cad-80cf-6815989ca0a0", "name": "Sdk init in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325421530400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117731f6-5733-4da5-89eb-49f0d58fa324", "name": "Sdk init in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325428120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d3b8cb9-391a-4292-b35e-30a636831074", "name": "Configuration phase cost:121 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325428899900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd25960-b02f-417e-983b-ad806cfb4a3c", "name": "Configuration task cost before running: 124 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325430573100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13d929b6-056f-4908-98b4-244d54a9d772", "name": "ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325440027800, "endTime": 25325615071800}, "additional": {"children": [], "state": "success", "detailId": "009a8a65-9e56-448c-96e6-2ad252ac1716", "logId": "f8651919-13bd-496d-b119-1aed2400abd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "009a8a65-9e56-448c-96e6-2ad252ac1716", "name": "create ijkplayer:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325433116700}, "additional": {"logType": "detail", "children": [], "durationId": "13d929b6-056f-4908-98b4-244d54a9d772"}}, {"head": {"id": "636cfa41-9b67-4b30-afd8-362ec8cb42ce", "name": "ijkplayer : default@PreBuild start {\n  rss: 191586304,\n  heapTotal: 127225856,\n  heapUsed: 99043832,\n  external: 1078024,\n  arrayBuffers: 112668\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325439955700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf2739f-d308-4053-b8bc-f03eb4941637", "name": "Executing task :ijkplayer:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325440047600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b000e5-5af0-4494-9ee9-a6bf7d0647ad", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'D:\\\\harmonyFor\\\\DevEco Studio\\\\jbr' },\n  {\n    CLASSPATH: '.;D:\\\\houduan\\\\java1.8\\\\jdk\\\\lib\\\\dt.jar;D:\\\\houduan\\\\java1.8\\\\jdk\\\\lib\\\\tools.jar'\n  }\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325614672400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2be24592-6477-4cc2-a1bd-fdb6f32631f7", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'D:\\\\harmonyFor\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325614733500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb999b5-dfc9-4529-b216-56cf9eee416d", "name": "ijkplayer : default@PreBuild end {\n  rss: 204001280,\n  heapTotal: 130727936,\n  heapUsed: 111274416,\n  external: 1144755,\n  arrayBuffers: 79900\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325615047800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8651919-13bd-496d-b119-1aed2400abd4", "name": "Finished :ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325440027800, "endTime": 25325615071800}, "additional": {"logType": "info", "children": [], "durationId": "13d929b6-056f-4908-98b4-244d54a9d772"}}, {"head": {"id": "3f23be00-0d00-42c3-99d6-59f313486481", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325618362700, "endTime": 25331645549000}, "additional": {"children": ["7e7a0c6f-edcc-439c-aa61-1616815860e0", "5a6e75ed-dd1f-404e-9bfa-e54ba02e3641"], "state": "success", "detailId": "309679d2-be5e-4591-9b96-a24121c81f21", "logId": "c89658fa-5c8f-4479-85d9-a01782f9df43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "309679d2-be5e-4591-9b96-a24121c81f21", "name": "create ijkplayer:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325617404600}, "additional": {"logType": "detail", "children": [], "durationId": "3f23be00-0d00-42c3-99d6-59f313486481"}}, {"head": {"id": "d7bf6c4b-f02f-4673-a13b-e7649f638033", "name": "ijkplayer : default@BuildNativeWithCmake start {\n  rss: 204021760,\n  heapTotal: 130727936,\n  heapUsed: 111507848,\n  external: 1144755,\n  arrayBuffers: 79900\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325618335200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e2d64d-a0d0-4589-b595-c4d7edde0702", "name": "Executing task :ijkplayer:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325618378800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9690b6ac-c52c-43f2-8b9b-695470dd82eb", "name": "runTaskFromQueue task cost before running: 312 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325618511300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee0a28be-5c4d-490f-b4e9-0cb9fea59eb8", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325625196700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60061fd1-6906-4ddc-8ae3-bfbc4d673a3b", "name": "Clean the cmake cache file CMakeCache.txt due to configuration change.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325625409400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c109355-d81b-4bac-b7b2-33e45d0c8bc7", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325629294300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e7a0c6f-edcc-439c-aa61-1616815860e0", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Worker0", "startTime": 25325631481100, "endTime": 25331644864800}, "additional": {"children": [], "state": "success", "parent": "3f23be00-0d00-42c3-99d6-59f313486481", "logId": "781891cc-7f82-49e4-84cd-4d8c68114783"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "324e795d-ee36-4431-9509-3ba5df7d7d64", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325630619300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c31efe48-e120-4188-815b-919e5736cf28", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325631556200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbc4a64e-dceb-4c62-bd71-df93f6df0416", "name": "default@BuildNativeWithCmake work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325631702200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75c47d30-0a48-4309-bc12-aafc5dc32ff1", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325635444400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be3c5e08-ee39-47df-b2f4-aeb3fb7142ba", "name": "Clean the cmake cache file CMakeCache.txt due to configuration change.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325635757000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfbdd07f-9fec-4616-af09-e495afd6d07b", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325637997700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a6e75ed-dd1f-404e-9bfa-e54ba02e3641", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 3716, "tid": "Worker1", "startTime": 25325638795900, "endTime": 25331645481800}, "additional": {"children": [], "state": "success", "parent": "3f23be00-0d00-42c3-99d6-59f313486481", "logId": "1d618fde-5ab4-4de6-ad77-74c8994b0f8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "9854b5ab-0628-4be9-8f70-4848b1c61f77", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325638650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "610d725d-e224-4738-84eb-e66b13f55f47", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325638687500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a430cd-b157-4a93-a5bd-c1fc49d261b8", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325638813100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40796828-7bd0-4870-9608-47bac3b03646", "name": "default@BuildNativeWithCmake work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325638845700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a1d2d0d-c084-4d31-8e7e-97f5b3f2e291", "name": "ijkplayer : default@BuildNativeWithCmake end {\n  rss: 204451840,\n  heapTotal: 130727936,\n  heapUsed: 111981344,\n  external: 1144755,\n  arrayBuffers: 79900\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325638950100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7e8980-6dfe-4075-9bc6-b837ccd090ed", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331644998800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "781891cc-7f82-49e4-84cd-4d8c68114783", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Worker0", "startTime": 25325631481100, "endTime": 25331644864800}, "additional": {"logType": "info", "children": [], "durationId": "7e7a0c6f-edcc-439c-aa61-1616815860e0", "parent": "c89658fa-5c8f-4479-85d9-a01782f9df43"}}, {"head": {"id": "fa22239c-4275-4a24-8c0a-7653f361a7d9", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331645400600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f68cc03-36a7-4807-81ac-9f2b3f139c4a", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331645504300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d618fde-5ab4-4de6-ad77-74c8994b0f8a", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Worker1", "startTime": 25325638795900, "endTime": 25331645481800}, "additional": {"logType": "info", "children": [], "durationId": "5a6e75ed-dd1f-404e-9bfa-e54ba02e3641", "parent": "c89658fa-5c8f-4479-85d9-a01782f9df43"}}, {"head": {"id": "c89658fa-5c8f-4479-85d9-a01782f9df43", "name": "Finished :ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325618362700, "endTime": 25331645549000}, "additional": {"logType": "info", "children": ["781891cc-7f82-49e4-84cd-4d8c68114783", "1d618fde-5ab4-4de6-ad77-74c8994b0f8a"], "durationId": "3f23be00-0d00-42c3-99d6-59f313486481"}}, {"head": {"id": "aa0e4e6d-f7fa-4fc2-acf3-239db1f0902c", "name": "ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331647927500, "endTime": 25331648075000}, "additional": {"children": [], "state": "success", "detailId": "fa7ae04c-afc2-41ad-9e52-7c63338ae651", "logId": "0d8d44ff-79b5-413a-a8ed-59a54febcd1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "fa7ae04c-afc2-41ad-9e52-7c63338ae651", "name": "create ijkplayer:compileNative task", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331647785400}, "additional": {"logType": "detail", "children": [], "durationId": "aa0e4e6d-f7fa-4fc2-acf3-239db1f0902c"}}, {"head": {"id": "c2086652-f24f-4c0f-be43-5f28b4f1223f", "name": "ijkplayer : compileNative start {\n  rss: 301760512,\n  heapTotal: 132562944,\n  heapUsed: 98069080,\n  external: 1045256,\n  arrayBuffers: 79900\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331647905500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e10cd7-c223-4bb4-9eea-b0bd3dc29de5", "name": "Executing task :ijkplayer:compileNative", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331647940300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fe4a3bf-8747-4f50-b006-1f8ca0e0f164", "name": "ijkplayer : compileNative end {\n  rss: 301764608,\n  heapTotal: 132562944,\n  heapUsed: 98078280,\n  external: 1045256,\n  arrayBuffers: 79900\n}", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331648060500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8d44ff-79b5-413a-a8ed-59a54febcd1d", "name": "Finished :ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331647927500, "endTime": 25331648075000}, "additional": {"logType": "info", "children": [], "durationId": "aa0e4e6d-f7fa-4fc2-acf3-239db1f0902c"}}, {"head": {"id": "b21be06c-c537-4674-b028-540c7ce1f565", "name": "BUILD SUCCESSFUL in 6 s 342 ms ", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331648418900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "90066981-648d-4388-a781-51e05a46ea17", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25325306864300, "endTime": 25331648804000}, "additional": {"time": {"year": 2025, "month": 7, "day": 21, "hour": 19, "minute": 35}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "9d73422d-deef-4ff5-b9bd-856aca53979d", "name": "Update task ijkplayer:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331649570700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11adf4a2-ac1b-402e-b199-133b1167e5fa", "name": "Update task ijkplayer:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331650546100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fdf186c-c19e-4aed-9de3-42e54ca3caa0", "name": "Update task ijkplayer:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331650786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f78efd59-e729-43c1-ac0f-431c2d7e393f", "name": "Update task ijkplayer:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331650972800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae342bd-0569-44f8-80c4-fc3abf1a9d1a", "name": "Update task ijkplayer:default@PreBuild input file:D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331651179500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08704581-48f3-421c-bf33-bd4e48a54148", "name": "Incremental task ijkplayer:default@PreBuild post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 3716, "tid": "Main Thread", "startTime": 25331651713600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}