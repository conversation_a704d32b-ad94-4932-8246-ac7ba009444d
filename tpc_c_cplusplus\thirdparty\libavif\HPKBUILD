# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON><PERSON> <<EMAIL>>
# Maintainer: <PERSON><PERSON> <<EMAIL>>

pkgname=libavif
pkgver=v1.0.1
pkgrel=0 
pkgdesc="This library aims to be a friendly, portable C implementation of the AV1 Image File Format" 
url="https://github.com/AOMediaCodec/libavif.git"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD-2-Clause")
depends=("libyuv" "libpng" "jpeg" "googletest" "libaom")
makedepends=()
source="https://github.com/AOMediaCodec/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="cmake"
patchflag=true
builddir=${pkgname}-${pkgver:1}
packagename=${builddir}.tar.gz

prepare() {
    mkdir -p $builddir/${ARCH}-build
}

build() {
    cd $builddir
    PKG_CONFIG_LIBDIR="${pkgconfigpath}" cmake "$@" -DCMAKE_C_FLAGS=-Wno-unused-command-line-argument \
    -DCMAKE_CXX_FLAGS=-Wno-unused-command-line-argument -DAVIF_BUILD_APPS=ON -DAVIF_BUILD_TESTS=ON \
    -DAVIF_BUILD_EXAMPLES=ON -DAVIF_ENABLE_GTEST=ON -DAVIF_CODEC_AOM=ON \
    -B${ARCH}-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C ${ARCH}-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd ${builddir}
    $MAKE -C ${ARCH}-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # ctest
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}

