# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=lunasvg
pkgver=v2.3.9
pkgrel=0
pkgdesc="SVG rendering library in C++"
url="https://github.com/sammycage/lunasvg"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT")
depends=()
makedepends=()

source="https://gitee.com/lycium_pkg_mirror/$pkgname/repository/archive/v2.3.9.zip"
downloadpackage=true
autounpack=true

builddir=$pkgname-${pkgver}
packagename=$builddir.zip

# svg文件用于测试svg转换png程序
svgfileurl=https://gitee.com/openharmony-sig/ohos-svg/raw/master/entry/src/main/resources/rawfile/line.svg
lunasvgtestfile=./lunasvgtest.svg

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" \
        -DLUNASVG_BUILD_EXAMPLES=ON \
        -DBUILD_SHARED_LIBS=ON \
        -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    curl ${svgfileurl} > ${lunasvgtestfile}
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir
}
