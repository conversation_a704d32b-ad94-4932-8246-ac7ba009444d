# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>, zhengxiaoqing <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=libde265
pkgver=v1.0.15
pkgrel=0
pkgdesc="libde265 is an open source implementation of the h.265 video codec."
url="https://github.com/strukturag/libde265"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL3.0")
depends=()
makedepends=()
source="https://github.com/strukturag/$pkgname/archive/refs/tags/$pkgver.tar.gz"

downloadpackage=true
autounpack=true
buildtools="cmake"

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() { 
    mkdir -p $builddir/$ARCH-build
}

build() { 
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DENABLE_ENCODER=ON -DENABLE_SDL=OFF -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() { 
    cd $builddir
    $MAKE VERBOSE=1 -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() { 
    echo "The test must be on an OpenHarmony device!"
    # 测试方式
    # 进入构建目录
    # 执行: ./dec265/dec265 ../testdata/girlshy.h265 
}

# 清理环境
cleanbuild() { 
    rm -rf ${PWD}/$builddir  #${PWD}/$packagename
}