# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the ImageMagick License (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
# 
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    chmod 777 -R /tmp
    chmod 777 -R $builddir-$ARCH-build
    chmod 777 -R $LYCIUM_FAULT_PATH/$pkgname
    mkdir -p /usr/sbin/
    ln -s /bin/install /usr/bin/
    ln -s $LYCIUM_THIRDPARTY_ROOT/tzdb/tzdb-2024a-$ARCH-build/zic /usr/sbin/

    touch Enter.txt
    echo "




    " > Enter.txt
    busybox adduser postgres < Enter.txt
    
    cd $builddir-$ARCH-build
    su postgres make check > ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        mkdir -p ${LYCIUM_FAULT_PATH}/${pkgname}
        cp test-suite.log ${LYCIUM_FAULT_PATH}/${pkgname}/
    fi

    rm Enter.txt

    chmod 771 -R /tmp
    chmod 664 -R $builddir-$ARCH-build
    chmod 664 -R $LYCIUM_FAULT_PATH/$pkgname

    busybox deluser postgres

    cd $OLDPWD
    return $res
}
