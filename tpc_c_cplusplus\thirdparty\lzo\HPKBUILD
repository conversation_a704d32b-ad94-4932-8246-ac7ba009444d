# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=lzo
pkgver=2.10
pkgrel=0
pkgdesc="LZO is a real-time data compression library, it is particularly suitable for the need for rapid compression and decompression of the occasion."
url="http://www.oberhumer.com/opensource/lzo/"
archs=("armeabi-v7a" "arm64-v8a")
license=("GNU GENERAL PUBLIC LICENSE V2.0")
depends=()
makedepends=()
source="http://www.oberhumer.com/opensource/$pkgname/download/$pkgname-$pkgver.tar.gz"

downloadpackage=true
autounpack=true
buildtools="configure"
builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz
source envset.sh
host=

prepare() {
    cp -rf $builddir $pkgname-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
    return 0
}

build() {
    cd $pkgname-$ARCH-build
    ./configure "$@" --host=$host > $buildlog 2>&1
    $MAKE V=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $pkgname-$ARCH-build
    $MAKE install >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    unset host
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

cleanbuild() {
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build #${PWD}/$packagename
}