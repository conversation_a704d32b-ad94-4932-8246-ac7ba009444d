# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
pkgname=gettext
pkgver=0.22
pkgrel=0
pkgdesc="gettext is an important step for the GNU Translation Project, as it is an asset on which we may build many other steps. This package offers to programmers, translators, and even users, a well integrated set of tools and documentation."
url="https://www.gnu.org/software/gettext"
archs=("armeabi-v7a" "arm64-v8a")
license=("GPLv3.0 license")
depends=()
makedepends=()
source="https://ftp.gnu.org/pub/gnu/$pkgname/$pkgname-$pkgver.tar.gz"
buildtools="configure"

autounpack=true
downloadpackage=true

builddir=$pkgname-$pkgver
packagename=$pkgname-$pkgver.tar.gz

source envset.sh
host=

prepare() {
    mkdir $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ];then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ];then
        setarm64ENV
        host=aarch64-linux
    else
        echo "$ARCH not support"
        return -1
    fi
}

build() {
    cd $builddir/$ARCH-build
    LDFLAGS="--static $LDFLAGS" CFLAGS="-static -Wno-error=int-conversion $CFLAGS" \
        ../configure "$@" --host=$host --srcdir=`pwd`/../ > `pwd`/build.log 2>&1
    make -j4  VERBOSE=1 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install VERBOSE=1 >> `pwd`/build.log 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ];then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ];then
        unsetarm64ENV
    else
        echo "$ARCH not support"
        return -1
    fi
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # TODO
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}