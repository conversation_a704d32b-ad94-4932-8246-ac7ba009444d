# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    cd $builddir/$ARCH-build
    echo "total test 4"  > $logfile 2>&1
    if [ $ARCH == "armeabi-v7a" ]
    then
        export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/data/tpc_c_cplusplus/lycium/usr/proj.4/armeabi-v7a/lib/
    elif [ $ARCH == "arm64-v8a" ]
    then
        export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/data/tpc_c_cplusplus/lycium/usr/proj.4/arm64-v8a/lib/
    else
        echo "$ARCH not support!" >> $logfile 2>&1
        cd $OLDPWD
        return -1
    fi
    
    ctest >> ${logfile} 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "test failed" >> $logfile 2>&1
        cd $OLDPWD
        return $res
    else
        echo "test passed" >> $logfile 2>&1
    fi 

    cd $OLDPWD
    return $res
}
