diff -Nur attr-2.4.47/include/xattr.h attr-2.4.47_patch/include/xattr.h
--- attr-2.4.47/include/xattr.h	2013-05-19 12:53:54.000000000 +0800
+++ attr-2.4.47_patch/include/xattr.h	2023-07-13 15:24:18.536282521 +0800
@@ -30,6 +30,47 @@
 #define XATTR_CREATE  0x1       /* set value, fail if attr already exists */
 #define XATTR_REPLACE 0x2       /* set value, fail if attr does not exist */
 
+#ifdef	__cplusplus
+	# define __BEGIN_DECLS	extern "C" {
+	# define __END_DECLS	}             
+#else                                
+	# define __BEGIN_DECLS                
+	# define __END_DECLS                  
+#endif
+
+#if defined __GNUC__ && defined __GNUC_MINOR__
+# define __GNUC_PREREQ(maj, min) \
+	((__GNUC__ << 16) + __GNUC_MINOR__ >= ((maj) << 16) + (min))
+#else
+# define __GNUC_PREREQ(maj, min) 0
+#endif
+
+# if __GNUC_PREREQ (4, 6) && !defined _LIBC
+#  define __LEAF , __leaf__
+#  define __LEAF_ATTR __attribute__ ((__leaf__))
+# else
+#  define __LEAF
+#  define __LEAF_ATTR
+# endif
+
+# if !defined __cplusplus && __GNUC_PREREQ (3, 3)
+#  define __THROW	__attribute__ ((__nothrow__ __LEAF))
+#  define __THROWNL	__attribute__ ((__nothrow__))
+#  define __NTH(fct)	__attribute__ ((__nothrow__ __LEAF)) fct
+#  define __NTHNL(fct)  __attribute__ ((__nothrow__)) fct
+# else
+#  if defined __cplusplus && __GNUC_PREREQ (2,8)
+#   define __THROW	throw ()
+#   define __THROWNL	throw ()
+#   define __NTH(fct)	__LEAF_ATTR fct throw ()
+#   define __NTHNL(fct) fct throw ()
+#  else
+#   define __THROW
+#   define __THROWNL
+#   define __NTH(fct)	fct
+#   define __NTHNL(fct) fct
+#  endif
+# endif
 
 __BEGIN_DECLS
 
