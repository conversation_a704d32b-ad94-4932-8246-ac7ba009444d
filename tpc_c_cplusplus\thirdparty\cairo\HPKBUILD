# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=cairo
pkgver=1.17.8
pkgrel=0
pkgdesc="Cairo is a 2D graphics library with support for multiple output devices."
url="https://gitlab.freedesktop.org/cairo/cairo"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL-2.1" "MPL 1.1")
depends=("glib" "freetype2" "fontconfig" "libpng" "zlib" "pixman" "libxml2" "libexpat" "libffi" "pcre2")
makedepends=("meson" "ninja")

# 原仓地址: https://gitlab.freedesktop.org/cairo/$pkgname/-/archive/$pkgver/$pkgname-$pkgver.tar.gz ,因网络原因使用镜像
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"

buildtools="meson"
downloadpackage=true
autounpack=true

builddir=$pkgname-$pkgver
packagename=$pkgname-$pkgver.zip

patch_flag=true

pkgconfigpath=

prepare() {
    # meson 编译没有配置pkgconfigpath,需自己配置
    for depend in ${depends[@]}
    do
        dependpath=$LYCIUM_ROOT/usr/$depend/$ARCH/lib/pkgconfig
        if [ ! -d ${dependpath} ]
        then
            continue
        fi
        pkgconfigpath=$pkgconfigpath"${dependpath}:"
    done
    pkgconfigpath=${pkgconfigpath%:*}

    mkdir -p $builddir/$ARCH-build
    cp $ARCH-cross-file.txt ./$builddir
    if [ "$patch_flag" == true ]
    then
        cd $builddir
	#屏蔽涉及xcb，x11以及特定字体的测试用例，OH不支持
        patch -p1 < ../cairo_ohos_test.patch > $buildlog 2>&1  
        cd $OLDPWD
        patch_flag=false
    fi
}

build() {
    cd $builddir
    ohos_sdk_path=${OHOS_SDK//\//\\\/}
    sed -i 's/ohos_sdk/'"$ohos_sdk_path"'/g' $ARCH-cross-file.txt

    meson setup $ARCH-build --cross-file $ARCH-cross-file.txt \
        --pkg-config-path $pkgconfigpath \
        --prefix=$LYCIUM_ROOT/usr/$pkgname/$ARCH > $ARCH-build/build.log 2>&1
    $Ninja -v -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $Ninja -v -C $ARCH-build install >> $buildlog 2>&1
    pkgconfigpath=""
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # TODO
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir
}
