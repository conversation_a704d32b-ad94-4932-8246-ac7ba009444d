diff --git a/configure b/configure
index ae289f77b..75aae13a2 100755
--- a/configure
+++ b/configure
@@ -98,6 +98,7 @@ EOF
 # all_platforms is a list of all supported target platforms. Maintain
 # alphabetically by architecture, generic-gnu last.
 all_platforms="${all_platforms} arm64-android-gcc"
+all_platforms="${all_platforms} aarch64-linux"
 all_platforms="${all_platforms} arm64-darwin-gcc"
 all_platforms="${all_platforms} arm64-darwin20-gcc"
 all_platforms="${all_platforms} arm64-darwin21-gcc"
@@ -119,6 +120,7 @@ all_platforms="${all_platforms} armv7-win32-vs16"
 all_platforms="${all_platforms} armv7-win32-vs17"
 all_platforms="${all_platforms} armv7s-darwin-gcc"
 all_platforms="${all_platforms} armv8-linux-gcc"
+all_platforms="${all_platforms} arm-linux"
 all_platforms="${all_platforms} loongarch32-linux-gcc"
 all_platforms="${all_platforms} loongarch64-linux-gcc"
 all_platforms="${all_platforms} mips32-linux-gcc"
