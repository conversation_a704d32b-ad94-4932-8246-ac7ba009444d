<?xml version="1.0" encoding="UTF-8"?>
<module type="EMPTY_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$/../.." />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module-library" exported="" scope="PROVIDED">
      <library name="Native-API 11" type="CPP_LIBRARY">
        <CLASSES />
        <JAVADOC />
        <SOURCES>
          <root url="file://$MODULE_DIR$/../../../../harmonyFor/openSDK/11/native/sysroot" />
        </SOURCES>
      </library>
    </orderEntry>
    <orderEntry type="module-library" exported="" scope="PROVIDED">
      <library name="ArkTS-API 11" type="ArkUI">
        <CLASSES />
        <JAVADOC />
        <SOURCES>
          <root url="file://$MODULE_DIR$/../../../../harmonyFor/openSDK/11/ets/api" />
          <root url="file://$MODULE_DIR$/../../../../harmonyFor/openSDK/11/ets/component" />
        </SOURCES>
      </library>
    </orderEntry>
  </component>
</module>