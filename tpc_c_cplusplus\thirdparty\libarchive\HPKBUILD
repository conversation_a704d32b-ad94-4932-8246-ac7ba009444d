# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# Maintainer:    <EMAIL>

pkgname=libarchive
pkgver=v3.6.2
pkgrel=0
pkgdesc="The libarchive project develops a portable, efficient C library that can read and write streaming archives in a variety of formats."
url="http://www.libarchive.org/"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD")
depends=("xz" "zstd")
makedepends=()
# 官网地址：https://github.com/libarchive/libarchive/archive/refs/tags/$pkgver.tar.gz，因网络原因采用gitee mirrors
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"

downloadpackage=true
autounpack=true
buildtools="cmake"

builddir=${pkgname}-$pkgver
packagename=$builddir.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    # 对最关键一步的退出码进行判断
    ret=$?
    cd $OLDPWD
    return $ret
}

# 打包安装
package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

# 进行测试的准备和说明
check() {
    echo "The test must be on an OpenHarmony device!"
    # 测试方式
    # 进入构建目录
    # 执行: ctest
}

# 清理环境fmt
cleanbuild() {
   rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
