# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=farmhash
pkgver=master
pkgrel=0
pkgdesc="FarmHash, a family of hash functions"
url="https://github.com/google/$pkgname"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache-2.0 license")
depends=()
makedepends=()

#官网地址：https://github.com/google/$pkgname.git，因网络原因采用gitee mirrors
source="https://gitee.com/lycium_pkg_mirror/$pkgname"
commitid=0d859a811870d10f53a594927d0d0b97573ad06d

autounpack=false
downloadpackage=false
builddir=$pkgname-${commitid}
packagename=
cloneflag=true
buildtools="configure"

source envset.sh
patchflag=true
host=

prepare() {
    if [ $cloneflag == true ]
    then
        mkdir -p $builddir
        git clone -b $pkgver $source $builddir > $publicbuildlog 2>&1
        ret=$?
        if [ $ret -ne 0 ]
        then
            return $ret
        fi
        cloneflag=false
        
        cd $builddir
        git reset --hard $commitid >> $publicbuildlog 2>&1
        ret=$?
        cd $OLDPWD
        if [ $ret -ne 0 ]
        then
            return $ret
        fi
    fi
    
    cp -arf $builddir $builddir-$ARCH-build
    
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
        export LDFLAGS="${OHOS_SDK}/native/llvm/lib/clang/$CLANG_VERSION/lib/arm-linux-ohos/a7_hard_neon-vfpv4/libclang_rt.builtins.a ${LDFLAGS}"
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    else
        echo "${ARCH} not support"
        return -1
    fi

    cd $builddir-$ARCH-build
    autoreconf -ifv > $buildlog 2>&1
    cd $OLDPWD
}


build() {
    cd $builddir-$ARCH-build
    ./configure "$@" --host=$host >> $buildlog 2>&1
    $MAKE VERBOSE=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    $MAKE install >> $buildlog 2>&1
    cd $OLDPWD
    unset host
    
    if [ $ARCH == "armeabi-v7a" ]
    then
          unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
          unsetarm64ENV
    else
        echo "${ARCH} not support"
        return -1
    fi
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 进入编译目录，执行 ctest
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir ${PWD}/$builddir-armeabi-v7a-build ${PWD}/$builddir-arm64-v8a-build  #${PWD}/packagename
}
