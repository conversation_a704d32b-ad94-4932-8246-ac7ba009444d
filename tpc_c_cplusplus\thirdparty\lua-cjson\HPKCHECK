# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1    # 导入HPKBUILD文件
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

# 在OH环境执行测试的接口
openharmonycheck() {
    res=0
    cd ${LYCIUM_ROOT}/../lycium/usr/${pkgname}/${ARCH}/lib

    # 使用lua-5.4.6完成测试
    mkdir -p /usr/local/lib/lua/5.4/cjson
    cp ${LYCIUM_ROOT}/../lycium/usr/${pkgname}/${ARCH}/lib/lua/cjson/util.lua /usr/local/lib/lua/5.4/cjson/
    cp ${LYCIUM_ROOT}/../lycium/usr/${pkgname}/${ARCH}/lib/*.so /usr/local/lib/lua/5.4/
    cp ${LYCIUM_ROOT}/../lycium/usr/${pkgname}/${ARCH}/lib/tests/octets-escaped.dat .

    # Decode all UTF-16 escapes测试项需要utf8.dat，该文件是通过genutf8.pl脚本生成的，如果不支持Perl脚本则需要手动上传资源文件
    ${LYCIUM_ROOT}/../lycium/usr/lua-cjson/${ARCH}/ib/tests/genutf8.pl
    cp ${LYCIUM_ROOT}/../lycium/usr/${pkgname}/${ARCH}/lib/tests/utf8.dat .

    ${LYCIUM_ROOT}/../lycium/usr/lua/${ARCH}/bin/lua \
        ${LYCIUM_ROOT}/../lycium/usr/${pkgname}/${ARCH}/lib/tests/test.lua > ${logfile} 2>&1
    res=$?
    rm -rf /usr/local/lib
    rm -rf octets-escaped.dat
    rm -rf utf8.dat
    cd $OLDPWD
    return $res
}
