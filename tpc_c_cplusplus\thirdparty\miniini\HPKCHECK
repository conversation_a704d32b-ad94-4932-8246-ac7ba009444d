# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

checkprepare() {
    # 此库生成的路径非{ARCH}/lib,故在此单独加载该库路径
    export LD_LIBRARY_PATH=${LYCIUM_ROOT}/usr/${pkgname}/${ARCH}/usr/lib:$LD_LIBRARY_PATH
}

openharmonycheck() {
    res=0    
    cd $builddir-$ARCH-build
    ./bin/tester > $logfile 2>&1
    res=$?
    cd $OLDPWD

    return $res
}
