# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>,litie <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=gc
pkgver=7f6f17c8b3425df6cd27d6f9385265b23034a793
pkgvel=0
pkgdesc="gc is an implementation of a conservative, thread-local, mark-and-sweep garbage collector. The implementation provides a fully functional replacement for the standard POSIX malloc(), calloc(), realloc(), and free() calls."
url="https://github.com/mkirchner/gc"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT License")
depends=()
makedepends=()

source="https://github.com/mkirchner/$pkgname/archive/$pkgver.zip"

builddir=${pkgname}-${pkgver}
packagename=${builddir}.zip
autounpack=ture
downloadpackage=ture
buildtools="make"
cc=
ar=

source envset.sh
patchflag=true

# gc 采用makefile编译构建，为了保留构建环境(方便测试)。因此同一份源码在解压后分为两份,各自编译互不干扰
prepare() {
	if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../gc_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    cp -rf ${builddir} $builddir-${ARCH}-build
    if [ $ARCH == "armeabi-v7a" ]; then
        cc=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang
        ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
    fi
    if [ $ARCH == "arm64-v8a" ]; then
        cc=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang
        ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
    fi

    return 0
}

build() {
    cd $builddir-${ARCH}-build/src
    $MAKE LIB="$ar cr" CC="$cc" -j4 > build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    install_dir=${LYCIUM_ROOT}/usr/${pkgname}/${ARCH}
    mkdir -p ${install_dir}/lib
    cd ${builddir}-${ARCH}-build/
    cp -arf src/libgc.so ${install_dir}/lib/
	mkdir -p ${install_dir}/include
    cp -arf ./src/gc.h ${install_dir}/include
    cd ${OLDPWD}

    return 0
}

check() {
    echo "The test must be on an OpenHarmony device!"
	cd $builddir-${ARCH}-build/test
	$MAKE LIB="$ar cr" CC="$cc" -j4 > build.log 2>&1
	ret=$?
	cd $OLDPWD
	unset ar
    unset cc
	return $ret
}

cleanbuild() {
    rm -rf ${PWD}/${builddir} ${PWD}/${builddir}-armeabi-v7a-build ${PWD}/${builddir}-arm64-v8a-build #${PWD}/$packagename
}
