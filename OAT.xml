<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <oatconfig>
        <filefilterlist>
            <filefilter
                desc="Filters for compatibility，license header policies"
                name="copyrightPolicyFilter">
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/ijkplayer/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/ijksdl/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/third_party/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/utils/opensles/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/utils/hashmap/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/utils/ffmpeg/.*"
                    type="filepath"/>
                <filteritem
                    desc="NDK头文件，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/utils/napi/native_common.h"
                    type="filepath"/>
                <filteritem
                    desc="hvigor配置文件，DevEco Studio自动生成，不手动修改"
                    name="hvigorfile.js"
                    type="filename"/>
                <filteritem
                    desc="hvigor配置文件，DevEco Studio自动生成，不手动修改"
                    name="*.json5"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.GPLv2"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.GPLv3"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.LGPLv2.1"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.LGPLv2.1.txt"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.LGPLv3"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="MODULE_LICENSE_APACHE2"
                    type="filename"/>
                <filteritem type="filepath" name="hvigor/hvigor-wrapper.js" desc="配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="hvigorw.bat" desc="hvigorw配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="hvigorw" desc="hvigorw配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filepath" name="hvigor/hvigor-wrapper.js" desc="配置文件，DevEco Studio自动生成，不手动修改"/>
				<filteritem type="filepath" name="ijkplayer/oh-package.json5" desc="配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="hvigor-config.json5" desc="配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="oh-package.json5" desc="hvigor配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="hvigorfile.*" desc="hvigor配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filepath" name="doc/.*" desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"/>
                <filteritem type="filename" name="LICENSE" desc="LICENSE说明，不需要添加版权头"/>
                <filteritem type="filename" name=".gitmodules" desc="项目工程配置文件，不需要添加版权头"/>
            </filefilter>
            <filefilter
                desc="Filters for compatibility，license header policies"
                name="defaultPolicyFilter">
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/ijkplayer/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/ijksdl/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/third_party/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/utils/opensles/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/utils/hashmap/.*"
                    type="filepath"/>
                <filteritem
                    desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/utils/ffmpeg/.*"
                    type="filepath"/>
                <filteritem
                    desc="NDK头文件，不修改许可证头，以防有修改许可证风险"
                    name="ijkplayer/src/main/cpp/utils/napi/native_common.h"
                    type="filepath"/>
                <filteritem
                    desc="hvigor配置文件，DevEco Studio自动生成，不手动修改"
                    name="hvigorfile.js"
                    type="filename"/>
                <filteritem
                    desc="hvigor配置文件，DevEco Studio自动生成，不手动修改"
                    name="*.json5"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.GPLv2"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.GPLv3"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.LGPLv2.1"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.LGPLv2.1.txt"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="COPYING.LGPLv3"
                    type="filename"/>
                <filteritem
                    desc="ijkplayer原库的版权协议，不修改，以防有修改版权风险"
                    name="MODULE_LICENSE_APACHE2"
                    type="filename"/>
                <filteritem type="filepath" name="hvigor/hvigor-wrapper.js" desc="配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="hvigorw.bat" desc="hvigorw配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="hvigorw" desc="hvigorw配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filepath" name="hvigor/hvigor-wrapper.js" desc="配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="hvigor-config.json5" desc="配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="oh-package.json5" desc="hvigor配置文件，DevEco Studio自动生成，不手动修改"/>
				<filteritem type="filepath" name="ijkplayer/oh-package.json5" desc="配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filename" name="hvigorfile.*" desc="hvigor配置文件，DevEco Studio自动生成，不手动修改"/>
                <filteritem type="filepath" name="doc/.*" desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险"/>
                <filteritem type="filepath" name="NOTICE" desc="NOTICE文件"/>
                <filteritem type="filename" name="LICENSE" desc="LICENSE说明，不需要添加版权头"/>
                <filteritem type="filename" name=".gitmodules" desc="项目工程配置文件，不需要添加版权头"/>
            </filefilter>
			
			<filefilter desc="Filters for binary file policies" name="binaryFileTypePolicyFilter">
				<filteritem desc="配置文件，DevEco Studio自动生成，不手动修改" name="hvigor/hvigor-wrapper.js" type="filepath"/>
				<filteritem desc="配置文件，DevEco Studio自动生成，不手动修改" name="hvigor-config.json5" type="filename"/>
				<filteritem desc="hvigor配置文件，DevEco Studio自动生成，不手动修改" name="oh-package.json5" type="filename"/>
				<filteritem desc="hvigor配置文件，DevEco Studio自动生成，不手动修改" name="build-profile.json5" type="filename"/>
				<filteritem desc="hvigor配置文件，DevEco Studio自动生成，不手动修改" name="app.json5" type="filename"/>
                <filteritem desc="hvigor配置文件，DevEco Studio自动生成，不手动修改" name="hvigorfile.*" type="filename"/>
                <filteritem desc="png图片格式，用于展示示例" name="*.png" type="filename"/>
                <filteritem desc="gif图片格式，用于展示示例" name="*.gif" type="filename"/>
                <filteritem desc="jpg图片格式，用于展示示例" name="*.jpg" type="filename"/>
                <filteritem desc="jpeg图片格式，用于展示示例" name="*.jpeg" type="filename"/>
                <filteritem desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险" name="ijkplayer/src/main/cpp/third_party/.*" type="filepath"/>
                <filteritem desc="第三方开源软件源码，不修改许可证头，以防有修改许可证风险" name="ijkplayer/libs/.*" type="filepath"/>
			</filefilter>
			
        </filefilterlist>
    </oatconfig>
</configuration>