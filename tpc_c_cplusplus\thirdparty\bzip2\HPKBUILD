# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=bzip2
pkgver=1_0_6
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("public domain" "LGPLv2.1" "GPLv2" "GPLv3")
depends=()
makedepends=()

# 官方下载地址https://sourceforge.net/projects/$pkgname/files/$pkgname-$pkgver.tar.gz受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/lycium_pkg_mirror/$pkgname/repository/archive/$pkgname-$pkgver.zip"

autounpack=true
downloadpackage=true
buildtools="make"

builddir=$pkgname-$pkgname-${pkgver}
packagename=$builddir.zip

cc=
ar=
ranlib=
# bzip2 采用makefile编译构建，为了保留构建环境(方便测试)。因此同一份源码在解压后分为两份,各自编译互不干扰
prepare() {
    cp -rf $builddir $builddir-$ARCH-build
    cd $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        cc=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang
        ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
        ranlib=${OHOS_SDK}/native/llvm/bin/llvm-ranlib
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        cc=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang
        ar=${OHOS_SDK}/native/llvm/bin/llvm-ar
        ranlib=${OHOS_SDK}/native/llvm/bin/llvm-ranlib
    fi
    cd $OLDPWD # 1> /dev/null
}

build() {
    cd $builddir-$ARCH-build
    make CC=${cc} AR=${ar} RANLIB=${ranlib} -j4 libbz2.a bzip2 bzip2recover > `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    make install PREFIX=$LYCIUM_ROOT/usr/$pkgname/$ARCH >> `pwd`/build.log 2>&1
    cd $OLDPWD
    unset cc ar ranlib
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # make check
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build #${PWD}/$packagename
}
