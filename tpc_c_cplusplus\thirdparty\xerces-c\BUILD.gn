# Copyright (c) 2021 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

declare_args() {
   enable_xerces_test = false
}

config("libxerces-c_config") {
    cflags_cc = [
        "-frtti",
        "-fexceptions",
        "-Wno-header-hygiene",
        "-DHAVE_CONFIG_H=1",
        "-DXERCES_BUILDING_LIBRARY=1",
        "-D_FILE_OFFSET_BITS=64",
        "-D_THREAD_SAFE=1",
        "-DXERCES_USE_TRANSCODER_ICU=1",
        "-Dxerces_c_EXPORTS",
        "-Wno-long-long",
        "-Woverlength-strings",
        "-Woverloaded-virtual",
        "-Wno-variadic-macros",
        "-Wno-misleading-indentation",
        "-Wno-memset-transposed-args",
        "-Wno-unused-variable",
        "-fstrict-aliasing",
        "-fPIC",
    ]

    cflags = [
        "-DHAVE_CONFIG_H=1",
        "-DXERCES_BUILDING_LIBRARY=1",
        "-D_FILE_OFFSET_BITS=64",
        "-D_THREAD_SAFE=1",
        "-Dxerces_c_EXPORTS",
        "-fPIC",
    ]

    include_dirs = [
        "xerces-c/src",
        "xerces-c",
        "adapted",
        "//third_party/icu/icu4c/source/common",
    ]
}

ohos_shared_library("libxerces-c") {
    sources= [
        "xerces-c/src/xercesc/util/Base64.cpp",
        "xerces-c/src/xercesc/util/BinFileInputStream.cpp",
        "xerces-c/src/xercesc/util/BinInputStream.cpp",
        "xerces-c/src/xercesc/util/BinMemInputStream.cpp",
        "xerces-c/src/xercesc/util/BitSet.cpp",
        "xerces-c/src/xercesc/util/DefaultPanicHandler.cpp",
        "xerces-c/src/xercesc/util/EncodingValidator.cpp",
        "xerces-c/src/xercesc/util/HeaderDummy.cpp",
        "xerces-c/src/xercesc/util/HexBin.cpp",
        "xerces-c/src/xercesc/util/JanitorExports.cpp",
        "xerces-c/src/xercesc/util/KVStringPair.cpp",
        "xerces-c/src/xercesc/util/Mutexes.cpp",
        "xerces-c/src/xercesc/util/PanicHandler.cpp",
        "xerces-c/src/xercesc/util/PlatformUtils.cpp",
        "xerces-c/src/xercesc/util/PSVIUni.cpp",
        "xerces-c/src/xercesc/util/QName.cpp",
        "xerces-c/src/xercesc/util/regx/ASCIIRangeFactory.cpp",
        "xerces-c/src/xercesc/util/regx/BlockRangeFactory.cpp",
        "xerces-c/src/xercesc/util/regx/BMPattern.cpp",
        "xerces-c/src/xercesc/util/regx/CharToken.cpp",
        "xerces-c/src/xercesc/util/regx/ClosureToken.cpp",
        "xerces-c/src/xercesc/util/regx/ConcatToken.cpp",
        "xerces-c/src/xercesc/util/regx/Match.cpp",
        "xerces-c/src/xercesc/util/regx/Op.cpp",
        "xerces-c/src/xercesc/util/regx/OpFactory.cpp",
        "xerces-c/src/xercesc/util/regx/ParenToken.cpp",
        "xerces-c/src/xercesc/util/regx/ParserForXMLSchema.cpp",
        "xerces-c/src/xercesc/util/regx/RangeFactory.cpp",
        "xerces-c/src/xercesc/util/regx/RangeToken.cpp",
        "xerces-c/src/xercesc/util/regx/RangeTokenMap.cpp",
        "xerces-c/src/xercesc/util/regx/RegularExpression.cpp",
        "xerces-c/src/xercesc/util/regx/RegxParser.cpp",
        "xerces-c/src/xercesc/util/regx/RegxUtil.cpp",
        "xerces-c/src/xercesc/util/regx/StringToken.cpp",
        "xerces-c/src/xercesc/util/regx/Token.cpp",
        "xerces-c/src/xercesc/util/regx/TokenFactory.cpp",
        "xerces-c/src/xercesc/util/regx/UnicodeRangeFactory.cpp",
        "xerces-c/src/xercesc/util/regx/UnionToken.cpp",
        "xerces-c/src/xercesc/util/regx/XMLRangeFactory.cpp",
        "xerces-c/src/xercesc/util/regx/XMLUniCharacter.cpp",
        "xerces-c/src/xercesc/util/StringPool.cpp",
        "xerces-c/src/xercesc/util/SynchronizedStringPool.cpp",
        "xerces-c/src/xercesc/util/TransService.cpp",
        "xerces-c/src/xercesc/util/XMemory.cpp",
        "xerces-c/src/xercesc/util/XML256TableTranscoder.cpp",
        "xerces-c/src/xercesc/util/XML88591Transcoder.cpp",
        "xerces-c/src/xercesc/util/XMLAbstractDoubleFloat.cpp",
        "xerces-c/src/xercesc/util/XMLASCIITranscoder.cpp",
        "xerces-c/src/xercesc/util/XMLBigDecimal.cpp",
        "xerces-c/src/xercesc/util/XMLBigInteger.cpp",
        "xerces-c/src/xercesc/util/XMLChar.cpp",
        "xerces-c/src/xercesc/util/XMLChTranscoder.cpp",
        "xerces-c/src/xercesc/util/XMLDateTime.cpp",
        "xerces-c/src/xercesc/util/XMLDouble.cpp",
        "xerces-c/src/xercesc/util/XMLEBCDICTranscoder.cpp",
        "xerces-c/src/xercesc/util/XMLException.cpp",
        "xerces-c/src/xercesc/util/XMLFloat.cpp",
        "xerces-c/src/xercesc/util/XMLIBM1047Transcoder.cpp",
        "xerces-c/src/xercesc/util/XMLIBM1140Transcoder.cpp",
        "xerces-c/src/xercesc/util/XMLInitializer.cpp",
        "xerces-c/src/xercesc/util/XMLMsgLoader.cpp",
        "xerces-c/src/xercesc/util/XMLNumber.cpp",
        "xerces-c/src/xercesc/util/XMLString.cpp",
        "xerces-c/src/xercesc/util/XMLStringTokenizer.cpp",
        "xerces-c/src/xercesc/util/XMLUCS4Transcoder.cpp",
        "xerces-c/src/xercesc/util/XMLUni.cpp",
        "xerces-c/src/xercesc/util/XMLUri.cpp",
        "xerces-c/src/xercesc/util/XMLURL.cpp",
        "xerces-c/src/xercesc/util/XMLUTF16Transcoder.cpp",
        "xerces-c/src/xercesc/util/XMLUTF8Transcoder.cpp",
        "xerces-c/src/xercesc/util/XMLWin1252Transcoder.cpp",
        "xerces-c/src/xercesc/dom/DOMException.cpp",
        "xerces-c/src/xercesc/dom/DOMLSException.cpp",
        "xerces-c/src/xercesc/dom/DOMRangeException.cpp",
        "xerces-c/src/xercesc/dom/DOMXPathException.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMAttrImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMAttrMapImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMAttrNSImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMCDATASectionImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMCharacterDataImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMChildNode.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMCommentImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMConfigurationImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMDeepNodeListImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMDocumentFragmentImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMDocumentImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMDocumentTypeImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMElementImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMElementNSImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMEntityImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMEntityReferenceImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMErrorImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMImplementationImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMImplementationListImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMImplementationRegistry.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMLocatorImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMNamedNodeMapImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMNodeIDMap.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMNodeImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMNodeIteratorImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMNodeListImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMNodeVector.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMNormalizer.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMNotationImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMParentNode.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMProcessingInstructionImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMRangeImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMStringListImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMStringPool.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMTextImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMTreeWalkerImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMTypeInfoImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMLSSerializerImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMLSInputImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMLSOutputImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMXPathExpressionImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMXPathNSResolverImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/DOMXPathResultImpl.cpp",
        "xerces-c/src/xercesc/dom/impl/XSDElementNSImpl.cpp",
        "xerces-c/src/xercesc/framework/BinOutputStream.cpp",
        "xerces-c/src/xercesc/framework/LocalFileFormatTarget.cpp",
        "xerces-c/src/xercesc/framework/LocalFileInputSource.cpp",
        "xerces-c/src/xercesc/framework/MemBufFormatTarget.cpp",
        "xerces-c/src/xercesc/framework/MemBufInputSource.cpp",
        "xerces-c/src/xercesc/framework/psvi/PSVIAttribute.cpp",
        "xerces-c/src/xercesc/framework/psvi/PSVIAttributeList.cpp",
        "xerces-c/src/xercesc/framework/psvi/PSVIElement.cpp",
        "xerces-c/src/xercesc/framework/psvi/PSVIItem.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSAnnotation.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSAttributeDeclaration.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSAttributeGroupDefinition.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSAttributeUse.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSComplexTypeDefinition.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSElementDeclaration.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSFacet.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSIDCDefinition.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSModel.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSModelGroup.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSModelGroupDefinition.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSMultiValueFacet.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSNamespaceItem.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSNotationDeclaration.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSObject.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSParticle.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSSimpleTypeDefinition.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSTypeDefinition.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSValue.cpp",
        "xerces-c/src/xercesc/framework/psvi/XSWildcard.cpp",
        "xerces-c/src/xercesc/framework/StdInInputSource.cpp",
        "xerces-c/src/xercesc/framework/StdOutFormatTarget.cpp",
        "xerces-c/src/xercesc/framework/URLInputSource.cpp",
        "xerces-c/src/xercesc/framework/Wrapper4DOMLSInput.cpp",
        "xerces-c/src/xercesc/framework/Wrapper4InputSource.cpp",
        "xerces-c/src/xercesc/framework/XMLAttDef.cpp",
        "xerces-c/src/xercesc/framework/XMLAttDefList.cpp",
        "xerces-c/src/xercesc/framework/XMLAttr.cpp",
        "xerces-c/src/xercesc/framework/XMLBuffer.cpp",
        "xerces-c/src/xercesc/framework/XMLBufferMgr.cpp",
        "xerces-c/src/xercesc/framework/XMLContentModel.cpp",
        "xerces-c/src/xercesc/framework/XMLDTDDescription.cpp",
        "xerces-c/src/xercesc/framework/XMLElementDecl.cpp",
        "xerces-c/src/xercesc/framework/XMLEntityDecl.cpp",
        "xerces-c/src/xercesc/framework/XMLFormatter.cpp",
        "xerces-c/src/xercesc/framework/XMLGrammarDescription.cpp",
        "xerces-c/src/xercesc/framework/XMLGrammarPoolImpl.cpp",
        "xerces-c/src/xercesc/framework/XMLNotationDecl.cpp",
        "xerces-c/src/xercesc/framework/XMLRecognizer.cpp",
        "xerces-c/src/xercesc/framework/XMLRefInfo.cpp",
        "xerces-c/src/xercesc/framework/XMLSchemaDescription.cpp",
        "xerces-c/src/xercesc/framework/XMLValidator.cpp",
        "xerces-c/src/xercesc/internal/BinFileOutputStream.cpp",
        "xerces-c/src/xercesc/internal/BinMemOutputStream.cpp",
        "xerces-c/src/xercesc/internal/DGXMLScanner.cpp",
        "xerces-c/src/xercesc/internal/ElemStack.cpp",
        "xerces-c/src/xercesc/internal/IGXMLScanner.cpp",
        "xerces-c/src/xercesc/internal/IGXMLScanner2.cpp",
        "xerces-c/src/xercesc/internal/MemoryManagerImpl.cpp",
        "xerces-c/src/xercesc/internal/ReaderMgr.cpp",
        "xerces-c/src/xercesc/internal/SGXMLScanner.cpp",
        "xerces-c/src/xercesc/internal/ValidationContextImpl.cpp",
        "xerces-c/src/xercesc/internal/VecAttributesImpl.cpp",
        "xerces-c/src/xercesc/internal/VecAttrListImpl.cpp",
        "xerces-c/src/xercesc/internal/WFXMLScanner.cpp",
        "xerces-c/src/xercesc/internal/XMLReader.cpp",
        "xerces-c/src/xercesc/internal/XMLScanner.cpp",
        "xerces-c/src/xercesc/internal/XMLScannerResolver.cpp",
        "xerces-c/src/xercesc/internal/XProtoType.cpp",
        "xerces-c/src/xercesc/internal/XSAXMLScanner.cpp",
        "xerces-c/src/xercesc/internal/XSerializeEngine.cpp",
        "xerces-c/src/xercesc/internal/XSObjectFactory.cpp",
        "xerces-c/src/xercesc/internal/XTemplateSerializer.cpp",
        "xerces-c/src/xercesc/parsers/AbstractDOMParser.cpp",
        "xerces-c/src/xercesc/parsers/DOMLSParserImpl.cpp",
        "xerces-c/src/xercesc/parsers/SAX2XMLFilterImpl.cpp",
        "xerces-c/src/xercesc/parsers/SAX2XMLReaderImpl.cpp",
        "xerces-c/src/xercesc/parsers/SAXParser.cpp",
        "xerces-c/src/xercesc/parsers/XercesDOMParser.cpp",
        "xerces-c/src/xercesc/sax/Dummy.cpp",
        "xerces-c/src/xercesc/sax/InputSource.cpp",
        "xerces-c/src/xercesc/sax/SAXException.cpp",
        "xerces-c/src/xercesc/sax/SAXParseException.cpp",
        "xerces-c/src/xercesc/sax2/sax2Dummy.cpp",
        "xerces-c/src/xercesc/validators/common/AllContentModel.cpp",
        "xerces-c/src/xercesc/validators/common/CMAny.cpp",
        "xerces-c/src/xercesc/validators/common/CMBinaryOp.cpp",
        "xerces-c/src/xercesc/validators/common/CMUnaryOp.cpp",
        "xerces-c/src/xercesc/validators/common/ContentLeafNameTypeVector.cpp",
        "xerces-c/src/xercesc/validators/common/ContentSpecNode.cpp",
        "xerces-c/src/xercesc/validators/common/DFAContentModel.cpp",
        "xerces-c/src/xercesc/validators/common/Grammar.cpp",
        "xerces-c/src/xercesc/validators/common/GrammarResolver.cpp",
        "xerces-c/src/xercesc/validators/common/MixedContentModel.cpp",
        "xerces-c/src/xercesc/validators/common/SimpleContentModel.cpp",
        "xerces-c/src/xercesc/validators/datatype/AbstractNumericFacetValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/AbstractNumericValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/AbstractStringValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/AnySimpleTypeDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/AnyURIDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/Base64BinaryDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/BooleanDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/DatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/DatatypeValidatorFactory.cpp",
        "xerces-c/src/xercesc/validators/datatype/DateDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/DateTimeDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/DateTimeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/DayDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/DecimalDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/DoubleDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/DurationDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/ENTITYDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/FloatDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/HexBinaryDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/IDDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/IDREFDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/ListDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/MonthDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/MonthDayDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/NameDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/NCNameDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/NOTATIONDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/QNameDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/StringDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/TimeDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/UnionDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/XMLCanRepGroup.cpp",
        "xerces-c/src/xercesc/validators/datatype/YearDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/datatype/YearMonthDatatypeValidator.cpp",
        "xerces-c/src/xercesc/validators/DTD/DTDAttDef.cpp",
        "xerces-c/src/xercesc/validators/DTD/DTDAttDefList.cpp",
        "xerces-c/src/xercesc/validators/DTD/DTDElementDecl.cpp",
        "xerces-c/src/xercesc/validators/DTD/DTDEntityDecl.cpp",
        "xerces-c/src/xercesc/validators/DTD/DTDGrammar.cpp",
        "xerces-c/src/xercesc/validators/DTD/DTDScanner.cpp",
        "xerces-c/src/xercesc/validators/DTD/DTDValidator.cpp",
        "xerces-c/src/xercesc/validators/DTD/XMLDTDDescriptionImpl.cpp",
        "xerces-c/src/xercesc/validators/schema/ComplexTypeInfo.cpp",
        "xerces-c/src/xercesc/validators/schema/GeneralAttributeCheck.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/FieldActivator.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/FieldValueMap.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/IC_Field.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/IC_Key.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/IC_KeyRef.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/IC_Selector.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/IC_Unique.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/IdentityConstraint.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/IdentityConstraintHandler.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/ValueStore.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/ValueStoreCache.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/XercesXPath.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/XPathMatcher.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/XPathMatcherStack.cpp",
        "xerces-c/src/xercesc/validators/schema/identity/XPathSymbols.cpp",
        "xerces-c/src/xercesc/validators/schema/NamespaceScope.cpp",
        "xerces-c/src/xercesc/validators/schema/SchemaAttDef.cpp",
        "xerces-c/src/xercesc/validators/schema/SchemaAttDefList.cpp",
        "xerces-c/src/xercesc/validators/schema/SchemaElementDecl.cpp",
        "xerces-c/src/xercesc/validators/schema/SchemaGrammar.cpp",
        "xerces-c/src/xercesc/validators/schema/SchemaInfo.cpp",
        "xerces-c/src/xercesc/validators/schema/SchemaSymbols.cpp",
        "xerces-c/src/xercesc/validators/schema/SchemaValidator.cpp",
        "xerces-c/src/xercesc/validators/schema/SubstitutionGroupComparator.cpp",
        "xerces-c/src/xercesc/validators/schema/TraverseSchema.cpp",
        "xerces-c/src/xercesc/validators/schema/XercesAttGroupInfo.cpp",
        "xerces-c/src/xercesc/validators/schema/XercesElementWildcard.cpp",
        "xerces-c/src/xercesc/validators/schema/XercesGroupInfo.cpp",
        "xerces-c/src/xercesc/validators/schema/XMLSchemaDescriptionImpl.cpp",
        "xerces-c/src/xercesc/validators/schema/XSDDOMParser.cpp",
        "xerces-c/src/xercesc/validators/schema/XSDErrorReporter.cpp",
        "xerces-c/src/xercesc/validators/schema/XSDLocator.cpp",
        "xerces-c/src/xercesc/validators/schema/XUtil.cpp",
        "xerces-c/src/xercesc/xinclude/XIncludeDOMDocumentProcessor.cpp",
        "xerces-c/src/xercesc/xinclude/XIncludeLocation.cpp",
        "xerces-c/src/xercesc/xinclude/XIncludeUtils.cpp",
        "xerces-c/src/stricmp.c",
        "xerces-c/src/strnicmp.c",
        "xerces-c/src/xercesc/util/NetAccessors/Socket/SocketNetAccessor.cpp",
        "xerces-c/src/xercesc/util/NetAccessors/Socket/UnixHTTPURLInputStream.cpp",
        "xerces-c/src/xercesc/util/NetAccessors/BinHTTPInputStreamCommon.cpp",
        "xerces-c/src/xercesc/util/Transcoders/ICU/ICUTransService.cpp",
        "xerces-c/src/xercesc/util/MsgLoaders/InMemory/InMemMsgLoader.cpp",
        "xerces-c/src/xercesc/util/MutexManagers/StdMutexMgr.cpp",
        "xerces-c/src/xercesc/util/FileManagers/PosixFileMgr.cpp",
    ]

    configs = [ 
        ":libxerces-c_config"
    ]
    
    deps = [
        "//third_party/icu/icu4c:shared_icuuc",
    ]
    
    part_name = "xerces"
}

config("samples_config") {
    cflags_cc = [
        "-frtti",
        "-fexceptions",
        "-Wno-header-hygiene",
        "-DHAVE_CONFIG_H=1",
        "-D_FILE_OFFSET_BITS=64",
        "-D_THREAD_SAFE=1",
        "-Wno-long-long",
        "-Wno-variadic-macros",
        "-Wno-misleading-indentation",
        "-Wno-main",
        "-Wno-unused-variable",
    ]

    include_dirs = [
        "xerces-c/samples",
        "xerces-c/src",
        "xerces-c/samples/src",
        "adapted",
    ]
}

ohos_executable("StdInParse") {
    sources= [
        "xerces-c/samples/src/StdInParse/StdInParse.cpp",
        "xerces-c/samples/src/StdInParse/StdInParseHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("SEnumVal") {
    sources= [
        "xerces-c/samples/src/SEnumVal/SEnumVal.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("CreateDOMDocument") {
    sources= [
        "xerces-c/samples/src/CreateDOMDocument/CreateDOMDocument.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("SCMPrint") {
    sources= [
        "xerces-c/samples/src/SCMPrint/SCMPrint.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c"
    ]

    part_name = "xerces"
}

ohos_executable("XInclude") {
    sources= [
        "xerces-c/samples/src/XInclude/XInclude.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("DOMCount") {
    sources= [
        "xerces-c/samples/src/DOMCount/DOMCount.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("EnumVal") {
    sources= [
        "xerces-c/samples/src/EnumVal/EnumVal.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("PSVIWriter") {
    sources= [
        "xerces-c/samples/src/PSVIWriter/PSVIWriter.cpp",
        "xerces-c/samples/src/PSVIWriter/PSVIWriterHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("MemParse") {
    sources= [
        "xerces-c/samples/src/MemParse/MemParse.cpp",
        "xerces-c/samples/src/MemParse/MemParseHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("DOMPrint") {
    sources= [
        "xerces-c/samples/src/DOMPrint/DOMPrint.cpp",
        "xerces-c/samples/src/DOMPrint/DOMPrintErrorHandler.cpp",
        "xerces-c/samples/src/DOMPrint/DOMPrintFilter.cpp",
        "xerces-c/samples/src/DOMPrint/DOMTreeErrorReporter.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("Redirect") {
    sources= [
        "xerces-c/samples/src/Redirect/Redirect.cpp",
        "xerces-c/samples/src/Redirect/RedirectHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("PParse") {
    sources= [
        "xerces-c/samples/src/PParse/PParse.cpp",
        "xerces-c/samples/src/PParse/PParseHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("SAXCount") {
    sources= [
        "xerces-c/samples/src/SAXCount/SAXCount.cpp",
        "xerces-c/samples/src/SAXCount/SAXCountHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("SAX2Print") {
    sources= [
        "xerces-c/samples/src/SAX2Print/SAX2FilterHandlers.cpp",
        "xerces-c/samples/src/SAX2Print/SAX2Print.cpp",
        "xerces-c/samples/src/SAX2Print/SAX2PrintHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("SAX2Count") {
    sources= [
        "xerces-c/samples/src/SAX2Count/SAX2Count.cpp",
        "xerces-c/samples/src/SAX2Count/SAX2CountHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("SAXPrint") {
    sources= [
        "xerces-c/samples/src/SAXPrint/SAXPrint.cpp",
        "xerces-c/samples/src/SAXPrint/SAXPrintHandlers.cpp",
    ]

    configs = [ 
        ":samples_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

config("tests_config") {
    cflags_cc = [
        "-frtti",
        "-fexceptions",
        "-Wno-header-hygiene",
        "-DHAVE_CONFIG_H=1",
        "-D_FILE_OFFSET_BITS=64",
        "-D_THREAD_SAFE=1",
        "-Wno-implicitly-unsigned-literal",
        "-Wno-variadic-macros",
        "-fstrict-aliasing",
        "-Wno-misleading-indentation",
        "-Wno-main",
        "-Wno-unused-variable",
    ]

    include_dirs = [
        "xerces-c/tests",
        "xerces-c/tests/src",
        "xerces-c/src",
        "xerces-c",
        "adapted",
    ]
}

ohos_executable("XSValueTest") {
    sources= [
        "xerces-c/tests/src/XSValueTest/XSValueTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("DOMTest") {
    sources= [
        "xerces-c/tests/src/DOM/DOMTest/DTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("ThreadTest") {
    sources= [
        "xerces-c/tests/src/ThreadTest/ThreadTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("Char16Test") {
    sources= [
        "xerces-c/tests/src/Char16Test/Char16Test.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("DOMMemTest") {
    sources= [
        "xerces-c/tests/src/DOM/DOMMemTest/DOMMemTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("DOMTraversalTest") {
    sources= [
        "xerces-c/tests/src/DOM/Traversal/Traversal.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("XSerializerTest") {
    sources= [
        "xerces-c/tests/src/XSerializerTest/XSerializerHandlers.cpp",
        "xerces-c/tests/src/XSerializerTest/XSerializerTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("RangeTest") {
    sources= [
        "xerces-c/tests/src/DOM/RangeTest/RangeTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("InitTermTest") {
    sources= [
        "xerces-c/tests/src/InitTermTest/InitTermTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("DOMTypeInfoTest") {
    sources= [
        "xerces-c/tests/src/DOM/TypeInfo/TypeInfo.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("XSTSHarness") {
    sources= [
        "xerces-c/tests/src/XSTSHarness/XSTSHarness.cpp",
        "xerces-c/tests/src/XSTSHarness/XSTSHarnessHandlers.cpp",
        "xerces-c/tests/src/XSTSHarness/XMLHarnessHandlers.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("EncodingTest") {
    sources= [
        "xerces-c/tests/src/EncodingTest/EncodingTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("Normalizer") {
    sources= [
        "xerces-c/tests/src/DOM/Normalizer/Normalizer.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("MemHandlerTest") {
    sources= [
        "xerces-c/tests/src/MemHandlerTest/MemoryMonitor.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

ohos_executable("NetAccessorTest") {
    sources= [
        "xerces-c/tests/src/NetAccessorTest/NetAccessorTest.cpp",
    ]

    configs = [ 
        ":tests_config"
    ]
    deps = [
        ":libxerces-c",
    ]

    part_name = "xerces"
}

group("samples") {

if(enable_xerces_test) {
    deps = [
        ":StdInParse",
        ":SEnumVal",
        ":CreateDOMDocument",
        ":SCMPrint",
        ":XInclude",
        ":DOMCount",
        ":EnumVal",
        ":PSVIWriter",
        ":MemParse",
        ":DOMPrint",
        ":Redirect",
        ":PParse",
        ":SAXCount",
        ":SAX2Print",
        ":SAX2Count",
        ":SAXPrint",
    ]
} else {
   deps = []
}
}

group("tests") {

if(enable_xerces_test){
    deps = [
        ":XSValueTest",
        ":DOMTest",
        ":ThreadTest",
        ":Char16Test",
        ":DOMMemTest",
        ":DOMTraversalTest",
        ":XSerializerTest",
        ":RangeTest",
        ":InitTermTest",
        ":DOMTypeInfoTest",
        ":XSTSHarness",
        ":EncodingTest",
        ":Normalizer",
        ":MemHandlerTest",
        ":NetAccessorTest",
    ]
}else {
    deps = []
}
}
