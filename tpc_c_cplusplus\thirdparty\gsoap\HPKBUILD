# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>,alnin <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=gsoap
pkgver=2.8.134
pkgrel=0
pkgdesc="The gSOAP toolkit is an extensive suite of portable C and C++ software to develop XML Web services with powerful type-safe XML data bindings."
url="https://sourceforge.net/projects/gsoap2/files/"
archs=("armeabi-v7a" "arm64-v8a")
license=("GPLv2 License")
depends=("openssl" "zlib")
makedepends=()

autounpack=true
downloadpackage=true
buildtools="configure"

packagename="$pkgname"_$pkgver.zip
source="https://nchc.dl.sourceforge.net/project/gsoap2/$packagename"

packagedir="$pkgname"-"${pkgver:0:3}"
builddir="$pkgname"-$pkgver

linuxflag=true
opensslpackagename=openssl-OpenSSL_1_1_1u
zlibpackagename=zlib-v1.2.13

source envset.sh
host=
prepare() {
    if $linuxflag
    then
        #编译openssl
        mkdir -p linux/openssl
        cp -rf ${LYCIUM_ROOT}/../thirdparty/openssl/$opensslpackagename.zip .
        unzip $opensslpackagename.zip > /dev/null 2>&1
        cd $opensslpackagename
        mkdir build && cd build
        ../config no-shared --prefix=${LYCIUM_ROOT}/../thirdparty/gsoap/linux/openssl > $publicbuildlog 2>&1
        make VERBOSE=1 >> $publicbuildlog 2>&1
        if [ $? -ne 0 ]
        then
            return 1
        fi
        make install >> $publicbuildlog 2>&1
        cd ../../

        #编译zlib
        mkdir -p linux/zlib
        cp -rf ${LYCIUM_ROOT}/../thirdparty/zlib/$zlibpackagename.zip .
        unzip $zlibpackagename.zip > /dev/null 2>&1
        cd $zlibpackagename
        mkdir build && cd build
        ../configure --prefix=${LYCIUM_ROOT}/../thirdparty/gsoap/linux/zlib >> $publicbuildlog 2>&1
        make VERBOSE=1 >> $publicbuildlog 2>&1
        if [ $? -ne 0 ]
        then
            return 1
        fi
        make install >> $publicbuildlog 2>&1
        cd ../../

        #编译gsoap
        mkdir -p linux/gsoap
        cp -rf $packagedir $builddir-linux-build
        cd $builddir-linux-build
        ./configure --prefix=${LYCIUM_ROOT}/../thirdparty/gsoap/linux/gsoap \
                    --with-openssl=${LYCIUM_ROOT}/../thirdparty/gsoap/linux/openssl \
                    --with-zlib=${LYCIUM_ROOT}/../thirdparty/gsoap/linux/zlib >> $publicbuildlog 2>&1
        make MAKE=make VERBOSE=1 >> $publicbuildlog 2>&1
        if [ $? -ne 0 ]
        then
            return 1
        fi
        make install >> $publicbuildlog 2>&1
        cd ../

        linuxflag=false
    fi

    cp -rf $packagedir $builddir-$ARCH-build
    cd $builddir-$ARCH-build
    patch -p1 < `pwd`/../gsoap_oh_pkg.patch >> $publicbuildlog 2>&1

    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi

    cd $OLDPWD
}

build() {
    cd $builddir-$ARCH-build
    PKG_CONFIG_LIBDIR="${pkgconfigpath}" LDFLAGS="-L${LYCIUM_ROOT}/usr/openssl/${ARCH}/lib -lssl -lcrypto -lz -lpthread" \
        CFLAGS="$CFLAGS -I${LYCIUM_ROOT}/usr/openssl/${ARCH}/include" \
        CXXFLAGS="$CXXFLAGS -I${LYCIUM_ROOT}/usr/openssl/${ARCH}/include" \
        ./configure "$@" --host=$host --disable-c-locale --enable-samples \
                    --with-openssl="${LYCIUM_ROOT}/usr/openssl/${ARCH}" \
                    --with-zlib="${LYCIUM_ROOT}/usr/zlib/${ARCH}" > $buildlog 2>&1

    make MAKE=make VERBOSE=1 >> $buildlog 2>&1
    if [ $? -eq 0 ]
    then
        cd $OLDPWD
        return 0
    fi

    #替换soapcpp2执行文件
    mv ./gsoap/src/soapcpp2 ./gsoap/src/soapcpp2.bak
    cp -f ${LYCIUM_ROOT}/../thirdparty/gsoap/linux/gsoap/bin/soapcpp2 ./gsoap/src/soapcpp2
    make MAKE=make VERBOSE=1 >> $buildlog 2>&1
    if [ $? -eq 0 ]
    then
        cd $OLDPWD
        return 0
    fi

    #替换wsdl2h执行文件
    mv ./gsoap/wsdl/wsdl2h ./gsoap/wsdl/wsdl2h.bak
    cp -f ${LYCIUM_ROOT}/../thirdparty/gsoap/linux/gsoap/bin/wsdl2h ./gsoap/wsdl/wsdl2h
    make MAKE=make VERBOSE=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    make install >> $buildlog 2>&1

    rm -f ./gsoap/src/soapcpp2
    mv ./gsoap/src/soapcpp2.bak ./gsoap/src/soapcpp2
    cp -f ./gsoap/src/soapcpp2 ${LYCIUM_ROOT}/usr/gsoap/${ARCH}/bin
    rm -f ./gsoap/wsdl/wsdl2h
    mv ./gsoap/wsdl/wsdl2h.bak ./gsoap/wsdl/wsdl2h
    cp -f ./gsoap/wsdl/wsdl2h ${LYCIUM_ROOT}/usr/gsoap/${ARCH}/bin

    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    unset host
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 进入编译目录，执行 ctest
}

# 清理环境
cleanbuild() {
    echo "The cleanbuild !"
    rm -rf ${PWD}/$opensslpackagename ${PWD}/$zlibpackagename ${PWD}/$packagedir ${PWD}/$builddir-linux-build \
        ${PWD}/$builddir-${archs[0]}-build ${PWD}/$builddir-${archs[1]}-build #${PWD}/$packagename
}
