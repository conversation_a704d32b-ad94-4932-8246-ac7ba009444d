# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, zhengxiaoqing <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=acl
pkgver=v2.1.0
pkgrel=0
pkgdesc="Animation Compression Library"
url="https://github.com/nfrechette/acl"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT license")
depends=()
makedepends=()
source="https://github.com/nfrechette/$pkgname.git"

downloadpackage=false
autounpack=false
builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

patchflag=true
cloneFlag=true

prepare() { 
    if $cloneFlag
    then
        git clone -b $pkgver $source $builddir > $publicbuildlog 2>&1
        # TODO 退出码校验
        if [ $? -ne 0 ]
        then
            echo "$pkgname git clone $source error."
            return -1
        fi
        cd $builddir
        git submodule update --init --recursive >> $publicbuildlog 2>&1
        if [ $? -ne 0 ]
        then
            echo "$pkgname git submodule update error."
            return -2
        fi
        cloneFlag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build

    if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../acl_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DCMAKE_CXX_FLAGS="-Wno-unused-command-line-argument" \
    -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD

    # 需要手动拷贝头文件
    cd $builddir/$ARCH-build
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp -r ../includes/acl/ $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp -r ../external/rtm/includes/rtm/ $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # 测试方式
    # 进入构建目录
    # 执行: ./tests/main_generic/acl_unit_tests
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}