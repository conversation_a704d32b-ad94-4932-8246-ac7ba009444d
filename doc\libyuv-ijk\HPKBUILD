# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=yuv
pkgver=ijk-r0.2.1-dev
pkgrel=0
pkgdesc="libyuv is an open source project that includes YUV conversion and scaling functionality."
url="https://github.com/bilibili/libyuv/branches"
archs=("armeabi-v7a" "arm64-v8a" "x86_64")
license=("BSD 3-Clause "New" or "Revised" License")
depends=()
makedepends=()
install=
source="https://codeload.github.com/bilibili/libyuv/zip/refs/heads/$pkgver"
autounpack=ture
downloadpackage=ture
builddir=$pkgname
packagename=$builddir-$pkgver.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
    cp -rf ./lib$builddir-$pkgver/* ./$builddir/
    cp CMakeLists.txt $builddir/
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L
    ${MAKE} -C $ARCH-build
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib
    cp -rf include/* $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp -rf $ARCH-build/*.a $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    echo "Test MUST on OpenHarmony device!"
}

cleanbuild(){
    rm -rf ${PWD}/$builddir ${PWD}/lib$builddir-$pkgver ${PWD}/$pkgname
}
