# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=pvmp3dec
pkgver=36ec11a
pkgrel=0
pkgdesc="PacketVideo MP3 Decoder Library"
url="https://github.com/N22E114/$pkgname"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache-2.0 license")
depends=()
makedepends=()
source="https://github.com/N22E114/$pkgname/archive/refs/tags/$pkgver.tar.gz"
buildtools="cmake"

autounpack=true
downloadpackage=true
patchflag=true

builddir=${pkgname}-${pkgver}
packagename=${pkgname}-${pkgver}.tar.gz

prepare() {
    if $patchflag
    then
        cd $builddir
        patch -p1 < `pwd`/../pvmp3dec_oh_pkg.patch
        patchflag=false
        cd $OLDPWD
    fi

    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    # 禁用注释检测、未使用的命令参数检测
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DCMAKE_CXX_FLAGS="-DOSCL_UNUSED_ARG=" \
        -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L  > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    cp oh_hello_test.mp3 $builddir/$ARCH-build
    echo "The test must be on an OpenHarmony device!"
    # cd $builddir/$ARCH-build
    # ./mp3_test oh_hello_test.mp3 output.pcm
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir
}
