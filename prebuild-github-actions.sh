#!/bin/bash

# GitHub Actions 专用的 ijkplayer 依赖库编译脚本
# 基于原始 prebuild.sh，但适配 GitHub Actions 环境

set -e  # 遇到错误立即退出

# 执行该脚本时需进入到脚本所在目录
ROOT_DIR=$(pwd)
API_VERSION=11
SDK_DIR=/opt/ohos-sdk-4.1/ohos-sdk/linux/11                              # GitHub Actions 中的 SDK 路径
LYCIUM_TOOLS_URL=https://gitee.com/openharmony-sig/tpc_c_cplusplus.git
LYCIUM_ROOT_DIR=$ROOT_DIR/tpc_c_cplusplus
LYCIUM_TOOLS_DIR=$LYCIUM_ROOT_DIR/lycium
LYCIUM_THIRDPARTY_DIR=$LYCIUM_ROOT_DIR/thirdparty
DEPENDS_DIR=$ROOT_DIR/doc
FFMPEG_NAME=FFmpeg-ff4.0
LIBYUV_NAME=libyuv-ijk
SOUNDTOUCH_NAME=soundtouch-ijk
OPESSL_NAME=openssl_1_1_1w

CI_OUTPUT_DIR=$ROOT_DIR/../out/tpc/

LIBS_NAME=("FFmpeg-ff4.0" "libyuv-ijk" "soundtouch-ijk" "openssl_1_1_1w")
PACKAGE_NAME=("FFmpeg-ff4.0-ijk0.8.8-20210426-001.tar.gz" "yuv-ijk-r0.2.1-dev.zip" "soundtouch-ijk-r0.1.2-dev.zip" "openssl-OpenSSL_1_1_1w.zip")

function prepare_lycium()
{
    if [ -d $LYCIUM_ROOT_DIR ]
    then
        rm -rf $LYCIUM_ROOT_DIR
    fi

    # 检查是否已存在tpc_c_cplusplus目录，避免重复克隆覆盖用户下载的源码包
    if [ ! -d "tpc_c_cplusplus" ]; then
        echo "正在下载lycium工具链..."
        git clone $LYCIUM_TOOLS_URL -b support_x86 --depth=1
    else
        echo "lycium工具链已存在，跳过下载（保护用户的源码包）"
    fi
    if [ $? -ne 0 ]
    then
        return 1
    fi

    cd $LYCIUM_TOOLS_DIR/Buildtools
    tar -zxvf toolchain.tar.gz
    if [ $? -ne 0 ]
    then
        echo "unpack sdk toolchain failed!!"
        cd $OLDPWD
        return 1
    fi

    cp toolchain/* $SDK_DIR/native/llvm/bin/

    # 跳过工具安装，GitHub Actions 环境已预装所有必要工具
    echo "跳过工具安装步骤（GitHub Actions 环境已预装）"
    
    cd $OLDPWD
    return 0
}

function copy_depends()
{
    local dir=$1
    local name=$2

    if [ -d $LYCIUM_THIRDPARTY_DIR/$name ]
    then
        rm -rf $LYCIUM_THIRDPARTY_DIR/$name
    fi
    cp -arf $dir/$name $LYCIUM_THIRDPARTY_DIR/
}

function prepare_depends()
{
    copy_depends $DEPENDS_DIR $LIBYUV_NAME
    copy_depends $DEPENDS_DIR $SOUNDTOUCH_NAME
}

function check_sdk()
{
    if [ ! -d $SDK_DIR ]
    then
        return 1
    fi

    export OHOS_SDK=$SDK_DIR
    return 0
}

function check_copy_shasum()
{
    local libpath=$1
    local pack_name=$2
    local libname=$3

    cd $LYCIUM_THIRDPARTY_DIR/$libpath
    if [ ! -f ./SHA512SUM ]
    then
        sha512sum $pack_name > ./SHA512SUM
    fi
    cp ./SHA512SUM $LYCIUM_TOOLS_DIR/usr/$libname/

    cd $OLDPWD
}

function install_shasum()
{
    check_copy_shasum $FFMPEG_NAME ${PACKAGE_NAME[0]} $FFMPEG_NAME
    check_copy_shasum $LIBYUV_NAME ${PACKAGE_NAME[1]} yuv
    check_copy_shasum $SOUNDTOUCH_NAME ${PACKAGE_NAME[2]} soundtouch
    check_copy_shasum $OPESSL_NAME ${PACKAGE_NAME[3]} $OPESSL_NAME
}

function start_build()
{
    local result=0
    cd $LYCIUM_TOOLS_DIR
    if [ $? -ne 0 ]
    then
        return 1
    fi

    bash build.sh $FFMPEG_NAME $LIBYUV_NAME $SOUNDTOUCH_NAME
    result=$?
    cd $OLDPWD
    return $result
}

function install_depends()
{
    local install_dir=$ROOT_DIR/ijkplayer/src/main/cpp/third_party/
    
    # 确保目标目录存在
    mkdir -p "$install_dir/ffmpeg"
    mkdir -p "$install_dir/yuv"
    mkdir -p "$install_dir/openssl"
    mkdir -p "$install_dir/soundtouch"
    
    cp -arf $LYCIUM_TOOLS_DIR/usr/$FFMPEG_NAME $install_dir/ffmpeg/ffmpeg
    if [ $? -ne 0 ]
    then
        echo "FFmpeg build failed!"
        return 1
    fi
    cp -arf $LYCIUM_TOOLS_DIR/usr/yuv $install_dir/yuv
    if [ $? -ne 0 ]
    then
        echo "yuv build failed!"
        return 1
    fi
    cp -arf $LYCIUM_TOOLS_DIR/usr/$OPESSL_NAME $install_dir/openssl
    if [ $? -ne 0 ]
    then
        echo "FFmpeg depends openssl build failed!"
        return 1
    fi
    cp -arf $LYCIUM_TOOLS_DIR/usr/soundtouch $install_dir/soundtouch
    if [ $? -ne 0 ]
    then
        echo "soundtouch build failed!"
        return 1
    fi

    if [ -d $CI_OUTPUT_DIR ]
    then
        cp -arf $LYCIUM_TOOLS_DIR/usr/$FFMPEG_NAME $CI_OUTPUT_DIR
        cp -arf $LYCIUM_TOOLS_DIR/usr/yuv $CI_OUTPUT_DIR
        cp -arf $LYCIUM_TOOLS_DIR/usr/$OPESSL_NAME $CI_OUTPUT_DIR
        cp -arf $LYCIUM_TOOLS_DIR/usr/soundtouch $CI_OUTPUT_DIR
    fi

    return 0
}

function prebuild()
{
    echo "=== GitHub Actions 环境下的 ijkplayer 依赖库编译 ==="
    
    check_sdk
    if [ $? -ne 0 ]
    then
        echo "ERROR: check_sdk failed!!!"
        return 1
    fi
    
    prepare_lycium
    if [ $? -ne 0 ]
    then
        echo "ERROR: prepare_lycium failed!!!"
        return 1
    fi
    
    prepare_depends
    
    start_build
    if [ $? -ne 0 ]
    then
        echo "ERROR: start_build failed!!!"
        return 1
    fi
    
    install_shasum
    
    install_depends
    if [ $? -ne 0 ]
    then
        echo "ERROR: install depends failed!!!"
        return 1
    fi
    
    echo "GitHub Actions 环境下的编译完成!!"
    return 0
}

prebuild $*
ret=$?
echo "ret = $ret"
exit $ret

#EOF
