#!/bin/bash

# OpenHarmony SDK 设置脚本
# 用于在服务器上配置SDK环境

set -e

ROOT_DIR=$(pwd)
API_VERSION=11
SDK_BASE_DIR="$ROOT_DIR/../ohos-sdk-$API_VERSION"
SDK_DIR="$SDK_BASE_DIR/linux/$API_VERSION"

echo "========================================"
echo "OpenHarmony SDK 设置"
echo "========================================"
echo "项目根目录: $ROOT_DIR"
echo "SDK目标目录: $SDK_DIR"

# 检查是否已存在SDK
if [ -d "$SDK_DIR" ]; then
    echo "✓ SDK目录已存在: $SDK_DIR"
    
    # 检查SDK内容
    if [ -d "$SDK_DIR/native" ]; then
        echo "✓ 检测到native工具链"
    else
        echo "✗ 警告: 未找到native工具链目录"
    fi
    
    if [ -d "$SDK_DIR/toolchains" ]; then
        echo "✓ 检测到toolchains"
    else
        echo "✗ 警告: 未找到toolchains目录"
    fi
    
    echo ""
    echo "SDK目录内容："
    ls -la "$SDK_DIR" 2>/dev/null || echo "无法列出SDK目录内容"
    
else
    echo "✗ SDK目录不存在: $SDK_DIR"
    echo ""
    echo "请选择SDK获取方式："
    echo "1. 从本地上传SDK"
    echo "2. 从华为官网下载SDK"
    echo "3. 手动指定SDK路径"
    
    read -p "请选择 (1-3): " choice
    
    case $choice in
        1)
            echo ""
            echo "请使用以下命令从本地上传SDK到服务器："
            echo "scp -r \"D:\\harmonyFor\\openSDK\\11\" $USER@$(hostname):$ROOT_DIR/../ohos-sdk-11/linux/"
            echo ""
            echo "或者先压缩再上传："
            echo "# 在Windows PowerShell中压缩："
            echo "Compress-Archive -Path \"D:\\harmonyFor\\openSDK\\11\" -DestinationPath \"D:\\harmonyFor\\ohos-sdk-11.zip\""
            echo "# 上传压缩包："
            echo "scp \"D:\\harmonyFor\\ohos-sdk-11.zip\" $USER@$(hostname):$ROOT_DIR/../"
            echo "# 在服务器上解压："
            echo "unzip ../ohos-sdk-11.zip && mkdir -p ../ohos-sdk-11/linux/ && mv ../11 ../ohos-sdk-11/linux/"
            echo ""
            echo "上传完成后，请重新运行此脚本"
            exit 1
            ;;
        2)
            echo ""
            echo "请访问华为开发者官网下载SDK："
            echo "https://developer.harmonyos.com/cn/develop/deveco-studio"
            echo ""
            echo "下载后解压到: $SDK_BASE_DIR"
            echo "然后重新运行此脚本"
            exit 1
            ;;
        3)
            read -p "请输入SDK的完整路径: " custom_sdk_path
            if [ -d "$custom_sdk_path" ]; then
                echo "创建符号链接: $custom_sdk_path -> $SDK_DIR"
                mkdir -p "$(dirname "$SDK_DIR")"
                ln -sf "$custom_sdk_path" "$SDK_DIR"
            else
                echo "✗ 指定的路径不存在: $custom_sdk_path"
                exit 1
            fi
            ;;
        *)
            echo "无效选择"
            exit 1
            ;;
    esac
fi

# 设置环境变量
echo ""
echo "设置环境变量..."
export OHOS_SDK="$SDK_DIR"

# 检查关键工具
echo ""
echo "检查SDK工具..."

CLANG_PATH="$SDK_DIR/native/llvm/bin/clang"
if [ -f "$CLANG_PATH" ]; then
    echo "✓ 找到clang编译器: $CLANG_PATH"
    "$CLANG_PATH" --version | head -1
else
    echo "✗ 未找到clang编译器: $CLANG_PATH"
fi

# 更新prebuild.sh中的SDK路径
echo ""
echo "更新prebuild.sh中的SDK路径..."
if [ -f "prebuild.sh" ]; then
    # 备份原文件
    cp prebuild.sh prebuild.sh.backup
    
    # 更新SDK_DIR路径
    sed -i "s|SDK_DIR=.*|SDK_DIR=$SDK_DIR|g" prebuild.sh
    echo "✓ 已更新prebuild.sh中的SDK路径"
else
    echo "✗ 未找到prebuild.sh文件"
fi

echo ""
echo "========================================"
echo "SDK设置完成"
echo "========================================"
echo "SDK路径: $SDK_DIR"
echo "环境变量: OHOS_SDK=$OHOS_SDK"
echo ""
echo "现在可以运行编译脚本："
echo "./prebuild.sh"
