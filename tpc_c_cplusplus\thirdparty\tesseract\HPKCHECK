# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1    # 导入HPKBUILD文件
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

checkprepare() {
    # libc++_shared.so 的路径加入环境变量
    export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:`pwd`/$builddir/$ARCH-build
    export TESSDATA_PREFIX=`pwd`/$builddir/$ARCH-build/
    echo "$LD_LIBRARY_PATH" > ld.log
    chmod a+rw -R /tmp/
}

# 在OH环境执行测试的接口
openharmonycheck() {
    res=0
    cd $builddir/$ARCH-build/
    echo "Using the RK3568 development board, the test case test time takes about 4 hours" > ${logfile} 2>&1
    echo "start test times: `date`" >> ${logfile} 2>&1
    make check-TESTS >> ${logfile} 2>&1
    echo "end test times: `date`" >> ${logfile} 2>&1

    # 直接使用返回值不可靠，查找失败个数更靠谱
    res=$(awk -F':' '/^FAIL:/ {print $2}'  test-suite.log |wc -l)

    # 拷贝log,每个测试用例有独立的log文件
    if [ $res -ne 0 ];then
        mkdir -p ${LYCIUM_FAULT_PATH}/${pkgname}
        for test in $(awk -F':' '/^FAIL:/ {print $2}' test-suite.log)
        do 
            cp $test.log ${LYCIUM_FAULT_PATH}/${pkgname}/ 
        done
        return -1
    fi
    cd $OLDPWD
    return 0
}
