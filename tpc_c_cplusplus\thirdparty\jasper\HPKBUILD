
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=jasper
pkgver=version-4.0.0
pkgrel=0
pkgdesc=""
url=
archs=("armeabi-v7a" "arm64-v8a")
license=("jasper license files")
depends=("libjpeg-turbo")
makedepends=()

source="https://github.com/${pkgname}-software/${pkgname}/archive/refs/tags/${pkgver}.tar.gz"

builddir=${pkgname}-${pkgver}
packagename=${builddir}.tar.gz
autounpack=true
downloadpackage=true
buildtools="cmake"

prepare() {
    mkdir -p ${builddir}/${ARCH}-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=${ARCH} -DJAS_ENABLE_DOC=OFF -DJ<PERSON>_STDC_VERSION=199901L -B${ARCH}-build -S./ -L > `pwd`/${ARCH}-build/build.log 2>&1
    make -j4 -C ${ARCH}-build >> `pwd`/${ARCH}-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd ${builddir}
    make -C ${ARCH}-build install >> `pwd`/${ARCH}-build/build.log 2>&1
    cd ${OLDPWD}
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

cleanbuild() {
     rm -rf ${PWD}/${builddir} #${PWD}/$packagename
}
