# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=epeg
pkgver=v0.9.3
pkgrel=0
pkgdesc="It's a convenience library API to using libjpeg to load JPEG images destined to be turned into thumbnails of the original, saving information with these thumbnails, retreiving it and managing to load the image ready for scaling with the minimum of fuss and CPU overhead."
url="https://github.com/mattes/epeg"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD 3-Clause License")
depends=("jpeg" "libexif")
makedepends=()
source="https://github.com/mattes/$pkgname/archive/refs/tags/$pkgver.tar.gz"

downloadpackage=true
autounpack=true
buildtools="cmake"
builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" \
        -DOHOS_ARCH=$ARCH \
        -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE -C $ARCH-build  >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" \
        -DCMAKE_C_FLAGS="${CMAKE_C_FLAGS} -I${LYCIUM_ROOT}/usr/epeg/${ARCH}/include" \
        -DOHOS_ARCH=$ARCH \
        -DBUILD_PROGRAM=ON \
        -B$ARCH-build -S./ >> $buildlog 2>&1
    $MAKE -C $ARCH-build  >> $buildlog 2>&1
    cd $OLDPWD
    echo "The test must be on an OpenHarmony device!"
}

cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}