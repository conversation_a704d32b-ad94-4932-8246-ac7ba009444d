# Contributor: huang<PERSON><PERSON><PERSON> <<EMAIL>>
# Maintainer:  huang<PERSON>zhong <<EMAIL>>
pkgname=pugixml
pkgver=v1.13
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT")
depends=()
makedepends=()
install=
source="https://github.com/zeux/$pkgname/archive/refs/tags/$pkgver.tar.gz"

downloadpackage=true
autounpack=true

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DPUGIXML_BUILD_TESTS=ON -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd "$builddir"
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
