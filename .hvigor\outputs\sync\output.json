{"ohos-module-entry": {"SELECT_TARGET": "default", "MODULE_BUILD_DIR": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build", "TARGETS": {"default": {"SOURCE_ROOT": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main", "RESOURCES_PATH": ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\main\\resources"], "BUILD_PATH": {"OUTPUT_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\outputs\\default", "INTERMEDIA_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates", "JS_ASSETS_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader_out\\default", "JS_LITE_ASSETS_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader_out_lite\\default", "RES_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default", "RES_PROFILE_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "ETS_SUPER_VISUAL_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule", "JS_SUPER_VISUAL_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\cache\\default\\default@CompileJS\\jsbundle", "WORKER_LOADER": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "MANIFEST_JSON": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\manifest\\default", "OUTPUT_METADATA_JSON": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json"}, "BUILD_OPTION": {"debuggable": true}}, "ohosTest": {"SOURCE_ROOT": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\ohosTest", "RESOURCES_PATH": ["D:\\new\\ohos_ijkplayer-2.0.3\\entry\\src\\ohosTest\\resources"], "BUILD_PATH": {"OUTPUT_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\outputs\\ohosTest", "INTERMEDIA_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates", "JS_ASSETS_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader_out\\ohosTest", "JS_LITE_ASSETS_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader_out_lite\\ohosTest", "RES_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\ohosTest", "RES_PROFILE_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\res\\ohosTest\\resources\\base\\profile", "ETS_SUPER_VISUAL_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\cache\\ohosTest\\ohosTest@OhosTestCompileArkTS\\esmodule", "JS_SUPER_VISUAL_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\cache\\ohosTest\\ohosTest@OhosTestCompileJS\\jsbundle", "WORKER_LOADER": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\loader\\ohosTest\\loader.json", "MANIFEST_JSON": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\manifest\\ohosTest", "OUTPUT_METADATA_JSON": "D:\\new\\ohos_ijkplayer-2.0.3\\entry\\build\\default\\intermediates\\hap_metadata\\ohosTest\\output_metadata.json"}, "BUILD_OPTION": {"debuggable": true}}}}, "ohos-module-ijkplayer": {"SELECT_TARGET": "default", "MODULE_BUILD_DIR": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build", "TARGETS": {"default": {"SOURCE_ROOT": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main", "RESOURCES_PATH": ["D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\src\\main\\resources"], "BUILD_PATH": {"OUTPUT_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\outputs\\default", "INTERMEDIA_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates", "JS_ASSETS_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\loader_out\\default", "JS_LITE_ASSETS_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\loader_out_lite\\default", "RES_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\res\\default", "RES_PROFILE_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "ETS_SUPER_VISUAL_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule", "JS_SUPER_VISUAL_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\cache\\default\\default@CompileJS\\jsbundle", "WORKER_LOADER": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\loader\\default\\loader.json", "MANIFEST_JSON": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\manifest\\default", "OUTPUT_METADATA_JSON": "D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json"}, "BUILD_OPTION": {"debuggable": true}}}}, "ohos-project": {"SELECT_PRODUCT_NAME": "default", "MODULE_BUILD_DIR": "D:\\new\\ohos_ijkplayer-2.0.3\\build", "BUNDLE_NAME": "com.example.ijkplayer", "BUILD_PATH": {"OUTPUT_PATH": "D:\\new\\ohos_ijkplayer-2.0.3\\build\\outputs\\default"}}, "version": 1}