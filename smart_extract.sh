#!/bin/bash

# 智能解压脚本 - 自动识别文件名并解压

echo "========================================"
echo "智能解压脚本"
echo "========================================"

cd temp_downloads

echo "检查下载的文件..."
ls -la *.gz *.zip 2>/dev/null || echo "未找到压缩文件"

echo ""
echo "开始智能解压..."

# 解压FFmpeg
echo "=== 处理FFmpeg ==="
ffmpeg_file=$(ls *FFmpeg* 2>/dev/null | head -1)
if [ -n "$ffmpeg_file" ]; then
    echo "找到FFmpeg文件: $ffmpeg_file"
    if [[ "$ffmpeg_file" == *.tar.gz ]]; then
        tar -xzf "$ffmpeg_file"
        # 查找解压后的目录
        ffmpeg_dir=$(ls -d FFmpeg* 2>/dev/null | head -1)
        if [ -n "$ffmpeg_dir" ]; then
            mv "$ffmpeg_dir" ffmpeg_src
            echo "✓ FFmpeg解压完成"
        else
            echo "✗ FFmpeg解压失败"
        fi
    fi
else
    echo "⚠ 未找到FFmpeg文件"
fi

# 解压soundtouch
echo "=== 处理soundtouch ==="
soundtouch_file=$(ls *soundtouch* 2>/dev/null | head -1)
if [ -n "$soundtouch_file" ]; then
    echo "找到soundtouch文件: $soundtouch_file"
    if [[ "$soundtouch_file" == *.zip ]]; then
        unzip -q "$soundtouch_file"
        # 查找解压后的目录
        soundtouch_dir=$(ls -d soundtouch* 2>/dev/null | head -1)
        if [ -n "$soundtouch_dir" ]; then
            mv "$soundtouch_dir" soundtouch_src
            echo "✓ soundtouch解压完成"
        else
            echo "✗ soundtouch解压失败"
        fi
    fi
else
    echo "⚠ 未找到soundtouch文件"
fi

# 解压libyuv
echo "=== 处理libyuv ==="
libyuv_file=$(ls *libyuv* 2>/dev/null | head -1)
if [ -n "$libyuv_file" ]; then
    echo "找到libyuv文件: $libyuv_file"
    if [[ "$libyuv_file" == *.zip ]]; then
        unzip -q "$libyuv_file"
        # 查找解压后的目录
        libyuv_dir=$(ls -d libyuv* 2>/dev/null | head -1)
        if [ -n "$libyuv_dir" ]; then
            mv "$libyuv_dir" libyuv_src
            echo "✓ libyuv解压完成"
        else
            echo "✗ libyuv解压失败"
        fi
    fi
else
    echo "⚠ 未找到libyuv文件"
fi

# 解压OpenSSL
echo "=== 处理OpenSSL ==="
openssl_file=$(ls *openssl* *OpenSSL* 2>/dev/null | head -1)
if [ -n "$openssl_file" ]; then
    echo "找到OpenSSL文件: $openssl_file"
    if [[ "$openssl_file" == *.zip ]]; then
        unzip -q "$openssl_file"
        # 查找解压后的目录
        openssl_dir=$(ls -d openssl* 2>/dev/null | head -1)
        if [ -n "$openssl_dir" ]; then
            mv "$openssl_dir" openssl_src
            echo "✓ OpenSSL解压完成"
        else
            echo "✗ OpenSSL解压失败"
        fi
    fi
else
    echo "⚠ 未找到OpenSSL文件"
fi

cd ..

echo ""
echo "========================================"
echo "解压结果检查"
echo "========================================"

# 检查解压结果
success_count=0
total_count=4

echo "检查解压后的目录..."

if [ -d "temp_downloads/ffmpeg_src" ]; then
    file_count=$(find temp_downloads/ffmpeg_src -type f | wc -l)
    echo "✓ FFmpeg: temp_downloads/ffmpeg_src ($file_count 个文件)"
    success_count=$((success_count + 1))
else
    echo "✗ FFmpeg: 目录不存在"
fi

if [ -d "temp_downloads/soundtouch_src" ]; then
    file_count=$(find temp_downloads/soundtouch_src -type f | wc -l)
    echo "✓ soundtouch: temp_downloads/soundtouch_src ($file_count 个文件)"
    success_count=$((success_count + 1))
else
    echo "✗ soundtouch: 目录不存在"
fi

if [ -d "temp_downloads/libyuv_src" ]; then
    file_count=$(find temp_downloads/libyuv_src -type f | wc -l)
    echo "✓ libyuv: temp_downloads/libyuv_src ($file_count 个文件)"
    success_count=$((success_count + 1))
else
    echo "✗ libyuv: 目录不存在"
fi

if [ -d "temp_downloads/openssl_src" ]; then
    file_count=$(find temp_downloads/openssl_src -type f | wc -l)
    echo "✓ OpenSSL: temp_downloads/openssl_src ($file_count 个文件)"
    success_count=$((success_count + 1))
else
    echo "✗ OpenSSL: 目录不存在"
fi

echo ""
echo "========================================"
echo "解压完成！"
echo "========================================"
echo "成功解压: $success_count/$total_count"

if [ $success_count -eq $total_count ]; then
    echo "🎉 所有源码都已成功解压！"
    echo ""
    echo "现在可以运行编译脚本："
    echo "export OHOS_SDK=\"/d/harmonyFor/openSDK/11\""
    echo "./build_msys2.sh"
elif [ $success_count -gt 0 ]; then
    echo "⚠ 部分源码解压成功，可以尝试继续编译"
    echo "缺失的依赖可能会导致编译失败"
else
    echo "❌ 解压失败，请检查下载的文件"
    echo ""
    echo "请运行以下命令检查下载的文件："
    echo "ls -la temp_downloads/*.gz temp_downloads/*.zip"
fi
