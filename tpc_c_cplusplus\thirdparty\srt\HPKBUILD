# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=srt
pkgver=1.5.3
pkgrel=0
pkgdesc="Secure Reliable Transport (SRT) is a transport protocol for ultra low (sub-second) latency live video and audio streaming, as well as for generic bulk data transfer"
archs=("armeabi-v7a" "arm64-v8a")
url="https://github.com/Haivision/srt"
license=( "MPL-2.0 license" )
depends=("openssl")
makedepends=()
source="https://github.com/Haivision/${pkgname}/archive/refs/tags/v${pkgver}.tar.gz"


downloadpackage=true
autounpack=true

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz
patchflag=true

prepare() {
    if $patchflag
    then
        cd $builddir
        # 添加适配OHOS的编译选项
        patch -p1 < `pwd`/../srt_oh_pkg.patch
        patchflag=false
        cd $OLDPWD
    fi

    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir/$ARCH-build
    PKG_CONFIG_PATH=${LYCIUM_ROOT}/usr/openssl/$ARCH/lib/pkgconfig \
        ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DENABLE_UNITTESTS=ON \
        -DOHOS_ARCH=$ARCH \
        -DCMAKE_C_FLAGS="-Wno-error=unused-command-line-argument -Wno-error=deprecated-copy" \
        -DCMAKE_CXX_FLAGS="-Wno-unused-command-line-argument -Wno-error=deprecated-copy" \
        -S../ -L > $buildlog 2>&1
    $MAKE VERBOSE=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    $MAKE install >> $buildlog 2>&1
    cd $OLDPWD
}


# 测试，需要在 ohos 设备上进行
check() {
    echo "The test must be on an OpenHarmony device!"
    # ctest
    if [ $ARCH == "armeabi-v7a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/arm-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    elif [ $ARCH == "arm64-v8a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/aarch64-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    else
        echo "${ARCH} not support"
        return -1
    fi
}

cleanbuild() {
  rm -rf ${PWD}/$builddir
}