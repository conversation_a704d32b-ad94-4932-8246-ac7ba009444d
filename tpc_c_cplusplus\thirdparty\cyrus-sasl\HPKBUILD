# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=cyrus-sasl
pkgver=2.1.28
pkgrel=0
pkgdesc="Simple Authentication and Security Layer (SASL) is a specification that describes how authentication mechanisms can be plugged into an application protocol on the wire. Cyrus SASL is an implementation of SASL that makes it easy for application developers to integrate authentication mechanisms into their application in a generic way."
url="https://www.cyrusimap.org/sasl/"
archs=("armeabi-v7a" "arm64-v8a")
license=("Carnegie Mellon Computing Services License")
depends=("sqlite" "gdbm")
makedepends=()
source="https://github.com/cyrusimap/$pkgname/releases/download/$pkgname-$pkgver/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz
patchflag=true

source envset.sh
host=
prepare() {
    if $patchflag
    then
        cd $builddir
	# 1.libc不支持getpass接口，使用scanf替代；2.printf不能及时输出提示信息，使用fflush刷新缓存区
        patch -p1 < `pwd`/../cyrus-sasl_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    else
        echo "${ARCH} not support"
        return -1
    fi
    # crypt函数使用链接静态库方式获取
    export LDFLAGS="-lclang_rt.asan $LDFLAGS"
    # crypt函数返回值忽略类型转换
    export CFLAGS="-Wno-int-conversion $CFLAGS"
}

build() {
    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host --enable-srp --enable-srp-setpass --enable-auth-sasldb --with-dblib=gdbm --with-gdbm=$LYCIUM_ROOT/usr/gdbm/$ARCH \
        --with-sqlite3=$LYCIUM_ROOT/usr/sqlite/$ARCH --enable-static=yes --with-saslauthd=$LYCIUM_ROOT/usr/cyrus-sasl/$ARCH \
	--with-dbpath=$LYCIUM_ROOT/usr/cyrus-sasl/$ARCH/sasldb2 --with-configdir=$LYCIUM_ROOT/usr/cyrus-sasl/$ARCH > `pwd`/build.log 2>&1
    make V=1 -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    unset host
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    else
        echo "${ARCH} not support"
        return -1
    fi
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test cmd
    # echo "127.0.0.1       localhost" > /etc/hosts
    # ping localhost
    # echo -e "pwcheck_method:auxprop\nauxprop_plugin:sasldb\nmech_list:plain login" > $LYCIUM_ROOT/usr/cyrus-sasl/$ARCH/sample.conf
    # ./saslauthd/saslauthd -a sasldb
    # ./utils/saslpasswd2 -c yourname
    # ./utils/sasldblistusers2
    # ./sample/server
    # ./sample/client localhost
}

cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packageName
}
