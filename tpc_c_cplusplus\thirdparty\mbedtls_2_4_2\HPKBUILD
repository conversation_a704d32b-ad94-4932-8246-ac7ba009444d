# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=mbedtls_2_4_2
pkgver=mbedtls-2.4.2
pkgrel=0
pkgdesc="Mbed TLS is a C library that implements cryptographic primitives, X.509 certificate manipulation and the SSL/TLS and DTLS protocols."
url="https://github.com/Mbed-TLS/mbedtls"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD-3-Clause")
depends=()
makedepends=()
source="https://codeload.github.com/Mbed-TLS/${pkgname:0:7}/zip/refs/tags/$pkgver"
buildtools="cmake"

autounpack=true
downloadpackage=true

builddir=${pkgname:0:7}-$pkgver
packagename=$pkgver.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    # 禁用注释检测、未使用的命令参数检测
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" \
        -DCMAKE_C_FLAGS="-Wno-error=documentation -Wno-error=unused-command-line-argument" \
        -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L  > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install VERBOSE=1 >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir
}
