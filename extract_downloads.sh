#!/bin/bash

echo "解压下载的源码包..."

cd temp_downloads

# 检查文件是否存在并解压
if [ -f "ff4.0--ijk0.8.8--20210426--001.tar.gz" ]; then
    echo "解压FFmpeg..."
    tar -xzf ff4.0--ijk0.8.8--20210426--001.tar.gz
    mv FFmpeg-ff4.0--ijk0.8.8--20210426--001 ffmpeg_src
    echo "FFmpeg解压完成"
else
    echo "警告: FFmpeg压缩包不存在"
fi

if [ -f "ijk-r0.1.2-dev.zip" ]; then
    echo "解压soundtouch..."
    unzip -q ijk-r0.1.2-dev.zip
    mv soundtouch-ijk-r0.1.2-dev soundtouch_src
    echo "soundtouch解压完成"
else
    echo "警告: soundtouch压缩包不存在"
fi

if [ -f "ijk-r0.2.1-dev.zip" ]; then
    echo "解压libyuv..."
    unzip -q ijk-r0.2.1-dev.zip
    mv libyuv-ijk-r0.2.1-dev libyuv_src
    echo "libyuv解压完成"
else
    echo "警告: libyuv压缩包不存在"
fi

if [ -f "OpenSSL_1_1_1w.zip" ]; then
    echo "解压OpenSSL..."
    unzip -q OpenSSL_1_1_1w.zip
    mv openssl-OpenSSL_1_1_1w openssl_src
    echo "OpenSSL解压完成"
else
    echo "警告: OpenSSL压缩包不存在"
fi

cd ..

echo ""
echo "解压完成！现在可以运行编译脚本："
echo "./build_msys2.sh"
