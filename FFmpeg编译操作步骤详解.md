# OpenHarmony IJKPlayer FFmpeg 编译操作步骤详解

## 环境要求

### 硬件要求
- **内存**：最低 2GB，推荐 4GB+
- **存储**：至少 10GB 可用空间
- **CPU**：支持多核编译

### 软件环境
- **操作系统**：Linux (Ubuntu/CentOS)
- **OpenHarmony SDK**：版本 4.1+
- **Git**：用于代码管理
- **基础编译工具**：gcc, make, cmake

## 步骤1：环境准备

### 1.1 设置 OpenHarmony SDK
```bash
# 设置 SDK 环境变量
export OHOS_SDK=/path/to/ohos-sdk-4.1/ohos-sdk/linux/11

# 验证 SDK 设置
echo $OHOS_SDK
ls $OHOS_SDK/native/llvm/bin/
```

### 1.2 检查系统资源
```bash
# 检查内存
free -h

# 检查磁盘空间
df -h

# 检查 CPU 核心数
nproc
```

## 步骤2：获取项目代码

### 2.1 克隆项目
```bash
# 克隆包含修复的分支
git clone -b ffmpeg-build-fix https://github.com/WODEXV/ohos_ijkplayer-2.0.3.git

# 进入项目目录
cd ohos_ijkplayer-2.0.3
```

### 2.2 验证关键修改
```bash
# 检查 FFmpeg HPKBUILD 配置
cat tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD | grep -A2 "builddir="

# 应该看到：
# builddir=FFmpeg-ff4.0-ijk0.8.8-20210426-001
# packagename=FFmpeg-ff4.0-ijk0.8.8-20210426-001.tar.gz

# 检查 OpenSSL SHA512SUM 是否已删除
ls tpc_c_cplusplus/thirdparty/openssl_1_1_1w/SHA512SUM 2>/dev/null || echo "SHA512SUM 已删除（正确）"
```

## 步骤3：开始编译

### 3.1 进入编译目录
```bash
cd tpc_c_cplusplus/lycium
```

### 3.2 检查构建环境
```bash
# 验证 SDK 环境
./build.sh --help 2>/dev/null || echo "构建脚本就绪"

# 检查已完成的构建
ls usr/ 2>/dev/null || echo "首次构建"
```

### 3.3 开始 FFmpeg 编译
```bash
# 启动 FFmpeg 编译
./build.sh FFmpeg-ff4.0
```

## 步骤4：编译过程监控

### 4.1 编译阶段说明

#### 阶段1：依赖检查
```
FFmpeg-ff4.0 not ready. wait openssl_1_1_1w
Build openssl_1_1_1w OpenSSL_1_1_1w start!
```
- 系统检测到 FFmpeg 依赖 OpenSSL
- 自动开始构建 OpenSSL

#### 阶段2：下载源码
```
% Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed
100  9.8M  100  9.8M    0     0  1234k      0  0:00:08  0:00:08 --:--:-- 1456k
```
- 从 GitHub 下载 OpenSSL 源码
- 下载 FFmpeg 源码

#### 阶段3：应用补丁
```
Compile OpenHarmony armeabi-v7a FFmpeg-ff4.0 ijk0.8.8--20210426--001 libs...
patching file libavcodec/idctdsp.c
patching file libavcodec/libx264.c
patching file libavformat/hls.c
...
```
- 应用 OpenHarmony 适配补丁
- 修复兼容性问题

#### 阶段4：配置构建
```
/bin/sh ./configure --prefix=/path/to/install --enable-openssl --enable-cross-compile ...
```
- 配置编译参数
- 检测依赖库
- 生成 Makefile

#### 阶段5：编译源码
```
CC    libavcodec/aacdec.o
CC    libavcodec/aacenc.o
CC    libavformat/movenc.o
...
```
- 编译各个模块
- 链接生成库文件

### 4.2 监控命令

#### 在新终端中监控进程
```bash
# 检查编译进程
ps aux | grep -E "(configure|make|gcc|clang)" | grep -v grep

# 监控系统负载
top -n 1 | head -5

# 查看构建日志
tail -f tpc_c_cplusplus/lycium/*build*.log
```

#### 检查编译进度
```bash
# 查看当前构建的架构
ls tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/ | grep build

# 检查输出目录
ls tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/ 2>/dev/null || echo "编译进行中"
```

## 步骤5：常见问题处理

### 5.1 内存不足
**症状：**
```
load average: 32.62, 32.42, 36.04
%Cpu(s): 20.6 us,  2.9 sy, 75.7 st
```

**解决方案：**
```bash
# 添加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 验证交换空间
free -h
```

### 5.2 编译卡住
**症状：**
- 长时间无输出
- CPU 使用率为 0

**解决方案：**
```bash
# 终止卡住的进程
pkill -f "build.sh"
pkill -f "configure"

# 清理部分构建文件
rm -rf tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/*-build

# 重新开始
./build.sh FFmpeg-ff4.0
```

### 5.3 网络下载失败
**症状：**
```
curl: (22) The requested URL returned error: 404
```

**解决方案：**
```bash
# 检查网络连接
ping github.com

# 手动下载文件
cd tpc_c_cplusplus/thirdparty/openssl_1_1_1w
curl -L -o openssl-OpenSSL_1_1_1w.zip https://codeload.github.com/openssl/openssl/zip/refs/tags/OpenSSL_1_1_1w
```

## 步骤6：验证编译结果

### 6.1 检查编译输出
```bash
# 检查 FFmpeg 库文件
ls -la tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/

# 应该看到三个架构的目录：
# armeabi-v7a/
# arm64-v8a/
# x86_64/

# 检查库文件
find tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/ -name "*.a" | head -10
```

### 6.2 验证依赖关系
```bash
# 检查 OpenSSL 是否编译成功
ls tpc_c_cplusplus/lycium/usr/openssl_1_1_1w/

# 检查构建记录
cat tpc_c_cplusplus/lycium/usr/hpk_build.csv
```

## 步骤7：集成到主项目

### 7.1 复制编译结果
```bash
# 复制到 IJKPlayer 项目
cp -r tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0 ijkplayer/src/main/cpp/third_party/ffmpeg/
cp -r tpc_c_cplusplus/lycium/usr/openssl_1_1_1w ijkplayer/src/main/cpp/third_party/openssl/
```

### 7.2 验证集成
```bash
# 检查集成结果
ls ijkplayer/src/main/cpp/third_party/
```

## 编译时间预估

### 各阶段耗时（参考）
- **下载阶段**：2-5 分钟（取决于网络）
- **Configure 阶段**：5-15 分钟
- **编译阶段**：20-60 分钟（取决于硬件）
- **总耗时**：30 分钟 - 2 小时

### 影响因素
- **内存大小**：内存越大，编译越快
- **CPU 核心数**：支持并行编译
- **网络速度**：影响下载时间
- **存储类型**：SSD 比 HDD 快

## 成功标志

编译成功时会看到：
```
Build FFmpeg-ff4.0 ijk0.8.8--20210426--001 end!
```

并且在 `tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/` 目录下有完整的三个架构的库文件。
