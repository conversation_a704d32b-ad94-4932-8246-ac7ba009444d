#!/bin/bash

# Windows MSYS2 环境下的 ijkplayer 依赖库编译脚本
# 使用前请确保已安装 MSYS2 并配置好 OpenHarmony SDK

# 执行该脚本时需进入到脚本所在目录
ROOT_DIR=$(pwd)
API_VERSION=11

# Windows 环境下的 SDK 路径配置
# 请根据实际 SDK 解压位置修改此路径
SDK_DIR="C:/ohos-sdk-4.1/ohos-sdk/windows/11"
# 或者使用相对路径：
# SDK_DIR="$ROOT_DIR/../ohos-sdk-4.1/ohos-sdk/windows/11"

LYCIUM_TOOLS_URL=https://gitee.com/openharmony-sig/tpc_c_cplusplus.git
LYCIUM_ROOT_DIR=$ROOT_DIR/tpc_c_cplusplus
LYCIUM_TOOLS_DIR=$LYCIUM_ROOT_DIR/lycium
LYCIUM_THIRDPARTY_DIR=$LYCIUM_ROOT_DIR/thirdparty
DEPENDS_DIR=$ROOT_DIR/doc
FFMPEG_NAME=FFmpeg-ff4.0
LIBYUV_NAME=libyuv-ijk
SOUNDTOUCH_NAME=soundtouch-ijk
OPESSL_NAME=openssl_1_1_1w

CI_OUTPUT_DIR=$ROOT_DIR/../out/tpc/

LIBS_NAME=("FFmpeg-ff4.0" "libyuv-ijk" "soundtouch-ijk" "openssl_1_1_1w")
PACKAGE_NAME=("FFmpeg-ff4.0-ijk0.8.8-20210426-001.tar.gz" "yuv-ijk-r0.2.1-dev.zip" "soundtouch-ijk-r0.1.2-dev.zip" "openssl-OpenSSL_1_1_1w.zip")

function check_msys2_tools()
{
    echo "检查 MSYS2 环境和必要工具..."
    
    # 检查是否在 MSYS2 环境中
    if [[ "$OSTYPE" != "msys" ]]; then
        echo "错误：请在 MSYS2 环境中运行此脚本"
        return 1
    fi
    
    # 检查必要的工具
    local required_tools=("gcc" "make" "cmake" "git" "wget" "unzip" "python3" "yasm" "nasm")
    local missing_tools=()
    
    for tool in ${required_tools[@]}; do
        if ! which $tool > /dev/null 2>&1; then
            missing_tools+=($tool)
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo "缺少以下工具，请使用 pacman 安装："
        for tool in ${missing_tools[@]}; do
            echo "  pacman -S $tool"
        done
        echo ""
        echo "建议安装完整的开发工具链："
        echo "  pacman -S base-devel"
        echo "  pacman -S mingw-w64-x86_64-toolchain"
        echo "  pacman -S mingw-w64-x86_64-cmake"
        echo "  pacman -S mingw-w64-x86_64-yasm"
        echo "  pacman -S mingw-w64-x86_64-nasm"
        return 1
    fi
    
    echo "MSYS2 工具检查完成"
    return 0
}

function prepare_lycium()
{
    if [ -d $LYCIUM_ROOT_DIR ]; then
        echo "删除现有的 lycium 工具链..."
        rm -rf $LYCIUM_ROOT_DIR
    fi

    echo "正在下载 lycium 工具链..."
    git clone $LYCIUM_TOOLS_URL -b support_x86 --depth=1
    if [ $? -ne 0 ]; then
        echo "错误：下载 lycium 工具链失败"
        return 1
    fi

    cd $LYCIUM_TOOLS_DIR/Buildtools
    if [ ! -f toolchain.tar.gz ]; then
        echo "错误：找不到 toolchain.tar.gz"
        cd $OLDPWD
        return 1
    fi
    
    echo "解压工具链..."
    tar -zxvf toolchain.tar.gz
    if [ $? -ne 0 ]; then
        echo "错误：解压 SDK 工具链失败"
        cd $OLDPWD
        return 1
    fi

    # Windows 环境下需要确保 SDK 目录存在
    if [ ! -d "$SDK_DIR/native/llvm/bin/" ]; then
        echo "错误：SDK 目录不存在: $SDK_DIR/native/llvm/bin/"
        echo "请检查 SDK_DIR 配置是否正确"
        cd $OLDPWD
        return 1
    fi
    
    echo "复制工具链到 SDK 目录..."
    cp toolchain/* "$SDK_DIR/native/llvm/bin/"
    if [ $? -ne 0 ]; then
        echo "警告：复制工具链到 SDK 目录失败，但继续执行"
    fi

    cd $OLDPWD
    return 0
}

function copy_depends()
{
    local dir=$1
    local name=$2

    if [ -d $LYCIUM_THIRDPARTY_DIR/$name ]; then
        rm -rf $LYCIUM_THIRDPARTY_DIR/$name
    fi
    cp -arf $dir/$name $LYCIUM_THIRDPARTY_DIR/
}

function prepare_depends()
{
    echo "准备依赖库配置..."
    copy_depends $DEPENDS_DIR $LIBYUV_NAME
    copy_depends $DEPENDS_DIR $SOUNDTOUCH_NAME
}

function check_sdk()
{
    echo "检查 OpenHarmony SDK..."
    
    # 转换 Windows 路径为 MSYS2 路径格式
    local sdk_path_unix=$(cygpath -u "$SDK_DIR" 2>/dev/null || echo "$SDK_DIR")
    
    if [ ! -d "$sdk_path_unix" ]; then
        echo "错误：SDK 目录不存在: $SDK_DIR"
        echo "请确保已正确下载并解压 OpenHarmony SDK"
        return 1
    fi

    # 检查关键的编译器文件
    if [ ! -f "$sdk_path_unix/native/llvm/bin/clang.exe" ] && [ ! -f "$sdk_path_unix/native/llvm/bin/clang" ]; then
        echo "错误：找不到 clang 编译器"
        echo "请检查 SDK 是否为 Windows 版本"
        return 1
    fi

    export OHOS_SDK="$SDK_DIR"
    echo "SDK 检查完成: $OHOS_SDK"
    return 0
}

function start_build()
{
    local result=0
    cd $LYCIUM_TOOLS_DIR
    if [ $? -ne 0 ]; then
        echo "错误：无法进入 lycium 工具目录"
        return 1
    fi

    echo "开始编译依赖库..."
    bash build.sh $FFMPEG_NAME $LIBYUV_NAME $SOUNDTOUCH_NAME
    result=$?
    cd $OLDPWD
    return $result
}

function install_depends()
{
    echo "安装编译好的依赖库..."
    local install_dir=$ROOT_DIR/ijkplayer/src/main/cpp/third_party/
    
    # 确保目标目录存在
    mkdir -p "$install_dir/ffmpeg"
    mkdir -p "$install_dir/yuv"
    mkdir -p "$install_dir/openssl"
    mkdir -p "$install_dir/soundtouch"
    
    cp -arf $LYCIUM_TOOLS_DIR/usr/$FFMPEG_NAME $install_dir/ffmpeg/ffmpeg
    if [ $? -ne 0 ]; then
        echo "错误：FFmpeg 编译失败!"
        return 1
    fi
    
    cp -arf $LYCIUM_TOOLS_DIR/usr/yuv $install_dir/yuv
    if [ $? -ne 0 ]; then
        echo "错误：yuv 编译失败!"
        return 1
    fi
    
    cp -arf $LYCIUM_TOOLS_DIR/usr/$OPESSL_NAME $install_dir/openssl
    if [ $? -ne 0 ]; then
        echo "错误：OpenSSL 编译失败!"
        return 1
    fi
    
    cp -arf $LYCIUM_TOOLS_DIR/usr/soundtouch $install_dir/soundtouch
    if [ $? -ne 0 ]; then
        echo "错误：soundtouch 编译失败!"
        return 1
    fi

    if [ -d $CI_OUTPUT_DIR ]; then
        mkdir -p $CI_OUTPUT_DIR
        cp -arf $LYCIUM_TOOLS_DIR/usr/$FFMPEG_NAME $CI_OUTPUT_DIR
        cp -arf $LYCIUM_TOOLS_DIR/usr/yuv $CI_OUTPUT_DIR
        cp -arf $LYCIUM_TOOLS_DIR/usr/$OPESSL_NAME $CI_OUTPUT_DIR
        cp -arf $LYCIUM_TOOLS_DIR/usr/soundtouch $CI_OUTPUT_DIR
    fi

    echo "依赖库安装完成!"
    return 0
}

function prebuild()
{
    echo "开始 Windows MSYS2 环境下的 ijkplayer 依赖库编译..."
    
    check_msys2_tools
    if [ $? -ne 0 ]; then
        echo "错误：MSYS2 工具检查失败"
        return 1
    fi
    
    check_sdk
    if [ $? -ne 0 ]; then
        echo "错误：SDK 检查失败"
        return 1
    fi
    
    prepare_lycium
    if [ $? -ne 0 ]; then
        echo "错误：准备 lycium 工具链失败"
        return 1
    fi
    
    prepare_depends
    
    start_build
    if [ $? -ne 0 ]; then
        echo "错误：编译失败"
        return 1
    fi
    
    install_depends
    if [ $? -ne 0 ]; then
        echo "错误：安装依赖库失败"
        return 1
    fi
    
    echo "Windows MSYS2 环境下的编译完成!"
    return 0
}

# 主函数
prebuild $*
ret=$?
echo "编译结果: $ret"
exit $ret
