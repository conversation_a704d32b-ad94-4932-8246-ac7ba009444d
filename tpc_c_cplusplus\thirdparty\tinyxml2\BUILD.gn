# Copyright (c) 2021 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

declare_args() {
  enable_tinyxml2_test = false
}

config("tinyxml2_config") {
  include_dirs = [ "//third_party/tinyxml2/tinyxml2" ]

  cflags = [
    "-fdata-sections",
    "-ffunction-sections",
    "-funwind-tables",
    "-fstack-protector-strong",
    "-no-canonical-prefixes",
    "-fno-addrsig",
    "-Wa,--noexecstack",
    "-Wformat",
    "-Werror=format-security",
    "-D__MUSL__",
    "-fPIC",
    "-fvisibility=hidden",
    "-fvisibility-inlines-hidden",
  ]
}

ohos_shared_library("tinyxml2_shared") {
  sources = [ "//third_party/tinyxml2/tinyxml2/tinyxml2.cpp" ]

  configs = [ ":tinyxml2_config" ]

  deps = []

  part_name = "tinyxml2"
}

ohos_executable("xmltest") {
  sources = [ "//third_party/tinyxml2/tinyxml2/xmltest.cpp" ]
  deps = [ ":tinyxml2_shared" ]
  configs = [ ":tinyxml2_config" ]
  part_name = "tinyxml2"
}

group("tinyxml2") {
  deps = [ ":tinyxml2_shared" ]
  if (enable_tinyxml2_test) {
    deps += [ ":xmltest" ]
  }
}
