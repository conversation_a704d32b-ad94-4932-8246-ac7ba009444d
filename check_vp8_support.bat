@echo off
echo === VP8 支持检查脚本 (Windows版) ===
echo.

set FFMPEG_DIR=ijkplayer\src\main\cpp\third_party\ffmpeg

if not exist "%FFMPEG_DIR%" (
    echo ❌ 错误：FFmpeg 目录不存在: %FFMPEG_DIR%
    echo 请确保您在正确的项目根目录下运行此脚本
    pause
    exit /b 1
)

echo ✅ 找到 FFmpeg 目录: %FFMPEG_DIR%
echo.

echo === 检查库文件是否存在 ===
for %%a in (armeabi-v7a arm64-v8a x86_64) do (
    echo.
    echo 检查架构: %%a
    set LIBAVCODEC=%FFMPEG_DIR%\%%a\lib\libavcodec.a
    
    if exist "%FFMPEG_DIR%\%%a\lib\libavcodec.a" (
        echo ✅ 找到 libavcodec.a: %FFMPEG_DIR%\%%a\lib\libavcodec.a
        
        REM 检查文件大小
        for %%f in ("%FFMPEG_DIR%\%%a\lib\libavcodec.a") do (
            echo 📦 文件大小: %%~zf 字节
        )
        
        REM 尝试在文件中查找 VP8 字符串
        echo 🔍 检查 VP8 相关内容...
        findstr /i "vp8" "%FFMPEG_DIR%\%%a\lib\libavcodec.a" >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✅ 在库文件中发现 VP8 相关内容
        ) else (
            echo ❌ 在库文件中未发现 VP8 相关内容
        )
        
    ) else (
        echo ❌ 未找到 libavcodec.a: %FFMPEG_DIR%\%%a\lib\libavcodec.a
    )
)

echo.
echo === 检查头文件 ===
set HEADER_DIR=%FFMPEG_DIR%\arm64-v8a\include

if exist "%HEADER_DIR%" (
    echo ✅ 找到头文件目录: %HEADER_DIR%
    
    REM 检查头文件中的 VP8 定义
    findstr /s /i "AV_CODEC_ID_VP8" "%HEADER_DIR%\*" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ 在头文件中发现 VP8 相关定义
        findstr /s /i "AV_CODEC_ID_VP8" "%HEADER_DIR%\*"
    ) else (
        echo ❌ 在头文件中未发现 VP8 相关定义
    )
    
) else (
    echo ❌ 未找到头文件目录: %HEADER_DIR%
)

echo.
echo === 检查完成 ===
echo.
echo 💡 如果看到 VP8 相关内容，说明 VP8 支持可能已编译进库中
echo 💡 您可以在应用中尝试播放 VP8 格式的视频来进一步验证
echo.
pause
