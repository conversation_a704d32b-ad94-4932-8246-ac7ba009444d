#Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>> luozhu <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=flatbuffers
pkgver=v23.5.9
pkgrel=0
pkgdesc="FlatBuffers is a cross platform serialization library architected for maximum memory efficiency."
url="https://github.com/google/flatbuffers"
archs=("armeabi-v7a" "arm64-v8a")
license=("MIT")
depends=()
makedepends=()

source="https://github.com/google/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildhostflatc=true

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    if $buildhostflatc
    then
        cp -rf $builddir $builddir-host-build
        cd $builddir-host-build
        cmake . > $publicbuildlog 2>&1
        $MAKE VERBOSE=1 >> $publicbuildlog 2>&1
        buildhostflatc=false

        cd $OLDPWD
    fi
    cp -rf $builddir $builddir-$ARCH-build
}

build() {
    cd $builddir-$ARCH-build
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DFLATBUFFERS_STATIC_FLATC=ON \
        -DFLATBUFFERS_FLATC_EXECUTABLE=`pwd`/../$builddir-host-build/flatc -B./ \
        -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    $MAKE VERBOSE=1 install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # ctest
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir ${PWD}/$builddir-host-build ${PWD}/$builddir-armeabi-v7a-build ${PWD}/$builddir-arm64-v8a-build #${PWD}/$packagename
}
