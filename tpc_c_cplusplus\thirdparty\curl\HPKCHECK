# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
openharmonycheck() {
    res=0
    ## checkNetWork
    ping www.baidu.com -c3  >> /dev/null
    if [ $? -ne 0 ]
    then
        echo "the network not ready, the test depends the network! make sure the network is OK!!"
        return 1
    fi

    cd ${builddir}/${ARCH}-build
    cp ../../curl_test.sh ./
    touch data.txt
    bash curl_test.sh > ${logfile} 2>&1
    res=$?
    cd $OLDPWD

    return $res
}
