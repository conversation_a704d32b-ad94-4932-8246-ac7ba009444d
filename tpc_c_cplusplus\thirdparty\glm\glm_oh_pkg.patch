diff -Naur glm-0.9.9.8/CMakeLists.txt glm-0.9.9.8-patch/CMakeLists.txt
--- glm-0.9.9.8/CMakeLists.txt	2023-06-08 14:03:36.843252172 +0800
+++ glm-0.9.9.8-patch/CMakeLists.txt	2023-06-08 14:50:28.914399961 +0800
@@ -12,4 +12,9 @@
 
 add_subdirectory(test)
 
+install(DIRECTORY glm DESTINATION include
+                FILES_MATCHING PATTERN "*.hpp" PATTERN "*.h")
+install(FILES ${OHOS_ARCH}-build/glm/libglm_shared.so DESTINATION lib)
+install(FILES ${OHOS_ARCH}-build/glm/libglm_static.a DESTINATION lib)
+
 endif(${CMAKE_SOURCE_DIR} STREQUAL ${CMAKE_CURRENT_SOURCE_DIR})
diff -Naur glm-0.9.9.8/test/CMakeLists.txt glm-0.9.9.8-patch/test/CMakeLists.txt
--- glm-0.9.9.8/test/CMakeLists.txt	2023-06-08 14:03:36.895253483 +0800
+++ glm-0.9.9.8-patch/test/CMakeLists.txt	2023-06-08 14:06:50.772482956 +0800
@@ -197,7 +197,7 @@
 		message("GLM: Clang - ${CMAKE_CXX_COMPILER_ID} compiler")
 	endif()
 
-	add_compile_options(-Werror -Weverything)
+	#add_compile_options(-Werror -Weverything)
 	add_compile_options(-Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c++11-long-long -Wno-padded -Wno-gnu-anonymous-struct -Wno-nested-anon-types)
 	add_compile_options(-Wno-undefined-reinterpret-cast -Wno-sign-conversion -Wno-unused-variable -Wno-missing-prototypes -Wno-unreachable-code -Wno-missing-variable-declarations -Wno-sign-compare -Wno-global-constructors -Wno-unused-macros -Wno-format-nonliteral)
 
