# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=libmdbx
pkgver=v0.11.6
pkgrel=0
pkgdesc="One of the fastest embeddable key-value ACID database without WAL. libmdbx surpasses the legendary LMDB in terms of reliability, features and performance."
url="https://erthink.github.io/libmdbx/"
archs=("armeabi-v7a" "arm64-v8a")
license=("OpenLDAP Public License Version 2.8")
depends=()
makedepends=()

source="https://github.com/erthink/$pkgname.git"

autounpack=false
downloadpackage=false
cloneflag=true

builddir=$pkgname-${pkgver:1}
packagename=
patchflag=true

prepare() {
    if [ $cloneflag == true ]
    then
        git clone -b $pkgver $source $builddir > $publicbuildlog 2>&1
        ret=$?
        if [ $ret -ne 0 ]
        then
            echo "[prepare failed]:git clone ${source} ret=" $ret
            return 1
        fi
        cloneflag=false
    fi
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DMDBX_LOCKING=2001 -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    
    # 编译example用于测试
    cd $builddir/example    
    if [ "$ARCH" = "armeabi-v7a" ]
    then
        ${OHOS_SDK}/native/llvm/bin/clang example-mdbx.c \
        --target=arm-linux-ohos --sysroot=${OHOS_SDK}/native/sysroot -march=armv7a \
        -L../$ARCH-build/ -lmdbx -I../ \
        -o ../$ARCH-build/mdbx_example >> $buildlog 2>&1
    else
        ${OHOS_SDK}/native/llvm/bin/clang example-mdbx.c \
        --target=aarch64-linux-ohos --sysroot=${OHOS_SDK}/native/sysroot \
        -L../$ARCH-build/ -lmdbx -I../ \
        -o ../$ARCH-build/mdbx_example >> $buildlog 2>&1
    fi
    
    ret=$?
    cd $OLDPWD
    return $ret
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
