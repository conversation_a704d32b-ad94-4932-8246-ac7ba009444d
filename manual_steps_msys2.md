# MSYS2环境下手动编译OpenHarmony ijkplayer依赖库

## 快速开始

### 1. 打开MSYS2终端
启动MSYS2 UCRT64终端（推荐）或MSYS2 MINGW64终端

### 2. 进入项目目录
```bash
cd /d/new/ohos_ijkplayer-2.0.3
```

### 3. 设置环境变量
```bash
# 设置OpenHarmony SDK路径（请根据实际路径修改）
export OHOS_SDK="/c/Users/<USER>/AppData/Local/OpenHarmony/Sdk/11"
# 或者
export OHOS_SDK="/d/path/to/your/ohos-sdk/windows/11"
```

### 4. 运行自动化脚本
```bash
# 给脚本执行权限
chmod +x build_msys2.sh

# 执行脚本
./build_msys2.sh
```

## 手动步骤（如果自动化脚本失败）

### 步骤1：安装必要工具
```bash
# 更新包管理器
pacman -Syu

# 安装编译工具
pacman -S --needed base-devel mingw-w64-x86_64-toolchain
pacman -S git wget unzip cmake make autoconf automake libtool pkg-config
```

### 步骤2：下载lycium工具链
```bash
# 下载lycium工具链
git clone https://gitee.com/openharmony-sig/tpc_c_cplusplus.git -b support_x86 --depth=1

# 进入lycium目录
cd tpc_c_cplusplus/lycium
```

### 步骤3：复制依赖配置
```bash
# 返回项目根目录
cd ../..

# 复制配置文件
cp -r doc/libyuv-ijk tpc_c_cplusplus/thirdparty/
cp -r doc/soundtouch-ijk tpc_c_cplusplus/thirdparty/
```

### 步骤4：下载源码并创建包
```bash
# 创建临时目录
mkdir -p temp_downloads
cd temp_downloads

# 下载FFmpeg源码
git clone https://github.com/bilibili/FFmpeg.git -b ff4.0--ijk0.8.8--20210426--001 --depth=1 ffmpeg_src

# 下载soundtouch源码
git clone https://github.com/bilibili/soundtouch.git -b ijk-r0.1.2-dev --depth=1 soundtouch_src

# 下载libyuv源码
git clone https://github.com/bilibili/libyuv.git -b ijk-r0.2.1-dev --depth=1 libyuv_src

# 下载OpenSSL源码
git clone https://github.com/openssl/openssl.git -b OpenSSL_1_1_1w --depth=1 openssl_src
```

### 步骤5：创建源码包
```bash
# 创建必要目录
mkdir -p ../tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0
mkdir -p ../tpc_c_cplusplus/thirdparty/soundtouch-ijk
mkdir -p ../tpc_c_cplusplus/thirdparty/libyuv-ijk
mkdir -p ../tpc_c_cplusplus/thirdparty/openssl_1_1_1w

# 创建FFmpeg包
cd ffmpeg_src
tar -czf ../tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/FFmpeg-ff4.0-ijk0.8.8-20210426-001.tar.gz .
cd ..

# 创建soundtouch包
cd soundtouch_src
zip -r ../tpc_c_cplusplus/thirdparty/soundtouch-ijk/soundtouch-ijk-r0.1.2-dev.zip .
cd ..

# 创建libyuv包
cd libyuv_src
zip -r ../tpc_c_cplusplus/thirdparty/libyuv-ijk/yuv-ijk-r0.2.1-dev.zip .
cd ..

# 创建OpenSSL包
cd openssl_src
zip -r ../tpc_c_cplusplus/thirdparty/openssl_1_1_1w/openssl-OpenSSL_1_1_1w.zip .
cd ..
```

### 步骤6：执行编译
```bash
# 返回项目根目录
cd ..

# 进入lycium目录
cd tpc_c_cplusplus/lycium

# 给脚本执行权限
chmod +x build.sh

# 执行编译
./build.sh FFmpeg-ff4.0 libyuv-ijk soundtouch-ijk
```

### 步骤7：复制编译结果
```bash
# 返回项目根目录
cd ../..

# 创建目标目录
mkdir -p ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg
mkdir -p ijkplayer/src/main/cpp/third_party/soundtouch
mkdir -p ijkplayer/src/main/cpp/third_party/yuv
mkdir -p ijkplayer/src/main/cpp/third_party/openssl

# 复制编译结果
cp -r tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/* ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/
cp -r tpc_c_cplusplus/lycium/usr/yuv/* ijkplayer/src/main/cpp/third_party/yuv/
cp -r tpc_c_cplusplus/lycium/usr/soundtouch/* ijkplayer/src/main/cpp/third_party/soundtouch/
cp -r tpc_c_cplusplus/lycium/usr/openssl_1_1_1w/* ijkplayer/src/main/cpp/third_party/openssl/
```

## 验证安装

检查以下目录是否包含文件：
```bash
ls -la ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/include/
ls -la ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/lib/
ls -la ijkplayer/src/main/cpp/third_party/soundtouch/include/
ls -la ijkplayer/src/main/cpp/third_party/soundtouch/lib/
ls -la ijkplayer/src/main/cpp/third_party/yuv/include/
ls -la ijkplayer/src/main/cpp/third_party/yuv/lib/
ls -la ijkplayer/src/main/cpp/third_party/openssl/include/
ls -la ijkplayer/src/main/cpp/third_party/openssl/lib/
```

## 故障排除

### 网络问题
如果git clone失败，可以：
1. 使用代理：`git config --global http.proxy http://proxy:port`
2. 手动下载zip包并解压
3. 使用镜像源

### 编译错误
1. 检查工具链是否完整安装
2. 确认OpenHarmony SDK路径正确
3. 查看编译日志中的具体错误信息

### 权限问题
```bash
# 确保脚本有执行权限
chmod +x build.sh
chmod +x prebuild.sh
```

### 路径问题
在MSYS2中：
- Windows路径 `D:\path` 对应 `/d/path`
- 使用正斜杠 `/` 而不是反斜杠 `\`

## 完成后

编译完成后，您可以：
1. 使用DevEco Studio打开项目
2. 编译整个ijkplayer项目
3. 在OpenHarmony设备上测试

## 清理

如果需要重新编译，可以清理临时文件：
```bash
rm -rf temp_downloads
rm -rf tpc_c_cplusplus
```
