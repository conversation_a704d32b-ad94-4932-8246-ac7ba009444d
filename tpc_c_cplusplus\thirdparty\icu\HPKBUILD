# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=icu
pkgver=release-73-2
pkgrel=0
pkgdesc="icu is short for International Components for Unicode."
url="https://github.com/unicode-org/icu"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD License")
depends=()
makedepends=()
# 官方下载地址https://github.com/unicode-org/$pkgname/archive/refs/tags/$pkgver.tar.gz受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/lycium_pkg_mirror/$pkgname/repository/archive/$pkgver.zip"
buildtools="configure"

autounpack=true
downloadpackage=true
buildhost=true

builddir=$pkgname-$pkgver
packagename=$pkgname-$pkgver.zip

source envset.sh
host=

prepare() {
    # 需要先编译host版本
    if [ $buildhost == true ];then
        mkdir -p $builddir/host-build
        cd $builddir/host-build
        CXXFLAGS="-std=c++11 -DU_USING_ICU_NAMESPACE=0" ../icu4c/source/runConfigureICU Linux/gcc > `pwd`/build.log 2>&1
        make -j4 VERBOSE=1 >> `pwd`/build.log 2>&1
        if [ $? -ne 0 ];then
            cat config.log >> `pwd`/build.log 2>&1
            return -2
        fi
        buildhost=false
        cd $OLDPWD
    fi
    mkdir -p $builddir/$ARCH-build
    
    if [ $ARCH == "armeabi-v7a" ];then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ];then
        setarm64ENV
        host=aarch64-linux
    else
        echo "$ARCH not support"
        return -3
    fi
}

build() {
    cd $builddir/$ARCH-build
    CXXFLAGS="$CXXFLAGS -std=c++11 -DU_USING_ICU_NAMESPACE=0" ../icu4c/source/configure "$@" --host=$host --with-cross-build=`pwd`/../host-build > `pwd`/build.log 2>&1
    make -j4 VERBOSE=1 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install VERBOSE=1 >> `pwd`/build.log 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ];then
        unsetarm32ENV
        unset host
    elif [ $ARCH == "arm64-v8a" ];then
        unsetarm64ENV
        unset host
    else
        echo "$ARCH not support"
        return -1
    fi
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # TODO
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
