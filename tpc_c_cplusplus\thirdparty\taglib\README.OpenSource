[{"Name": "taglib", "License": "LGPL-2.1 and MPL-1.1", "License File": ["https://github.com/taglib/taglib/blob/master/COPYING.LGPL", "https://github.com/taglib/taglib/blob/master/COPYING.MPL"], "Version Number": "v1.13.1", "Owner": "<EMAIL>", "Upstream URL": "https://codeload.github.com/taglib/taglib/zip/refs/tags/v1.13.1", "Description": "TagLib Audio Meta-Data Library"}, {"Name": "zlib", "License": "zlib License", "License File": "https://github.com/madler/zlib/blob/master/LICENSE", "Version Number": "v1.2.13", "Owner": "<EMAIL>", "Upstream URL": "https://github.com/madler/zlib/releases/download/v1.2.13/zlib-1.2.13.tar.gz", "Description": "A massively spiffy yet delicately unobtrusive compression library."}, {"Name": "cppunit", "License": "LGPL-2.0 license", "License File": "https://sourceforge.net/projects/cppunit/", "Version Number": "1.14.0", "Owner": "<EMAIL>", "Upstream URL": "https://dev-www.libreoffice.org/src/cppunit-1.14.0.tar.gz", "Description": "CppUnit is the C++ port of the famous JUnit framework for unit testing"}]