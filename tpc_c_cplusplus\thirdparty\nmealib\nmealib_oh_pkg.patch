diff -Naur nmealib-0.6.5/Makefile.inc nmealib-0.6.5-armeabi-v7a-build/Makefile.inc
--- nmealib-0.6.5/Makefile.inc	2012-01-10 19:19:36.000000000 +0800
+++ nmealib-0.6.5-armeabi-v7a-build/Makefile.inc	2024-05-16 20:00:03.597309400 +0800
@@ -34,6 +34,6 @@
 VERSION=0.0.0
 endif
 
-CC ?= gcc
+CC =
 CCFLAGS += -fPIC -O2 -Wall -Wextra -Wformat=2 -Winit-self \
-           -Wmissing-include-dirs -Wswitch-default -Wswitch-enum -Werror
+           -Wmissing-include-dirs -Wswitch-default -Wswitch-enum
diff -Naur nmealib-0.6.5/samples/Makefile nmealib-0.6.5-armeabi-v7a-build/samples/Makefile
--- nmealib-0.6.5/samples/Makefile	2012-01-10 19:19:36.000000000 +0800
+++ nmealib-0.6.5-armeabi-v7a-build/samples/Makefile	2024-05-16 20:01:06.173245700 +0800
@@ -8,7 +8,7 @@
 SMPLS = $(SAMPLES:%=../build/samples/%)
 SMPLOBJ = $(SAMPLES:%=%/main.o)
 
-LIBS = -lm -L../lib -lnmea
+LIBS = -L../lib -lnmea -lm
 INCS = -I ../include
 
 
