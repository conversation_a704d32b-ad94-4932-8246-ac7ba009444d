# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    res=0
    #该测试用例需要联网才能进行后续的测试
    ping www.baidu.com
    res=$?
    if [ $res != 0 ]
    then
        return $res
    fi

    echo "127.0.0.1 localhost localhost localdomain" > /etc/hosts

    # 运行测试用例
    cd $LYCIUM_THIRDPARTY_ROOT/$pkgname/$builddir/test/
    ./test-$ARCH > ${logfile} 2>&1
    res=$?
    if [ $res != 0 ]
    then
        cp ${logfile} $LYCIUM_ROOT/check_result/check_fault
    fi
    cd $OLDPWD
    return $res
}
