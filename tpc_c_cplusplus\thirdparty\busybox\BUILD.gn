
# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("//build/config/python.gni")
import("//build/templates/metadata/module_info.gni")

template("build_busybox") {
    _main_target_name = target_name
    _target_label =
        get_label_info(":${_main_target_name}", "label_with_toolchain")
    _subsystem_name = invoker.subsystem_name
    _part_name = invoker.part_name

    action(target_name) {
        forward_variables_from(invoker,
                           [
                             "sources",
                             "outputs",
                             "args",
                             "script",
                           ])

        _install_module_info = {
            module_def = _target_label
            module_info_file =
                rebase_path(get_label_info(_target_label, "target_out_dir"),
                        root_build_dir) +
                "/${_main_target_name}_module_info.json"
            subsystem_name = _subsystem_name
            part_name = _part_name
            toolchain = current_toolchain
            toolchain_out_dir = rebase_path(root_out_dir, root_build_dir)
        }
        metadata = {
            install_modules = [ _install_module_info ]
        }
    }
    generate_module_info("${_main_target_name}_info") {
        module_name = _main_target_name
        module_type = "bin"
        module_source_dir = "${target_out_dir}"
        install_enable = true
        module_install_images = [ "system" ]
    }
}

build_busybox("busybox") {

    script = "build_busybox.sh"
    sources = [ "//third_party/busybox" ]
    outputs = [ "${target_out_dir}/busybox" ]
    args = [
        rebase_path(target_out_dir, root_build_dir),
        rebase_path("//"),
        current_cpu
    ]

    subsystem_name = "thirdparty"
    part_name = "busybox"
}

group("busybox_cmd") {
    deps = [":busybox"]
}
