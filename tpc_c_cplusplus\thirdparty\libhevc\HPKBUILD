# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=libhevc
pkgver=simpleperf-release   # 2024.02.02
pkgrel=0
pkgdesc="libhevc is a format codec library"
url="https://android.googlesource.com/platform/external/libhevc"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache License 2.0")
depends=()
makedepends=()
# 原仓地址镜像下载失败，使用镜像仓
# source="https://android.googlesource.com/platform/external/$pkgname/+archive/refs/heads/$pkgver.tar.gz"
source="https://gitee.com/lycium_pkg_mirror/$pkgname/repository/archive/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="cmake"
patchflag=true

builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz
systemprocesser=
prepare() {
    cd $builddir
    if [ "$patchflag" == true ]
    then
        patch -p1 < ../libhevc_ohos_pkg.patch
        patchflag=false
    fi
    if [ $ARCH == "armeabi-v7a" ]
    then
        systemprocesser="aarch32"
    elif [ $ARCH == "arm64-v8a" ]
    then
        systemprocesser="aarch64"
    else
        echo "${ARCH} not support"
        return -1
    fi
}

build() {
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake \
    -DCMAKE_TOOLCHAIN_FILE=${OHOS_SDK}/native/build/cmake/ohos.toolchain.cmake \
    -DCMAKE_SYSTEM_PROCESSOR=${systemprocesser} \
    -DCMAKE_C_FLAGS="-DENABLE_NEON" \
    -DCMAKE_CXX_FLAGS="-DENABLE_NEON" \
    -B$ARCH-build -S ./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    # 不能make install 需要手动拷贝静态库文件
    cd $builddir/$ARCH-build
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin/
    
    cp *.a $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    cp hevcdec $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin/
    cp hevcenc $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin/
    
    cp ../common/ihevc_typedefs.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp ../common/iv.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp ../common/ivd.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp ../common/ithread.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    
    cp ../encoder/itt_video_api.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp ../encoder/ihevce_api.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp ../encoder/ihevce_plugin.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp ../encoder/ihevce_profile.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp ../decoder/ihevcd_cxa.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/

    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    # 拷贝测试资源
    cp enc.cfg $builddir/$ARCH-build/
    cp test.cfg $builddir/$ARCH-build/
    cp *.yuv $builddir/$ARCH-build/
    cp *.h265 $builddir/$ARCH-build/
    cp *.yuv $builddir/test/decoder
    cp *.h265 $builddir/test/encoder
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir  #${PWD}/$packagename
}
