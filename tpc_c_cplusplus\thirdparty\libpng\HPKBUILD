# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <hanjin<PERSON><EMAIL>>
pkgname=libpng
pkgver=v1.6.39
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("PNG Reference Library License version 2")
depends=()
makedepends=()
install=

# 官方下载地址source="https://sourceforge.net/projects/$pkgname/files/libpng16/$pkgver/$pkgname-$pkgver.tar.xz"受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.zip

source envset.sh
host=

prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
}

build() {
    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host --enable-shared --enable-static > `pwd`/build.log 2>&1
    ret=-1
    if [ $LYCIUM_BUILD_OS == "CYGWI" ]
    then
        make libpng16.la -j4 >> `pwd`/build.log 2>&1
        ret=$?
    else
        make -j4 >> `pwd`/build.log 2>&1
        ret=$?
    fi
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    ret=-1
    if [ $LYCIUM_BUILD_OS == "CYGWI" ]
    then   
        make install-libLTLIBRARIES >> `pwd`/build.log 2>&1
        make install-library-links >> `pwd`/build.log 2>&1
        make install-pkgincludeHEADERS >> `pwd`/build.log 2>&1
        make install-header-links >> `pwd`/build.log 2>&1
        make install-pkgconfigDATA >> `pwd`/build.log 2>&1
        ret=$?
    else
        make install >> `pwd`/build.log 2>&1
        ret=$?
    fi
    cd $OLDPWD
    return $ret
}

check() {
    cd $builddir/$ARCH-build
    make pngtest pngunknown pngstest pngvalid pngimage pngcp timepng >> `pwd`/build.log 2>&1
    sed -i '/.*check-TESTS: $(check_PROGRAMS)/c\check-TESTS: #$(check_PROGRAMS)' Makefile
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    unset host
    echo "The test must be on an OpenHarmony device!"
    # test CMD
    # make check-TESTS
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
