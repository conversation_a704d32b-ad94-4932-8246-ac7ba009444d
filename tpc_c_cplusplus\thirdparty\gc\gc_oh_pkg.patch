diff -Naur gc-7f6f17c8b3425df6cd27d6f9385265b23034a793-armeabi-v7a-build/src/Makefile gc-7f6f17c8b3425df6cd27d6f9385265b23034a793-armeabi-v7a-build_new/src/Makefile
--- gc-7f6f17c8b3425df6cd27d6f9385265b23034a793-armeabi-v7a-build/src/Makefile	1970-01-01 08:00:00.000000000 +0800
+++ gc-7f6f17c8b3425df6cd27d6f9385265b23034a793-armeabi-v7a-build_new/src/Makefile	2024-05-10 16:45:22.630030600 +0800
@@ -0,0 +1,19 @@
+CFLAGS=-g -fPIC -Wall -Wextra -pedantic -I./include
+LDFLAGS=
+
+SRCS=gc.c log.c
+OBJS=$(SRCS:.c=.o)
+
+LIB_NAME=libgc.so
+
+all: $(LIB_NAME)
+
+%.o: %.c
+	$(CC) $(CFLAGS) -c $< -o $@
+$(LIB_NAME): $(OBJS)
+	$(CC) -shared $(OBJS) -o $@
+
+clean:
+	rm -f $(OBJS) $(LIB_NAME)
+
+.PHONY: all clean
diff -Naur gc-7f6f17c8b3425df6cd27d6f9385265b23034a793-armeabi-v7a-build/test/Makefile gc-7f6f17c8b3425df6cd27d6f9385265b23034a793-armeabi-v7a-build_new/test/Makefile
--- gc-7f6f17c8b3425df6cd27d6f9385265b23034a793-armeabi-v7a-build/test/Makefile	2024-05-10 16:46:07.923807400 +0800
+++ gc-7f6f17c8b3425df6cd27d6f9385265b23034a793-armeabi-v7a-build_new/test/Makefile	2024-05-10 16:45:22.663005200 +0800
@@ -1,4 +1,3 @@
-CC=clang
 CFLAGS=-g -Wall -Wextra -pedantic -I../include -fprofile-arcs -ftest-coverage
 LDFLAGS=-g -L../build/src -L../build/test --coverage
 LDLIBS=
