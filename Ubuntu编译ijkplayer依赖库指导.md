# Ubuntu 编译 ijkplayer 依赖库指导

## 概述

本文档详细说明了在 Ubuntu 服务器环境下编译 OpenHarmony ijkplayer 依赖库的完整过程，包括环境准备、SDK 配置、编译过程以及常见问题的解决方案。

## 目录

- [环境准备](#环境准备)
- [SDK 配置](#sdk配置)
- [编译依赖库](#编译依赖库)
- [prebuild.sh 使用中的问题与解决方案](#prebuildsh使用中的问题与解决方案)
- [prebuild.sh 修改说明](#prebuildsh修改说明)
- [验证编译结果](#验证编译结果)
- [故障排除](#故障排除)

## 环境准备

### 系统要求

- **操作系统**: Ubuntu 18.04+ 或其他 Linux 发行版
- **架构**: x86_64
- **内存**: 建议 8GB 以上
- **存储**: 至少 20GB 可用空间
- **网络**: 稳定的互联网连接

### 基础工具安装

```bash
# 更新系统包
sudo apt update

# 安装基础编译工具
sudo apt install -y gcc make cmake pkg-config autoconf automake patch libtool
sudo apt install -y wget unzip git python3 python3-pip ninja-build meson
sudo apt install -y build-essential flex bison yasm nasm
```

## SDK 配置

### 1. 下载 OpenHarmony SDK

#### 获取 SDK 直链地址

访问华为开发者官网或使用以下示例链接：

```bash
# 示例：OpenHarmony 4.1 SDK全量包
https://cidownload.openharmony.cn/version/Release_Version/OpenHarmony-********/20250521_154226/version-Release_Version-OpenHarmony-********-20250521_154226-ohos-sdk-full_4.1-Release.tar.gz
```

#### 下载并解压 SDK

```bash
# 下载SDK
wget "SDK直链地址" -O ohos-sdk-full.tar.gz

# 解压到指定目录
mkdir -p /root/ohos-sdk-4.1
tar -xzf ohos-sdk-full.tar.gz -C /root/ohos-sdk-4.1

# 验证SDK结构
ls -la /root/ohos-sdk-4.1/ohos-sdk/linux/11/native/llvm/bin/clang
```

### 2. 配置 SDK 路径

#### 修改 prebuild.sh 中的 SDK 路径

```bash
# 编辑prebuild.sh
vim prebuild.sh

# 修改第20行的SDK_DIR配置
SDK_DIR=/root/ohos-sdk-4.1/ohos-sdk/linux/11
```

#### 设置环境变量

```bash
# 设置SDK环境变量
export OHOS_SDK="/root/ohos-sdk-4.1/ohos-sdk/linux/11"

# 验证SDK可用性
$OHOS_SDK/native/llvm/bin/clang --version
```

### 3. 添加执行权限

```bash
# 给SDK工具添加执行权限
chmod +x /root/ohos-sdk-4.1/ohos-sdk/linux/11/native/llvm/bin/*

# 给编译脚本添加执行权限
chmod +x prebuild.sh
```

## 编译依赖库

### 1. 项目结构

ijkplayer 项目需要编译以下依赖库：

- **FFmpeg**: 基于 B 站的 FFmpeg 版本(ff4.0--ijk0.8.8--20210426--001)
- **soundtouch**: 基于 B 站的 soundtouch 版本(ijk-r0.1.2-dev)
- **libyuv**: 基于 B 站的 yuv 版本(ijk-r0.2.1-dev)
- **OpenSSL**: OpenSSL_1_1_1w 版本

### 2. 执行编译

#### 方法 1：使用 prebuild.sh（推荐）

```bash
# 进入项目目录
cd /root/ohos_ijkplayer-2.0.3

# 设置环境变量
export OHOS_SDK="/root/ohos-sdk-4.1/ohos-sdk/linux/11"

# 执行编译脚本
./prebuild.sh
```

#### 方法 2：手动编译

```bash
# 复制依赖库配置
cp -r doc/libyuv-ijk tpc_c_cplusplus/thirdparty/
cp -r doc/soundtouch-ijk tpc_c_cplusplus/thirdparty/

# 进入lycium目录
cd tpc_c_cplusplus/lycium

# 执行编译
./build.sh FFmpeg-ff4.0 libyuv-ijk soundtouch-ijk
```

### 3. 编译过程

编译过程包括以下阶段：

1. **工具安装**: 安装编译所需的系统工具
2. **下载 lycium 工具链**: 克隆 OpenHarmony 编译工具链
3. **下载源码包**: 下载 FFmpeg、soundtouch、libyuv、OpenSSL 源码
4. **交叉编译**: 使用 OpenHarmony 工具链编译各个库
5. **安装库文件**: 将编译结果复制到项目目录

## prebuild.sh 使用中的问题与解决方案

### 1. 权限问题

#### 问题现象

```bash
./prebuild.sh: Permission denied
```

#### 解决方案

```bash
# 添加执行权限
chmod +x prebuild.sh

# 验证权限
ls -la prebuild.sh
```

### 2. SDK 路径问题

#### 问题现象

```bash
OHOS_SDK 未设置, 请先下载安装ohos SDK, 并设置OHOS_SDK环境变量
```

#### 解决方案

```bash
# 设置正确的SDK环境变量
export OHOS_SDK="/root/ohos-sdk-4.1/ohos-sdk/linux/11"

# 验证SDK路径
echo $OHOS_SDK
ls -la $OHOS_SDK/native/llvm/bin/clang
```

### 3. 架构兼容性问题

#### 问题现象

```bash
cannot execute binary file: Exec format error
```

#### 原因分析

- 下载了 Windows 版 SDK，但在 Linux 环境下使用
- SDK 架构与服务器架构不匹配

#### 解决方案

```bash
# 重新下载Linux版SDK
# 确保下载链接包含 "linux" 而不是 "windows"
wget "Linux版SDK直链" -O ohos-sdk-linux.tar.gz
```

### 4. 源码下载失败

#### 问题现象

```bash
curl: (22) The requested URL returned error: 468
FFmpeg-ff4.0 not ready. wait openssl_1_1_1w
```

#### 原因分析

- Gitee 镜像源不稳定或链接格式错误
- GitHub 直链格式不正确

#### 解决方案

参见[prebuild.sh 修改说明](#prebuildsh修改说明)部分

### 5. 重复下载覆盖问题

#### 问题现象

- 每次执行 prebuild.sh 都重新下载 tpc_c_cplusplus
- 手动下载的源码包被覆盖

#### 解决方案

修改 prebuild.sh 添加检查逻辑（详见修改说明）

### 6. 依赖库配置缺失

#### 问题现象

```bash
Please check the dependencies of these items:
/path/to/soundtouch-ijk
/path/to/libyuv-ijk
```

#### 解决方案

```bash
# 手动复制依赖库配置
cp -r doc/libyuv-ijk tpc_c_cplusplus/thirdparty/
cp -r doc/soundtouch-ijk tpc_c_cplusplus/thirdparty/
```

## prebuild.sh 修改说明

### 1. 避免重复下载 lycium 工具链

#### 修改位置

`prepare_lycium()` 函数中的 git clone 部分

#### 原始代码

```bash
git clone $LYCIUM_TOOLS_URL -b support_x86 --depth=1
```

#### 修改后代码

```bash
# 检查是否已存在tpc_c_cplusplus目录，避免重复克隆覆盖用户下载的源码包
if [ ! -d "tpc_c_cplusplus" ]; then
    echo "正在下载lycium工具链..."
    git clone $LYCIUM_TOOLS_URL -b support_x86 --depth=1
else
    echo "lycium工具链已存在，跳过下载（保护用户的源码包）"
fi
```

#### 修改原因

- 避免每次执行都重新下载覆盖已有的工具链
- 保护用户手动修改的配置文件
- 提高编译效率

### 2. 修改 SDK 路径配置

#### 修改位置

第 19-23 行的 SDK_DIR 配置

#### 原始代码

```bash
SDK_DIR=$ROOT_DIR/../ohos-sdk-$API_VERSION/linux/$API_VERSION
```

#### 修改后代码

```bash
SDK_DIR=/root/ohos-sdk-4.1/ohos-sdk/linux/11                              # SDK路径（实际解压位置）
# 原始配置：SDK_DIR=$ROOT_DIR/../ohos-sdk-$API_VERSION/linux/$API_VERSION
# 如果SDK在其他位置，请修改为实际路径，例如：
# SDK_DIR=/home/<USER>/ohos-sdk/linux/11
```

#### 修改原因

- 适配实际的 SDK 解压位置
- 提供清晰的配置说明
- 避免路径配置错误

### 3. 修改 tpc_c_cplusplus 仓库源码下载配置

#### 修改文件

- `tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD`
- `tpc_c_cplusplus/thirdparty/openssl_1_1_1w/HPKBUILD`

#### FFmpeg 配置修改

**原始配置**:

```bash
# source="https://github.com/bilibili/FFmpeg/archive/refs/tags/ff4.0--ijk0.8.8--20210426--001.tar.gz"
source="https://gitee.com/lycium_pkg_mirror/FFmpeg/repository/archive/ff4.0--ijk0.8.8--20210426--001.zip"
packagename=$builddir.zip
```

**修改后配置**:

```bash
source="https://codeload.github.com/bilibili/FFmpeg/tar.gz/refs/tags/ff4.0--ijk0.8.8--20210426--001"
# 备用源（如果GitHub不可用）：
# source="https://gitee.com/lycium_pkg_mirror/FFmpeg/repository/archive/ff4.0--ijk0.8.8--20210426--001.zip"
packagename=$builddir.tar.gz
```

#### OpenSSL 配置修改

**原始配置**:

```bash
source="https://gitee.com/mirrors/${pkgname:0:7}/repository/archive/$pkgver.zip"
```

**修改后配置**:

```bash
source="https://github.com/openssl/openssl/archive/refs/tags/$pkgver.zip"
# 备用源（如果GitHub不可用）：
# source="https://gitee.com/mirrors/${pkgname:0:7}/repository/archive/$pkgver.zip"
```

#### 修改原因

- 解决 Gitee 镜像源不稳定的问题
- 使用 GitHub 官方源提高下载成功率
- 修正下载链接格式错误（curl 468 错误）
- 保持源码包格式与配置的一致性

### 4. 创建修改版本的 Git 仓库

#### 目的

- 保存修改后的 tpc_c_cplusplus 配置
- 避免重复修改工作
- 便于版本管理和分享

#### 操作步骤

```bash
# 1. 初始化Git仓库
cd tpc_c_cplusplus
git init
git remote add origin https://github.com/用户名/tpc_c_cplusplus-ijkplayer.git

# 2. 提交修改
git add .
git commit -m "修改FFmpeg和OpenSSL下载源为GitHub直链"
git push -u origin main

# 3. 修改prebuild.sh使用新仓库
LYCIUM_TOOLS_URL=https://github.com/用户名/tpc_c_cplusplus-ijkplayer.git
```

## 验证编译结果

### 1. 检查编译输出

```bash
# 检查lycium编译输出
ls -la tpc_c_cplusplus/lycium/usr/
```

应该包含以下目录：

- `FFmpeg-ff4.0/` - FFmpeg 库文件
- `yuv/` - libyuv 库文件
- `soundtouch/` - soundtouch 库文件
- `openssl_1_1_1w/` - OpenSSL 库文件

### 2. 检查项目集成

```bash
# 检查最终的库文件位置
ls -la ijkplayer/src/main/cpp/third_party/
```

应该包含：

- `ffmpeg/ffmpeg/` - FFmpeg 库
- `yuv/` - libyuv 库
- `soundtouch/` - soundtouch 库
- `openssl/` - OpenSSL 库

### 3. 验证库文件架构

```bash
# 检查库文件架构（应该是ARM64）
file ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/lib/libavcodec.a
```

输出应该显示：`ARM aarch64 archive`

## 故障排除

### 1. 编译失败

#### 检查日志

```bash
# 查看详细编译日志
find tpc_c_cplusplus/lycium -name "*.log" | xargs tail -n 50
```

#### 清理重新编译

```bash
# 清理编译缓存
cd tpc_c_cplusplus/lycium
rm -rf usr/*
rm -rf FFmpeg-* openssl-* soundtouch-* yuv-*

# 重新编译
./build.sh FFmpeg-ff4.0 libyuv-ijk soundtouch-ijk
```

### 2. 网络问题

#### 使用代理

```bash
# 设置HTTP代理
export http_proxy=http://proxy:port
export https_proxy=http://proxy:port
```

#### 手动下载源码包

```bash
# 手动下载并放置到正确位置
wget "源码直链" -O "目标文件名"
```

### 3. 磁盘空间不足

#### 检查空间

```bash
df -h
```

#### 清理空间

```bash
# 清理不必要的文件
sudo apt autoremove
sudo apt autoclean
```

## 总结

通过本指导文档，您应该能够：

1. **正确配置 OpenHarmony SDK 环境**
2. **成功编译 ijkplayer 所需的依赖库**
3. **解决编译过程中的常见问题**
4. **理解 prebuild.sh 的修改原理和方法**
5. **创建和维护自定义的编译配置**

编译成功后，您将获得适用于 OpenHarmony 平台的 FFmpeg、soundtouch、libyuv 和 OpenSSL 库文件，可以用于 ijkplayer 项目的后续开发。

## 附录

### 常用命令速查

```bash
# 设置环境变量
export OHOS_SDK="/root/ohos-sdk-4.1/ohos-sdk/linux/11"

# 编译所有依赖库
cd /root/ohos_ijkplayer-2.0.3
./prebuild.sh

# 手动编译特定库
cd tpc_c_cplusplus/lycium
./build.sh FFmpeg-ff4.0 libyuv-ijk soundtouch-ijk

# 检查编译结果
ls -la tpc_c_cplusplus/lycium/usr/
ls -la ijkplayer/src/main/cpp/third_party/

# 清理编译缓存
rm -rf tpc_c_cplusplus/lycium/usr/*
rm -rf tpc_c_cplusplus/lycium/FFmpeg-*
rm -rf tpc_c_cplusplus/lycium/openssl-*
```

### 相关链接

- [OpenHarmony 官方文档](https://docs.openharmony.cn/)
- [ijkplayer 项目地址](https://github.com/bilibili/ijkplayer)
- [FFmpeg 官方网站](https://ffmpeg.org/)
- [OpenSSL 官方网站](https://www.openssl.org/)
