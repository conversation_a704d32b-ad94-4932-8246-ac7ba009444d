{"version": "1.0", "events": [{"head": {"id": "95d3df1e-80f1-4199-8808-6001b02048a1", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 880889822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3af7ec1-6ec1-4a6a-8feb-a103756f5df4", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 880896241300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9eb0335-0076-4181-bd3c-43ca274022d1", "name": "hvigor daemon: Socket will be closed. socketId=13zDKiUXhSWlvg8jAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 880898057600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc34952-925a-4fc5-b5c7-62445c55a253", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":12108,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"de6873857704b4ce74f1c0d1f8334f8f0bba00ff\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753108045864,\"createdBy\":\"deveco\",\"sessionId\":\"0000005074309a7aba4d9cc163951cdd26e958b9ea8d6c2e93b2bef17a8aa2b3546b4e373f0195b00e9e7602eba45a4c72d2f6e60fb8fdcf386b3bbb5137c16f12fcc42a5f933ab9e9386e1946f99953da91e7faa3fb68587049723318ed1201\"}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 880899234100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de42c34b-da95-4e66-8e43-99f8b0dafb02", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 880915610400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca89381e-c882-49ec-8189-fa70bfdef403", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 880915895000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "618bfe22-33d0-43bf-97ab-800110f1596b", "name": "hvigor daemon: Socket is connected. socketId=55CDpVIewpZzd11UAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882060190800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc80f2be-b78d-435c-8534-66575105818e", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"42809c4110d90f682938597528c2556b5103a105\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\onlyreceiver\\\\editVersion\\\\update11\\\\globalstateUse\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5140,\"state\":\"idle\",\"lastUsedTime\":1752805626314,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"0000005035465bbb6f3d5c2c33fe4747ba2a5898fffda2e8e55ad25942bc459b9580a8b060cccfa52a7bd36d99a0b7e4b75bddd3d7c99c1bde55cbdd6b43f8cbd601fcaff5420efe69552e27c20ca2364d8aa9c3b0c8eea4159258819fbe6195\"},{\"pid\":12108,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"de6873857704b4ce74f1c0d1f8334f8f0bba00ff\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753108045880,\"createdBy\":\"deveco\",\"sessionId\":\"0000005074309a7aba4d9cc163951cdd26e958b9ea8d6c2e93b2bef17a8aa2b3546b4e373f0195b00e9e7602eba45a4c72d2f6e60fb8fdcf386b3bbb5137c16f12fcc42a5f933ab9e9386e1946f99953da91e7faa3fb68587049723318ed1201\"}]", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882061332300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b3c7b9c-a709-4f99-8bbd-76cd9f65ee68", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":12108,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"de6873857704b4ce74f1c0d1f8334f8f0bba00ff\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753108045880,\"createdBy\":\"deveco\",\"sessionId\":\"0000005074309a7aba4d9cc163951cdd26e958b9ea8d6c2e93b2bef17a8aa2b3546b4e373f0195b00e9e7602eba45a4c72d2f6e60fb8fdcf386b3bbb5137c16f12fcc42a5f933ab9e9386e1946f99953da91e7faa3fb68587049723318ed1201\"}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882062436500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7143603e-76bf-4c0c-9c06-aa56752be203", "name": "set active socket. socketId=55CDpVIewpZzd11UAAAD", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882067810700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5e850a5-94d4-4ee5-9630-4f3e4f6069fa", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":12108,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"de6873857704b4ce74f1c0d1f8334f8f0bba00ff\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753108047043,\"createdBy\":\"deveco\",\"sessionId\":\"0000005074309a7aba4d9cc163951cdd26e958b9ea8d6c2e93b2bef17a8aa2b3546b4e373f0195b00e9e7602eba45a4c72d2f6e60fb8fdcf386b3bbb5137c16f12fcc42a5f933ab9e9386e1946f99953da91e7faa3fb68587049723318ed1201\"}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882068710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "165748de-3996-4363-a3fb-93845e55a8e3", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=ijkplayer', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882071567900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c16534c5-775d-49c2-b062-9a6336ceb160", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882072135300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b14e82b6-3ccc-4898-b97e-d6e9edc0ab87", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":12108,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"de6873857704b4ce74f1c0d1f8334f8f0bba00ff\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753108047049,\"createdBy\":\"deveco\",\"sessionId\":\"0000005074309a7aba4d9cc163951cdd26e958b9ea8d6c2e93b2bef17a8aa2b3546b4e373f0195b00e9e7602eba45a4c72d2f6e60fb8fdcf386b3bbb5137c16f12fcc42a5f933ab9e9386e1946f99953da91e7faa3fb68587049723318ed1201\"}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882072975500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a89aa575-2759-46ec-885b-ef33a6bad722", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882078040500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e7cf945-f152-413f-a897-3233a6895eaf", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882081174200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5fe61ce-a9b3-4c66-ba53-23fef1195818", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882088364300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c36891a-b697-4107-b5bb-5a6d5825dad3", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882097080300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61187eae-cb35-4b2f-865c-9ea80947312f", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882097129300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "853a3d50-bc62-4a48-9b43-c9eb54dad764", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882103474100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a253d0d-ed29-4801-be2c-309451a5a5ef", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882106498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edbddfd7-0f1c-4a32-85ef-907220f85d36", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882114434500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d2c039-53d6-4a91-a9bb-776342ffefd1", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882114480100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7655913c-4b84-44c5-a027-2d9d63f715f8", "name": "Module entry Collected Dependency: D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882136787700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb491da-e0b8-4f3a-af93-786566aeeedb", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882136828600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0557956d-656b-44b5-b0df-4c78c6ee568a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882141276400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b73218f5-d970-459c-ae05-1622aaf7c116", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882141327500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9285346-dbfd-470f-89d2-24a184175344", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882141414700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c5c964c-7f09-4d95-9d10-45b800977d72", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882141468500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ae3a2d-50ea-4503-bb73-6c6afa2f9004", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882141485000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac49fc16-9042-4a9d-86af-91156caa34f0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882141493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02738f43-a625-4fa0-a29b-f17c02415a24", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882141524800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb5af4b-1ca5-4d26-b04b-a8953f113525", "name": "require SDK: toolchains,ArkTS; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882147748100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "babcf4de-3c5a-4e17-ae3d-1bfec31c639c", "name": "Module entry task initialization takes 7 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882157141000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37d2ab23-49e6-4b65-8656-d4e2cc0a35a2", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882157197200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8da621a3-af63-4af6-876d-9b418203a498", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882161843400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6a46c1-ed5e-4b11-89f7-981d4bc20ae5", "name": "hvigorfile, require result:  { harTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882168424900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1094c213-3e1c-4b09-80fb-252afacf36c5", "name": "hvigorfile, binding system plugins { harTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882168477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e89c578-f1a7-47ea-8d91-33a75479b12d", "name": "<PERSON><PERSON><PERSON> ijkplayer Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882175823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26fad5d2-ad2c-41b6-be44-86827160a933", "name": "<PERSON><PERSON><PERSON> ijkplayer's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882175858600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d6ffa6a-90dc-4c82-9d94-a9fa13d20aa1", "name": "Start initialize module-target build option map, moduleName=ijkplayer, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882177330500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40aa9990-32c2-4f3f-986b-c3788f42086b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882177375400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe77cb97-4a0e-457e-93c4-f90834d6485d", "name": "Module 'ijkplayer' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882177979200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffc70924-8af0-46df-975d-36d8fe93818d", "name": "End initialize module-target build option map, moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882177994100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40ac3f2f-eaab-46c3-949f-c44bf9485d43", "name": "Module 'ijkplayer' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882178024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33cc2d6f-45b0-4fc7-abbc-994b6fca50ef", "name": "require SDK: toolchains,ArkTS,native; moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882179811300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "138f7f97-6a1e-492f-9f66-42ed2e1b432a", "name": "<PERSON><PERSON><PERSON> ijkplayer task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882184639000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1800f887-5c04-4f3a-afa1-713efee3db20", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882185190500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1687392b-f616-430c-a238-130f030d1120", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882185343900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4419d5c8-643c-4041-82c1-5c6e6cb39a07", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882185373000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8796ad04-34e5-424f-acd4-ef12b7b12a4a", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882185419900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c81fc0-da81-44ea-939a-d96595a72ae6", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882185430000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a407a71-dfb7-4316-a589-284646624314", "name": "<PERSON><PERSON><PERSON>_ijkplayer-2.0.3 Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882186495300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc20825c-62f7-4285-89be-df4eebaa3b78", "name": "<PERSON><PERSON><PERSON> ohos_ijkplayer-2.0.3's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882186517300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee26108f-88f0-49f0-940c-3ccfd424e929", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882190118500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93df597-f8ec-4a5e-8bc2-8e463846c636", "name": "Sdk init in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882200692200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b12d16b4-b77b-4d19-897c-0f93b987b5ee", "name": "project has submodules:entry,ijkplayer", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882231645700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "906517bf-8772-4c2d-a142-a49cbbf2501f", "name": "module:ijkplayer no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882234430700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d33d8f-3fec-4a52-a889-8177303610d6", "name": "Project task initialization takes 37 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882237534400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4382c5-1bf9-4c35-a786-95db785835b4", "name": "Sdk init in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882245168700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fc18b2a-f5ae-4f9f-8303-2fc1e89ff7be", "name": "Sdk init in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882257802900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31024cee-1984-4f13-86bc-fd0d90ced014", "name": "Configuration phase cost:178 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882259141000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea533f0c-6b99-4df4-bc3a-22f6ff45c857", "name": "Configuration task cost before running: 185 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882261443900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2e3c060-2852-491d-b713-aa409e944a58", "name": "ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882277707700, "endTime": 882291493200}, "additional": {"children": [], "state": "success", "detailId": "b39fa6aa-9b83-46ed-b918-43c7dd423c1a", "logId": "f279bc1e-2861-4670-95eb-a835330c481b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "b39fa6aa-9b83-46ed-b918-43c7dd423c1a", "name": "create ijkplayer:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882270537400}, "additional": {"logType": "detail", "children": [], "durationId": "b2e3c060-2852-491d-b713-aa409e944a58"}}, {"head": {"id": "1fafc913-c3ba-49bc-89e9-e34ddf0e882c", "name": "ijkplayer : default@PreBuild start {\n  rss: 192049152,\n  heapTotal: 126763008,\n  heapUsed: 102013664,\n  external: 1172769,\n  arrayBuffers: 207413\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882277667400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b864d6c3-82ba-4192-a717-c8c32816772d", "name": "Executing task :ijkplayer:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882277732100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84303e88-64db-425d-b14d-40679a28adbf", "name": "Incremental task ijkplayer:default@PreBuild pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882291217900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f486cc5-5af1-4e4e-ad9a-563954a0fac0", "name": "ijkplayer : default@PreBuild end {\n  rss: 192671744,\n  heapTotal: 127025152,\n  heapUsed: 102384048,\n  external: 1172769,\n  arrayBuffers: 207413\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882291363500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f279bc1e-2861-4670-95eb-a835330c481b", "name": "UP-TO-DATE :ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882277707700, "endTime": 882291493200}, "additional": {"logType": "info", "children": [], "durationId": "b2e3c060-2852-491d-b713-aa409e944a58"}}, {"head": {"id": "43433113-5ce8-4b71-9ca4-daae96895b73", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882296730700, "endTime": 885288929100}, "additional": {"children": ["8d60bcb9-e9b6-4e9d-a160-17c7e032ee13", "2dd56326-f435-42ce-b2cd-5aa1080b13f5"], "state": "success", "detailId": "aa4a8757-d0f0-45ed-8187-959e41524eff", "logId": "7e562726-51cf-4d6c-b0cc-22be5c94a69e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "aa4a8757-d0f0-45ed-8187-959e41524eff", "name": "create ijkplayer:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882295160800}, "additional": {"logType": "detail", "children": [], "durationId": "43433113-5ce8-4b71-9ca4-daae96895b73"}}, {"head": {"id": "e479e701-5f28-4535-85e0-47ce690279ce", "name": "ijkplayer : default@BuildNativeWithCmake start {\n  rss: 192712704,\n  heapTotal: 127025152,\n  heapUsed: 102614352,\n  external: 1172769,\n  arrayBuffers: 207413\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882296681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6101107-63c1-44a9-88c5-e72c048fc825", "name": "Executing task :ijkplayer:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882296752700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fed11665-8ab6-45fd-8b9b-3bdfd5c4f6d1", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882306892800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5edbc76f-9b14-4437-b989-e730d9a6dc6f", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882309425900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d60bcb9-e9b6-4e9d-a160-17c7e032ee13", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 12108, "tid": "Worker0", "startTime": 882539095800, "endTime": 884667257400}, "additional": {"children": [], "state": "success", "parent": "43433113-5ce8-4b71-9ca4-daae96895b73", "logId": "1c35f4e7-a7d6-456f-b037-ca89c8093370"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "0029dfc4-9842-4cbd-805d-9a54dda7a367", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882311352800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73c5836e-9af3-489a-8836-904c845ea9be", "name": "default@BuildNativeWithCmake work[0] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882311443100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0782d2d9-5230-40c2-a721-78c7656441ef", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882314671700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364aedeb-6357-41f4-bf56-21456e30acd5", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882318640500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd56326-f435-42ce-b2cd-5aa1080b13f5", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 12108, "tid": "Worker1", "startTime": 883211467900, "endTime": 885288660600}, "additional": {"children": [], "state": "success", "parent": "43433113-5ce8-4b71-9ca4-daae96895b73", "logId": "55774f50-e5fb-4552-8c16-d25061c3cb22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "a6c44e4a-06d5-446a-9f45-0b559edc82f7", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882319441200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb5e86a2-41e0-4f56-a34d-3a0879de3269", "name": "default@BuildNativeWithCmake work[1] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882319493300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d047903-b93e-4206-8d2e-91640e3a80e5", "name": "ijkplayer : default@BuildNativeWithCmake end {\n  rss: 193708032,\n  heapTotal: 127025152,\n  heapUsed: 103293456,\n  external: 1180961,\n  arrayBuffers: 215605\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882319964000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ece30b-10e2-4778-ad82-7c9be6198d26", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882539193600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63291a76-042a-4e50-ac57-7779f6f5cbf2", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 883211307600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ce19e9-80cf-4d04-881f-bb9f9ef11c5c", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 883211487900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80553b30-0ea7-4ca9-9d2e-75242fe4ae1f", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 884667514300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c35f4e7-a7d6-456f-b037-ca89c8093370", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 12108, "tid": "Worker0", "startTime": 882539095800, "endTime": 884667257400}, "additional": {"logType": "info", "children": [], "durationId": "8d60bcb9-e9b6-4e9d-a160-17c7e032ee13", "parent": "7e562726-51cf-4d6c-b0cc-22be5c94a69e"}}, {"head": {"id": "4e555a66-4023-444e-bf1b-6eb4e0f065d9", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885221193300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94655cb6-b744-4622-8d9d-5335062bfd5f", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885288745600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55774f50-e5fb-4552-8c16-d25061c3cb22", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 12108, "tid": "Worker1", "startTime": 883211467900, "endTime": 885288660600}, "additional": {"logType": "info", "children": [], "durationId": "2dd56326-f435-42ce-b2cd-5aa1080b13f5", "parent": "7e562726-51cf-4d6c-b0cc-22be5c94a69e"}}, {"head": {"id": "7e562726-51cf-4d6c-b0cc-22be5c94a69e", "name": "Finished :ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882296730700, "endTime": 885288929100}, "additional": {"logType": "info", "children": ["1c35f4e7-a7d6-456f-b037-ca89c8093370", "55774f50-e5fb-4552-8c16-d25061c3cb22"], "durationId": "43433113-5ce8-4b71-9ca4-daae96895b73"}}, {"head": {"id": "52ca3d94-b307-488b-b3f3-06f9135f2283", "name": "ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885292146300, "endTime": 885292332900}, "additional": {"children": [], "state": "success", "detailId": "fa21f3ba-6903-4c68-8741-750b59e0f2f2", "logId": "2dc6a7d6-b5f0-4988-a00b-1d7816b33aac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "fa21f3ba-6903-4c68-8741-750b59e0f2f2", "name": "create ijkplayer:compileNative task", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885291963100}, "additional": {"logType": "detail", "children": [], "durationId": "52ca3d94-b307-488b-b3f3-06f9135f2283"}}, {"head": {"id": "62970334-da26-442c-bef9-dd9bdee30850", "name": "ijkplayer : compileNative start {\n  rss: 293847040,\n  heapTotal: 127025152,\n  heapUsed: 103486880,\n  external: 1180961,\n  arrayBuffers: 215605\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885292100200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60cd2b66-6f7c-430e-ab5a-d9b8c6581380", "name": "Executing task :ijkplayer:compileNative", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885292169200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f923170d-a526-4329-99fa-fa1fa909fc58", "name": "ijkplayer : compileNative end {\n  rss: 293851136,\n  heapTotal: 127025152,\n  heapUsed: 103496088,\n  external: 1180961,\n  arrayBuffers: 215605\n}", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885292307200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dc6a7d6-b5f0-4988-a00b-1d7816b33aac", "name": "Finished :ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885292146300, "endTime": 885292332900}, "additional": {"logType": "info", "children": [], "durationId": "52ca3d94-b307-488b-b3f3-06f9135f2283"}}, {"head": {"id": "288ed1e1-e10a-40b7-9723-7157af37ee31", "name": "BUILD SUCCESSFUL in 3 s 217 ms ", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885292888000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "c099e136-096d-4c32-9615-5765fe62d070", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 882076672200, "endTime": 885293531900}, "additional": {"time": {"year": 2025, "month": 7, "day": 21, "hour": 22, "minute": 27}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "4c226c5d-221b-49cb-aebe-206cbf858d08", "name": "There is no need to refresh cache, since the incremental task ijkplayer:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12108, "tid": "Main Thread", "startTime": 885293790400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}