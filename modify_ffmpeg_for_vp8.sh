#!/bin/bash

# FFmpeg VP8 支持配置修改脚本
# 用于在 tpc_c_cplusplus 下载后修改 FFmpeg 配置以支持 VP8

set -e

echo "=== FFmpeg VP8 支持配置修改脚本 ==="

FFMPEG_HPKBUILD="tpc_c_cplusplus/thirdparty/FFmpeg-ff4.0/HPKBUILD"

if [ ! -f "$FFMPEG_HPKBUILD" ]; then
    echo "❌ 错误：FFmpeg 配置文件未找到: $FFMPEG_HPKBUILD"
    echo "请确保 tpc_c_cplusplus 已正确下载"
    exit 1
fi

echo "✅ 找到 FFmpeg 配置文件: $FFMPEG_HPKBUILD"

# 备份原始文件
cp "$FFMPEG_HPKBUILD" "$FFMPEG_HPKBUILD.backup"
echo "✅ 已备份原始配置文件"

# 显示修改前的配置
echo "=== 修改前的配置 ==="
echo "依赖关系:"
grep "depends=" "$FFMPEG_HPKBUILD" || echo "未找到 depends 行"
echo "编译选项:"
grep "enable-libopenh264" "$FFMPEG_HPKBUILD" || echo "未找到 libopenh264 配置"

# 1. 添加 libvpx 依赖
echo "=== 修改 1: 添加 libvpx 依赖 ==="
# 使用更简单的方法
sed -i 's|"openh264")|"openh264" "libvpx")|g' "$FFMPEG_HPKBUILD"
echo "✅ 已添加 libvpx 依赖"

# 2. 添加 VP8 编译选项
echo "=== 修改 2: 添加 VP8 编译选项 ==="
# 在 --enable-libopenh264 后添加 VP8 选项
sed -i 's|--enable-libopenh264|--enable-libopenh264 --enable-decoder=vp8 --enable-parser=vp8 --enable-libvpx --enable-libvpx-vp8-decoder|g' "$FFMPEG_HPKBUILD"
echo "✅ 已添加 VP8 编译选项"

# 显示修改后的配置
echo "=== 修改后的配置 ==="
echo "依赖关系:"
grep "depends=" "$FFMPEG_HPKBUILD"
echo "编译选项:"
grep -E "(enable-decoder=vp8|enable-libvpx)" "$FFMPEG_HPKBUILD" || echo "未找到 VP8 相关配置"

# 验证修改
echo "=== 验证修改结果 ==="
if grep -q "libvpx" "$FFMPEG_HPKBUILD"; then
    echo "✅ libvpx 依赖已添加"
else
    echo "❌ libvpx 依赖添加失败"
fi

if grep -q "enable-decoder=vp8" "$FFMPEG_HPKBUILD"; then
    echo "✅ VP8 解码器已启用"
else
    echo "❌ VP8 解码器启用失败"
fi

if grep -q "enable-libvpx" "$FFMPEG_HPKBUILD"; then
    echo "✅ libvpx 库支持已启用"
else
    echo "❌ libvpx 库支持启用失败"
fi

echo "=== FFmpeg VP8 配置修改完成 ==="

# 显示完整的修改后文件（用于调试）
echo "=== 完整的修改后配置文件 ==="
cat "$FFMPEG_HPKBUILD"
