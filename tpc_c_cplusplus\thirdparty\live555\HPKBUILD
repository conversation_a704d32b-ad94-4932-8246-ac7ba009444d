# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=live555
pkgver=master 
pkgrel=0 
pkgdesc="Skype Silk Codec SDK]Decode silk v3 audio files (like wechat amr, aud files, qq slk files) and convert to other format (like mp3). Batch conversion support."
url="https://github.com/rgaufman/live555" 
archs=("arm64-v8a" "armeabi-v7a") # cpu 架构
license=("LGPL-3.0, GPL-3.0 licenses found")
source="https://github.com/rgaufman/${pkgname}.git"
commitid=2c92a57ca04b83b2038ab2ab701d05a54be06a85
autounpack=false
downloadpackage=false 
buildtools="make"
depends=("openssl")

builddir=$pkgname-${commitid}
packagename=
cloneflag=true

prepare() {
    if [ $cloneflag == true ]
    then
        mkdir $builddir
        git clone -b $pkgver $source $builddir
        if [ $? -ne 0 ]
        then
            return -1
        fi
        cd $builddir
        git reset --hard $commitid
        if [ $? -ne 0 ]
        then
            return -2
        fi
        cd $OLDPWD
        cloneflag=false
    fi

    mkdir -p $ARCH-build
    cp $builddir/* $ARCH-build -rf

    if [ $ARCH == "armeabi-v7a" ]; then
        cp -f config.armeabi-v7a $ARCH-build
    elif [ $ARCH == "arm64-v8a" ]; then
        cp -f config.arm64-v8a $ARCH-build
    else
        echo "Not support ${ARCH} yet"
        return -1
    fi
}

build() {
    cd $ARCH-build/
    if [ $ARCH == "armeabi-v7a" ]; then
        ./genMakefiles armeabi-v7a
    elif [ $ARCH == "arm64-v8a" ]; then
        ./genMakefiles arm64-v8a
    else
        echo "Not support ${ARCH} yet"
        return -1
    fi

    $MAKE VERBOSE=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

# 安装打包
package() {
    cd $ARCH-build/
    make DESTDIR=${LYCIUM_ROOT} PREFIX=/usr/${pkgname}/${ARCH} LIBDIR=/usr/${pkgname}/${ARCH}/lib install >> $buildlog 2>&1
    cd $OLDPWD
}

# 测试，需要在 ohos 设备上进行
check() {
    echo "The test must be on an OpenHarmony device!"
    if [ $ARCH == "armeabi-v7a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/arm-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    elif [ $ARCH == "arm64-v8a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/aarch64-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    else
        echo "${ARCH} not support"
        return -1
    fi
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/${ARCH}-build ${PWD}/${ARCH}-build ${PWD}/$builddir
}