[binaries]
c = 'ohos_sdk/native/llvm/bin/aarch64-linux-ohos-clang'
cpp = 'ohos_sdk/native/llvm/bin/aarch64-linux-ohos-clang++'
ar = 'ohos_sdk/native/llvm/bin/llvm-ar'
strip = 'ohos_sdk/native/llvm/bin/llvm-strip'
ld = 'ohos_sdk/native/llvm/bin/ld.lld'

pkgconfig = '/usr/bin/pkg-config'

[host_machine]
system = 'linux'
cpu_family = 'aarch64'
cpu = 'arm64-v8a'
endian = 'little'

[properties]
needs_exe_wrapper = true
skip_sanity_check = true
sys_root = ''
platform = 'generic'
pkg_config_libdir = 'lycium_root/usr/json-c/arm64-v8a/lib/pkgconfig:lycium_root/usr/speexdsp/arm64-v8a/lib/pkgconfig:lycium_root/usr/libatomic_ops/arm64-v8a/lib/pkgconfig:lycium_root/usr/libsndfile/arm64-v8a/lib/pkgconfig:lycium_root/usr/lib/arm64-v8a/lib/pkgconfig:lycium_root/usr/fftw3/arm64-v8a/lib/pkgconfig:lycium_root/usr/check/arm64-v8a/lib/pkgconfig'


[built-in options]
c_args = [
    '-D__MUSL__=1',
    '-Wno-int-conversion',
    '-march=armv8a',
    '-mfpu=neon',
    '-Ilycium_root/usr/gettext/arm64-v8a/include',
    '-mfloat-abi=softfp',
    ]
cpp_args = [
    '-D__MUSL__=1',
    '-Wno-int-conversion',
    '-march=armv8a',
    '-mfpu=neon',
    '-Ilycium_root/usr/gettext/arm64-v8a/include',
    '-mfloat-abi=softfp',
    ]

c_link_args = [
    '-Llycium_root/usr/gettext/arm64-v8a/lib', 
    '-lintl',
    ]
cpp_link_args = [
    '-Llycium_root/usr/gettext/arm64-v8a/lib', 
    '-lintl',
    ]


