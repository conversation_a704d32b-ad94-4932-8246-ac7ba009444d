# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer:  <PERSON> <<EMAIL>>

pkgname=kaldi
pkgver=kaldi10
pkgrel=0
pkgdesc="Kaldi is a toolkit for speech recognition, intended for use by speech recognition researchers and professionals."
url="http://www.kaldi-asr.org/"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache License v2.0")
depends=("OpenBLAS" "clapack")
makedepends=("make" "automake" "autoconf" "patch" "grep" "bzip2" "gzip" "unzip" "wget" "git" "sox" "gfortran" "libtoolize" "svn" "awk")
# 官网地址：https://github.com/kaldi-asr/pkgname/archive/refs/heads/pkgver.tar.gz，因网络原因采用gitee mirrors
source="https://gitee.com/mirrors/$pkgname/repository/archive/$pkgver.zip"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.zip
patchflag=true
host=
target=
downloaddepsflag=true

source envset.sh

prepare() {
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
        target="arm-v7a"
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
        target="aarch64-v8a"
    else
        echo "$ARCH is not supported"
        return -1
    fi

    if $patchflag
    then
        cd $builddir
        # 1. 库使用uname的方式来判断平台，需要添加编译参数来指定平台--target
        # 2. 由于没有gfortran工具，需要替换OpenBLAS的数学库，由于拆分依赖库需要指定clapack库的目录, 新增编译参数--clapack-lib
        # 3. decoder依赖的库路径有误，需要修正
        # 4. 编译测试用例时会执行测试用例，需要去除
        # 5. 尚不支持HAVE_EXECINFO_H,需要相应的头文件
        # 6. 以-O2的方式进行编译，去掉-g选项
        # 7. 测试用例代码中有错误，重复定义变量:trans_model
        # 8. openfst 交叉编译需要指定host，做出相应修改
        patch -p1 < $PKGBUILD_ROOT/kaldi_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi

    if $downloaddepsflag
    then
        # 下载依赖包
        sure download "https://gitee.com/lycium_pkg_mirror/kaldi-deps/repository/archive/v0.1.zip" kaldi-deps-v0.1.zip
        unzip kaldi-deps-v0.1.zip
        downloaddepsflag=false
    fi

    cp -rf $builddir $builddir-$ARCH-build
}

build() {
    cd $builddir-$ARCH-build/tools
    # 编译内部依赖库
    make -j4 HOST=$host DOWNLOAD_DIR=$PKGBUILD_ROOT/kaldi-deps-v0.1 openfst cub VERBOSE=1 > `pwd`/../build.log 2>&1

    cd $PKGBUILD_ROOT/$builddir-$ARCH-build/src
    # --use-cuda不允许跨平台编译
    ./configure --static --target=$target --use-cuda=no --clapack-lib="$LYCIUM_ROOT/usr/clapack/$ARCH/lib" \
        --openblas-root=$LYCIUM_ROOT/usr/OpenBLAS/$ARCH --fst-root=$PKGBUILD_ROOT/$builddir-$ARCH-build/tools/openfst-1.6.7 >> `pwd`/../build.log 2>&1

    # 处理编译依赖配置
    make depend -j4 VERBOSE=1 >> `pwd`/../build.log 2>&1
    ret=$?
    if [ $ret -ne 0 ]
    then
        return $ret
    fi
    
    # 开始编译库文件
    make -j4 VERBOSE=1 >> `pwd`/../build.log 2>&1
    ret=$?
    if [ $ret -ne 0 ]
    then
        return $ret
    fi

    # 编译测试用例
    make test -j4 VERBOSE=1 >> `pwd`/../build.log 2>&1
    ret=$?
    cd $PKGBUILD_ROOT
    return $ret
}

package() {
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib $LYCIUM_ROOT/usr/$pkgname/$ARCH/include $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin
    cd $builddir-$ARCH-build/src

    # 安装头文件, 保持目录结构
    cp --parents $(find . -name *.h) $LYCIUM_ROOT/usr/$pkgname/$ARCH/include

    # 安装库文件
    cp $(find . -name *.a) $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib

    # 安装可执行程序
    cp --parents $(find . -executable -type f -not -path *.sh -not -path ./configure -not -path *-test) $LYCIUM_ROOT/usr/$pkgname/$ARCH/bin

    cd $OLDPWD
    unset target host
}

check() {
    # 复制libc++_shared.so
    if [ $ARCH == "armeabi-v7a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/arm-linux-ohos/libc++_shared.so $builddir-$ARCH-build/
    elif [ $ARCH == "arm64-v8a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/aarch64-linux-ohos/libc++_shared.so $builddir-$ARCH-build/
    else
        echo "$ARCH is not supported"
        return -1
    fi

    cd $builddir-$ARCH-build
    # 1. 注释掉因数学库原因测试不通过的用例
    patch -p1 < $PKGBUILD_ROOT/kaldi_oh_test.patch
    cd $OLDPWD

    echo "The test must be on an OpenHarmony device!"
    # make test
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir ${PWD}/$builddir-armeabi-v7a-build ${PWD}/$builddir-arm64-v8a-build ${PWD}/kaldi-deps-v0.1 # ${PWD}/$packagename
}
