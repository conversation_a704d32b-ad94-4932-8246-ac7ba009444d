@echo off
setlocal enabledelayedexpansion
echo === 验证当前编译结果 ===
echo.

set FFMPEG_DIR=ijkplayer\src\main\cpp\third_party\ffmpeg

if not exist "%FFMPEG_DIR%" (
    echo ❌ FFmpeg 目录不存在: %FFMPEG_DIR%
    pause
    exit /b 1
)

echo ✅ FFmpeg 目录存在: %FFMPEG_DIR%
echo.

echo === 详细检查各架构的库文件 ===
for %%a in (armeabi-v7a arm64-v8a x86_64) do (
    echo.
    echo --- 检查架构: %%a ---
    set ARCH_DIR=%FFMPEG_DIR%\%%a
    set LIB_DIR=!ARCH_DIR!\lib
    set LIBAVCODEC=!LIB_DIR!\libavcodec.a
    
    if exist "!ARCH_DIR!" (
        echo ✅ 架构目录存在: !ARCH_DIR!
        
        if exist "!LIB_DIR!" (
            echo ✅ lib 目录存在: !LIB_DIR!
            
            if exist "!LIBAVCODEC!" (
                echo ✅ libavcodec.a 存在: !LIBAVCODEC!
                
                REM 获取文件大小
                for %%f in ("!LIBAVCODEC!") do (
                    set /a size_mb=%%~zf/1024/1024
                    echo 📦 文件大小: %%~zf 字节 (~!size_mb! MB^)
                )
                
                REM 检查是否包含解码器符号
                echo 🔍 检查解码器符号...
                findstr /i "ff_.*_decoder" "!LIBAVCODEC!" | findstr /i "vp8" >nul 2>&1
                if !errorlevel! equ 0 (
                    echo ✅ 发现 VP8 解码器符号
                ) else (
                    echo ❌ 未发现 VP8 解码器符号
                )
                
                REM 检查是否包含 VP8 字符串
                findstr /i "vp8" "!LIBAVCODEC!" >nul 2>&1
                if !errorlevel! equ 0 (
                    echo ✅ 发现 VP8 相关字符串
                ) else (
                    echo ❌ 未发现 VP8 相关字符串
                )
                
            ) else (
                echo ❌ libavcodec.a 不存在: !LIBAVCODEC!
            )
        ) else (
            echo ❌ lib 目录不存在: !LIB_DIR!
        )
    ) else (
        echo ❌ 架构目录不存在: !ARCH_DIR!
    )
)

echo.
echo === 检查其他重要库文件 ===
for %%a in (armeabi-v7a arm64-v8a x86_64) do (
    echo.
    echo 检查 %%a 架构的其他库:
    set LIB_DIR=%FFMPEG_DIR%\%%a\lib
    
    if exist "!LIB_DIR!" (
        for %%l in (libavformat.a libavutil.a libswscale.a) do (
            if exist "!LIB_DIR!\%%l" (
                echo ✅ %%l 存在
            ) else (
                echo ❌ %%l 不存在
            )
        )
    )
)

echo.
echo === 对比分析 ===
echo 如果所有架构的 libavcodec.a 大小都相似且较小（比如 < 10MB），
echo 可能说明 VP8 支持没有编译进去。
echo.
echo 典型的大小参考：
echo - 不包含 VP8: 约 8-12 MB
echo - 包含 VP8: 约 12-16 MB
echo.

echo === 建议的下一步 ===
echo 1. 如果文件大小偏小且未发现 VP8 符号，说明需要重新编译
echo 2. 检查 GitHub Actions 的编译日志，确认配置修改是否生效
echo 3. 考虑手动修改配置文件后重新编译
echo.

pause
