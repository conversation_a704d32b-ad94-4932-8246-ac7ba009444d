# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=clip2tri
pkgver=master
pkgrel=0
pkgdesc="Using clipper and poly2tri together for robust triangulation"
url="https://github.com/raptor/clip2tri"
archs=("armeabi-v7a" "arm64-v8a")
license=("The MIT License")
depends=()
makedepends=()

source="https://github.com/raptor/$pkgname.git"
commitid=f62a734d22733814b8a970ed8a68a4d94c24fa5f

autounpack=false
downloadpackage=false

builddir=$pkgname-${pkgver}
download_and_patch_flag=true

prepare() {
    if [ "$download_and_patch_flag" == true ]
    then
        git clone -b $pkgver $source $builddir > $publicbuildlog 2>&1
	if [ $? -ne 0 ]
        then
	  return -1
        fi
        cd $builddir
        git reset --hard $commitid > $publicbuildlog 2>&1
	if [ $? -ne 0 ]
        then
	  cd $OLDPWD
	  return -2
        fi
	patch -p1 < ../clip2tri_ohos_test.patch #添加测试用例，未对原库做改动
        cd $OLDPWD
        download_and_patch_flag=false
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    mkdir -p ${LYCIUM_ROOT}/usr/$pkgname/$ARCH/include
    mkdir -p ${LYCIUM_ROOT}/usr/$pkgname/$ARCH/include/clipper
    cp clipper/*.hpp ${LYCIUM_ROOT}/usr/$pkgname/$ARCH/include/clipper
    $MAKE VERBOSE=1 -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
