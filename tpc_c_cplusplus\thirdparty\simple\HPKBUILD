# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, DZD <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=simple
pkgver=v0.4.0
pkgrel=0
pkgdesc=""
url="https://github.com/wangfenjin/simple"
archs=("arm64-v8a" "armeabi-v7a")
license=("MIT License")
depends=()
makedepends=()
install=
source="https://codeload.github.com/wangfenjin/simple/zip/refs/tags/v0.4.0"
autounpack=false
downloadpackage=false
builddir=$pkgname
packagename=
host=
cloneFlag=true
patchFlag=false
source envset.sh

prepare() {
    if $cloneFlag
    then
        git clone --branch $pkgver https://github.com/wangfenjin/$pkgname.git > $publicbuildlog 2>&1
	ret=$?
	if [ $ret -ne 0 ]; then
	    return $ret
	fi
	cd $builddir
	cloneFlag=false
        cd $OLDPWD
    fi
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@"  -DCMAKE_C_FLAGS=-Wno-unused-command-line-argument \
    -DCMAKE_CXX_FLAGS=-Wno-unused-command-line-argument -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > $buildlog 2>&1
    $MAKE -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    unset host
    echo "Test MUST on OpenHarmony device!"
}

cleanbuild(){
    rm -rf ${PWD}/$builddir
}
