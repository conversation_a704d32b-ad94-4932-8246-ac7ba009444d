# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=gdbm
pkgver=1.23
pkgrel=0
pkgdesc="GNU dbm (or GDBM, for short) is a library of database functions that use extensible hashing and work similar to the standard UNIX dbm."
url="https://www.gnu.org.ua/software/gdbm"
archs=("armeabi-v7a" "arm64-v8a")
license=("GPLv3")
depends=()
makedepends=()
source="https://ftp.gnu.org/gnu/$pkgname/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz

source envset.sh
host=
prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    else
        echo "${ARCH} not support"
	return -1
    fi
}

build() {
    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host > `pwd`/build.log 2>&1
    make V=1 -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    unset host
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    else
        echo "${ARCH} not support"
	return -1
    fi
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # TODO
}

cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packageName
}
