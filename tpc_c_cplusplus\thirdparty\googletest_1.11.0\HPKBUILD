# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <EMAIL>
# Maintainer: <EMAIL>

pkgname=googletest_1.11.0
pkgver=release-1.11.0
pkgrel=0
pkgdesc="GoogleTest is Google’s C++ testing and mocking framework"
url="https://github.com/google/googletest"
archs=("armeabi-v7a" "arm64-v8a")
license=("BSD-3-Clause license")
source="https://gitee.com/mirrors/${pkgname:0:10}/repository/archive/$pkgver.zip" 

autounpack=true
downloadpackage=true

builddir=${pkgname:0:10}-${pkgver}
packagename=${builddir}.zip

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DCMAKE_CXX_FLAGS="-Wno-error=unused-command-line-argument -Wno-error=unused-but-set-variable" -DCMAKE_FLAGS="-Wno-unused-command-line-argument -Wno-unused-but-set-variable" -DBUILD_GMOCK=ON -Dgtest_build_tests=ON -Dgmock_build_tests=ON -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE -C $ARCH-build VERBOSE=1 >> $buildlog 2>&1
    # 对最关键一步的退出码进行判断
    ret=$?
    cd $OLDPWD
    return $ret
}

# 安装打包
package() {
    cd $builddir
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
