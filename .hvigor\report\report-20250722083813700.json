{"version": "1.0", "events": [{"head": {"id": "c4c645b7-a5bb-4c23-9a71-016436d296f7", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 575745596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f82b50-2d73-4ca6-bda7-7049bf1a891b", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 575760348300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81bdddc2-385f-42a2-b878-8e7b6fac91ac", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 575760647200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "047e934b-3ecd-46b9-a1e4-fec0d41cbfc8", "name": "hvigor disconnect:  transport close", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 575761814400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b35f7c7-fc26-410b-b78f-91995d8e9069", "name": "hvigor daemon: Socket will be closed. socketId=Opsv7M1MiRES00gdAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 575762675400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6e543b1-bc0c-4a4b-8b15-4e461eeef258", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2128,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"7f593f7d063ab29472c464a73e9647bf74b68398\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753144690721,\"createdBy\":\"deveco\",\"sessionId\":\"00000050cfbf6772f0477443650070505343419936d7ce4f8950a94eaed726ee8ee3b3e512552b4df3c3636106237ea6b8bafd8f3a98781e18e7f3d38de17c4673758ca85ea8ace7a056c938ae1ddd99ea6f61be6d625b83998c0ea0bf557f08\"}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 575763269000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36a3c98a-cb19-4ab2-ab4c-03c309a6e560", "name": "hvigor daemon: Socket is connected. socketId=MU0E0hVdWvuPJklKAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576858699400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c52bc3e6-04ac-4a8c-902e-68c135ae452c", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"42809c4110d90f682938597528c2556b5103a105\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\onlyreceiver\\\\editVersion\\\\update11\\\\globalstateUse\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5140,\"state\":\"idle\",\"lastUsedTime\":1752805626314,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"0000005035465bbb6f3d5c2c33fe4747ba2a5898fffda2e8e55ad25942bc459b9580a8b060cccfa52a7bd36d99a0b7e4b75bddd3d7c99c1bde55cbdd6b43f8cbd601fcaff5420efe69552e27c20ca2364d8aa9c3b0c8eea4159258819fbe6195\"},{\"keyId\":\"de6873857704b4ce74f1c0d1f8334f8f0bba00ff\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":12108,\"state\":\"stopped\",\"lastUsedTime\":1753108050354,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005074309a7aba4d9cc163951cdd26e958b9ea8d6c2e93b2bef17a8aa2b3546b4e373f0195b00e9e7602eba45a4c72d2f6e60fb8fdcf386b3bbb5137c16f12fcc42a5f933ab9e9386e1946f99953da91e7faa3fb68587049723318ed1201\"},{\"pid\":2128,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"7f593f7d063ab29472c464a73e9647bf74b68398\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753144690742,\"createdBy\":\"deveco\",\"sessionId\":\"00000050cfbf6772f0477443650070505343419936d7ce4f8950a94eaed726ee8ee3b3e512552b4df3c3636106237ea6b8bafd8f3a98781e18e7f3d38de17c4673758ca85ea8ace7a056c938ae1ddd99ea6f61be6d625b83998c0ea0bf557f08\"}]", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576859841500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718241d5-e434-4f5f-b9c5-c875bc44750a", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2128,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"7f593f7d063ab29472c464a73e9647bf74b68398\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753144690742,\"createdBy\":\"deveco\",\"sessionId\":\"00000050cfbf6772f0477443650070505343419936d7ce4f8950a94eaed726ee8ee3b3e512552b4df3c3636106237ea6b8bafd8f3a98781e18e7f3d38de17c4673758ca85ea8ace7a056c938ae1ddd99ea6f61be6d625b83998c0ea0bf557f08\"}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576860886100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f45f1b-3f83-4526-8f75-c25a49fc544e", "name": "set active socket. socketId=MU0E0hVdWvuPJklKAAAD", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576866630200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a96298a-7a13-4b72-99d2-5675534eecdb", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2128,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"7f593f7d063ab29472c464a73e9647bf74b68398\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753144691840,\"createdBy\":\"deveco\",\"sessionId\":\"00000050cfbf6772f0477443650070505343419936d7ce4f8950a94eaed726ee8ee3b3e512552b4df3c3636106237ea6b8bafd8f3a98781e18e7f3d38de17c4673758ca85ea8ace7a056c938ae1ddd99ea6f61be6d625b83998c0ea0bf557f08\"}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576867537700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed600b33-e639-4d28-95b8-f14e18872bc3", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=ijkplayer', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576870114500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1ebc814-fb3c-4507-96d3-ec9cf32de4a8", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576870552200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdadf958-4ffe-4235-a62d-4cd83f334d90", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2128,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\new\\\\ohos_ijkplayer-2.0.3\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"7f593f7d063ab29472c464a73e9647bf74b68398\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1753144691847,\"createdBy\":\"deveco\",\"sessionId\":\"00000050cfbf6772f0477443650070505343419936d7ce4f8950a94eaed726ee8ee3b3e512552b4df3c3636106237ea6b8bafd8f3a98781e18e7f3d38de17c4673758ca85ea8ace7a056c938ae1ddd99ea6f61be6d625b83998c0ea0bf557f08\"}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576871131000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db95f9c-0cb4-411f-878e-ccd728f133ea", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576874950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e90ce1-158c-4c90-9e01-c8484b63ea0e", "name": "Cache service initialization finished in 2 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576876692100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a79ab33a-120e-49c1-a46d-c5aebf6f5b08", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576881851000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57c229a5-02cb-4137-a9dd-d9832eabf795", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576887727200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "114066e9-4b66-4a5b-b429-77ac4401d3fa", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576887771300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12eafa33-8c80-4ad6-9b69-cd79ac3f647e", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576893710400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "041b1035-e917-4b05-8d09-c6b63eae757d", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576897007700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "553f9326-23f6-4e14-8bbc-d111521af83b", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576904437600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3248ec80-5813-4dc4-b3db-b2512cbc274c", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576904486500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee8c59a8-dce9-4e68-bdad-8d8ade29febc", "name": "Module entry Collected Dependency: D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576917991100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42009bff-4e67-4dd3-92d0-6267babeb17c", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576918102600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38fdd256-6820-4259-b975-b3bb04260202", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576921749200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17c52e79-bcc4-47b3-bb08-a1e6d5c920cf", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576921791200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318e3269-ca76-40ce-a349-85c7bb463892", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576921857500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c749998-6f0b-443c-b1d9-9f015d10bde9", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576921889500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3cc1269-cd55-4de8-8580-4f83c82e5c6d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576921898800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b546cf0-f0c4-4864-bf51-b047c0445a5e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576921905900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9f642b7-2b11-45f1-b3de-ad04610beaf2", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576921942200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b290ad8a-f2ae-4287-987d-28eabe891535", "name": "require SDK: toolchains,ArkTS; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576923561300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c99939d-9ede-4a76-b3ba-b84206293e75", "name": "Module entry task initialization takes 4 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576929563600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7090fa2-eee8-440f-8833-c6ac7f4dbccf", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576929615900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005b63ec-81d6-4026-97d2-51e54e6cb90d", "name": "hvigorfile, resolving D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576932459800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aadb2927-d652-434e-b60f-229d24990f5e", "name": "hvigorfile, require result:  { harTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576938421600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b19c53d-477a-45cd-b737-bc32bedc1bbf", "name": "hvigorfile, binding system plugins { harTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576938471100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "babe1eba-d7d7-4e6c-9095-ea4dcc3ae20a", "name": "<PERSON><PERSON><PERSON> ijkplayer Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576946884200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8b7e76-5f83-43fb-a51d-f37a1010c1d3", "name": "<PERSON><PERSON><PERSON> ijkplayer's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576946920500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0e54532-1c5f-439a-a6c6-ebe5d55ccdac", "name": "Start initialize module-target build option map, moduleName=ijkplayer, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576948117700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac1a865b-d652-4ac1-b8e3-155767d54090", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576948166600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c871b19-01a7-449e-adb0-1b7277312fe0", "name": "Module 'ijkplayer' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576948733100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3830274f-0ec7-4404-8069-0eee8381f70d", "name": "End initialize module-target build option map, moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576948749900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d51e19-96a3-4d39-a400-d3e78c19dc2a", "name": "Module 'ijkplayer' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"napiLibFilterOption\": {\n    \"enableOverride\": true\n  },\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS='-s' -DCMAKE_CXX_FLAGS='-s'\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576948784400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3ef808-33f0-4b9a-9f11-b10fed6182c7", "name": "require SDK: toolchains,ArkTS,native; moduleName=ijkplayer", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576950242700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d16812da-9342-42ba-a476-71d294d4be93", "name": "<PERSON><PERSON><PERSON> ijkplayer task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576953933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc5b945-4675-4856-ab03-55373b578bbc", "name": "hvigorfile, resolve finished D:\\new\\ohos_ijkplayer-2.0.3\\ijkplayer\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576954431600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4067134-e27f-4617-a89f-0a7539e29d55", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576954511900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a64d0c44-692d-4fa7-adea-553004869aab", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576954532600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c0b01e7-a5fd-41eb-bf8f-abd97a93b762", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576954572200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f3dcc5-48cd-4da7-82a3-2180c02e6368", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576954582600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae6969f-4234-49b3-bc22-03504fe979f2", "name": "<PERSON><PERSON><PERSON>_ijkplayer-2.0.3 Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576955218600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d88be075-eb21-4474-a509-b17557fc0b22", "name": "<PERSON><PERSON><PERSON> ohos_ijkplayer-2.0.3's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576955237700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f5ca42d-46c6-4362-8f5b-329afec2fa59", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576957459800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08cbdefc-c7d8-437b-bdb2-2749fdf33265", "name": "Sdk init in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576966585500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d1c4e88-4e02-411f-8a75-b739ee24b209", "name": "project has submodules:entry,ijkplayer", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576987859800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aae50b15-d655-47fe-bd6e-d54404b100ba", "name": "module:ijkplayer no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576990715200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96f67f69-a669-4a84-bc74-50254e1a6d2b", "name": "Project task initialization takes 28 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576994661000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5959ad5-7693-4124-9160-0e20bde664d3", "name": "Sdk init in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577000633900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833f5382-8961-412d-b37b-fbd7ad2bd09b", "name": "Sdk init in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577006872600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a68e2dc-9ef8-4897-8a30-d664ed0951b9", "name": "Configuration phase cost:131 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577007683800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6013fd2-2ba3-4539-8ed0-53e4d04ecec5", "name": "Configuration task cost before running: 136 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577009493500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cc04095-6c94-4158-b65d-766a031b9632", "name": "ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577017069500, "endTime": 577025068200}, "additional": {"children": [], "state": "success", "detailId": "19388c67-0b70-4a41-b0db-38f599ce77c9", "logId": "7523d477-c7e3-4a34-aea2-c57d8fbff175"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "19388c67-0b70-4a41-b0db-38f599ce77c9", "name": "create ijkplayer:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577011795400}, "additional": {"logType": "detail", "children": [], "durationId": "8cc04095-6c94-4158-b65d-766a031b9632"}}, {"head": {"id": "b6fe2d87-d995-4799-822b-18ac31bf7d80", "name": "ijkplayer : default@PreBuild start {\n  rss: 189214720,\n  heapTotal: 127025152,\n  heapUsed: 101955976,\n  external: 1189145,\n  arrayBuffers: 223789\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577017030900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff6c34c-fcdb-43ae-852c-5791e527b03e", "name": "Executing task :ijkplayer:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577017087800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "835184f4-4d31-4b2c-bf9f-2804e187f408", "name": "Incremental task ijkplayer:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577024888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4114bc38-cfff-4689-913d-3a53f1b49dcd", "name": "ijkplayer : default@PreBuild end {\n  rss: 190148608,\n  heapTotal: 127025152,\n  heapUsed: 102077872,\n  external: 1189145,\n  arrayBuffers: 223789\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577024983200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7523d477-c7e3-4a34-aea2-c57d8fbff175", "name": "UP-TO-DATE :ijkplayer:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577017069500, "endTime": 577025068200}, "additional": {"logType": "info", "children": [], "durationId": "8cc04095-6c94-4158-b65d-766a031b9632"}}, {"head": {"id": "db4b3267-6b46-4af4-8461-cab70c6cb8b6", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577028285700, "endTime": 578704188400}, "additional": {"children": ["9388f215-eec5-46f3-b5a7-13e0670a9b09", "c6cbcba5-2428-4cc4-9926-48d0eabd555c"], "state": "success", "detailId": "42826d31-b093-4149-81e9-2458226f31db", "logId": "793a5b04-dff8-4468-82f8-026dbda331d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "42826d31-b093-4149-81e9-2458226f31db", "name": "create ijkplayer:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577027393100}, "additional": {"logType": "detail", "children": [], "durationId": "db4b3267-6b46-4af4-8461-cab70c6cb8b6"}}, {"head": {"id": "1d030e59-97a3-4e8a-ad4c-bdafe518b89f", "name": "ijkplayer : default@BuildNativeWithCmake start {\n  rss: 190529536,\n  heapTotal: 127025152,\n  heapUsed: 102308168,\n  external: 1189145,\n  arrayBuffers: 223789\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577028261900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af85410e-a177-405f-870f-17d40c0c6f94", "name": "Executing task :ijkplayer:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577028299500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc13c85c-a16c-44be-90be-a1b336a4d9c1", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577039842000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4394637-d86a-4d22-adcd-246f799d4991", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577042504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9388f215-eec5-46f3-b5a7-13e0670a9b09", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2128, "tid": "Worker0", "startTime": 577044868900, "endTime": 578703934100}, "additional": {"children": [], "state": "success", "parent": "db4b3267-6b46-4af4-8461-cab70c6cb8b6", "logId": "b15e68b5-e41e-4156-bdf2-a7ced6c49069"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "e774ef86-e72f-40c7-9eb4-80852534045c", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577043939400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d4dabd4-d9ba-422d-8ad5-edbed6b0d5a5", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577044933800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "571c928a-6648-4d8f-b390-c8a09109be1f", "name": "default@BuildNativeWithCmake work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577045084000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd5efb7a-cfcc-4b28-998f-f9dcce866970", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-DOHOS_STL=c++_shared',\n  '-DCMAKE_BUILD_TYPE=Release',\n  \"-DCMAKE_C_FLAGS='-s'\",\n  \"-DCMAKE_CXX_FLAGS='-s'\",\n  '-HD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\new\\\\ohos_ijkplayer-2.0.3\\\\ijkplayer\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577047412000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "063f3810-f9b7-4657-aa57-ddd38a9b0c38", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577048363900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6cbcba5-2428-4cc4-9926-48d0eabd555c", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2128, "tid": "Worker1", "startTime": 577048819700, "endTime": 578703214000}, "additional": {"children": [], "state": "success", "parent": "db4b3267-6b46-4af4-8461-cab70c6cb8b6", "logId": "ca4bbb16-1baa-4289-a13b-f6630b33e53f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "164dbae7-d40b-454b-858c-1300bc1ad8d1", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577048718700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd573e0d-d8e3-4a2a-876d-cce662d487ac", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577048731800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0f102ce-5a81-43fb-bd25-fa9c935e9d4b", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577048834700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c6310e6-db1b-4380-aed0-db2c2ab91891", "name": "default@BuildNativeWithCmake work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577048861100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9230232e-c1d7-4c9d-bcdc-ea715611156a", "name": "ijkplayer : default@BuildNativeWithCmake end {\n  rss: 194244608,\n  heapTotal: 127287296,\n  heapUsed: 103001432,\n  external: 1189145,\n  arrayBuffers: 223789\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577049230900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f640d216-15d3-4e41-bb09-015ff44f15e0", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578703430700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca4bbb16-1baa-4289-a13b-f6630b33e53f", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2128, "tid": "Worker1", "startTime": 577048819700, "endTime": 578703214000}, "additional": {"logType": "info", "children": [], "durationId": "c6cbcba5-2428-4cc4-9926-48d0eabd555c", "parent": "793a5b04-dff8-4468-82f8-026dbda331d5"}}, {"head": {"id": "845b4e82-e527-4570-9f11-fe1a42f9e26c", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578703868300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecb1fd1b-ab4f-4359-81ac-ef1265e6e700", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578703956800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b15e68b5-e41e-4156-bdf2-a7ced6c49069", "name": "ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2128, "tid": "Worker0", "startTime": 577044868900, "endTime": 578703934100}, "additional": {"logType": "info", "children": [], "durationId": "9388f215-eec5-46f3-b5a7-13e0670a9b09", "parent": "793a5b04-dff8-4468-82f8-026dbda331d5"}}, {"head": {"id": "793a5b04-dff8-4468-82f8-026dbda331d5", "name": "Finished :ijkplayer:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 577028285700, "endTime": 578704188400}, "additional": {"logType": "info", "children": ["b15e68b5-e41e-4156-bdf2-a7ced6c49069", "ca4bbb16-1baa-4289-a13b-f6630b33e53f"], "durationId": "db4b3267-6b46-4af4-8461-cab70c6cb8b6"}}, {"head": {"id": "f625d31e-bc83-4f09-a343-acae2a462cfe", "name": "ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578708327700, "endTime": 578708532000}, "additional": {"children": [], "state": "success", "detailId": "79771127-2ca6-4bb8-a24e-ef64bdf27dcd", "logId": "105656c9-b94b-4195-a449-00b8200f7e8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "79771127-2ca6-4bb8-a24e-ef64bdf27dcd", "name": "create ijkplayer:compileNative task", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578708141300}, "additional": {"logType": "detail", "children": [], "durationId": "f625d31e-bc83-4f09-a343-acae2a462cfe"}}, {"head": {"id": "0771585f-c8d6-4c1c-9be9-a7e0ceeeca53", "name": "ijkplayer : compileNative start {\n  rss: 294903808,\n  heapTotal: 127287296,\n  heapUsed: 103270264,\n  external: 1189145,\n  arrayBuffers: 223789\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578708278600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50074b86-6219-4933-81e5-0b7f9bc87b1d", "name": "Executing task :ijkplayer:compileNative", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578708350200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579c0272-807a-41b8-a4af-50cfd1c09038", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578708461900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67389638-d996-4cbb-84a7-3cd4c9f54b7f", "name": "ijkplayer : compileNative end {\n  rss: 294907904,\n  heapTotal: 127287296,\n  heapUsed: 103281248,\n  external: 1189145,\n  arrayBuffers: 223789\n}", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578708509700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "105656c9-b94b-4195-a449-00b8200f7e8d", "name": "Finished :ijkplayer:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578708327700, "endTime": 578708532000}, "additional": {"logType": "info", "children": [], "durationId": "f625d31e-bc83-4f09-a343-acae2a462cfe"}}, {"head": {"id": "5afff775-6ad6-478f-a723-0fc5cfae2c6f", "name": "BUILD SUCCESSFUL in 1 s 835 ms ", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578709034000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "49a5ddc8-4c49-44b8-b4b5-555412003a48", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 576874174000, "endTime": 578709709800}, "additional": {"time": {"year": 2025, "month": 7, "day": 22, "hour": 8, "minute": 38}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "14b02964-d7a8-4d1d-b434-9ee9dbabf64f", "name": "There is no need to refresh cache, since the incremental task ijkplayer:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2128, "tid": "Main Thread", "startTime": 578709961200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}