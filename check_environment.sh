#!/bin/bash

# OpenHarmony ijkplayer 环境检查脚本 (MSYS2版本)

echo "========================================"
echo "OpenHarmony ijkplayer 环境检查工具"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 函数：打印检查结果
print_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ "$result" = "PASS" ]; then
        echo -e "✓ ${GREEN}[PASS]${NC} $test_name"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    elif [ "$result" = "FAIL" ]; then
        echo -e "✗ ${RED}[FAIL]${NC} $test_name"
        if [ -n "$message" ]; then
            echo -e "  ${RED}$message${NC}"
        fi
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    elif [ "$result" = "WARN" ]; then
        echo -e "⚠ ${YELLOW}[WARN]${NC} $test_name"
        if [ -n "$message" ]; then
            echo -e "  ${YELLOW}$message${NC}"
        fi
    fi
}

# 检查MSYS2环境
check_msys2() {
    echo ""
    echo "检查MSYS2环境..."
    
    if [[ "$OSTYPE" == "msys" ]]; then
        print_result "MSYS2环境" "PASS"
    else
        print_result "MSYS2环境" "FAIL" "当前不在MSYS2环境中，请使用MSYS2终端运行此脚本"
    fi
    
    # 检查MSYS2版本
    if command -v pacman &> /dev/null; then
        local pacman_version=$(pacman --version | head -n1)
        print_result "包管理器pacman" "PASS" "$pacman_version"
    else
        print_result "包管理器pacman" "FAIL" "pacman未找到"
    fi
}

# 检查必要工具
check_tools() {
    echo ""
    echo "检查编译工具..."
    
    local tools=("git" "wget" "unzip" "tar" "make" "cmake" "gcc" "zip" "autoconf" "automake" "libtool" "pkg-config")
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            local version=$($tool --version 2>/dev/null | head -n1 || echo "版本信息不可用")
            print_result "工具: $tool" "PASS" "$version"
        else
            print_result "工具: $tool" "FAIL" "未安装，请运行: pacman -S $tool"
        fi
    done
}

# 检查OpenHarmony SDK
check_ohos_sdk() {
    echo ""
    echo "检查OpenHarmony SDK..."
    
    if [ -n "$OHOS_SDK" ]; then
        if [ -d "$OHOS_SDK" ]; then
            print_result "OHOS_SDK环境变量" "PASS" "路径: $OHOS_SDK"
            
            # 检查SDK结构
            if [ -d "$OHOS_SDK/native" ]; then
                print_result "SDK native目录" "PASS"
            else
                print_result "SDK native目录" "FAIL" "native目录不存在"
            fi
            
            if [ -d "$OHOS_SDK/native/llvm" ]; then
                print_result "SDK LLVM工具链" "PASS"
            else
                print_result "SDK LLVM工具链" "FAIL" "LLVM工具链不存在"
            fi
            
        else
            print_result "OHOS_SDK路径" "FAIL" "路径不存在: $OHOS_SDK"
        fi
    else
        print_result "OHOS_SDK环境变量" "WARN" "未设置，请设置: export OHOS_SDK=/path/to/sdk"
    fi
}

# 检查项目结构
check_project_structure() {
    echo ""
    echo "检查项目结构..."
    
    local current_dir=$(pwd)
    
    # 检查是否在项目根目录
    if [ -f "prebuild.sh" ] && [ -f "README.md" ] && [ -d "ijkplayer" ]; then
        print_result "项目根目录" "PASS" "当前在项目根目录: $current_dir"
    else
        print_result "项目根目录" "FAIL" "请在项目根目录运行此脚本"
    fi
    
    # 检查关键目录
    local dirs=("doc" "ijkplayer" "ijkplayer/src" "ijkplayer/src/main" "ijkplayer/src/main/cpp")
    
    for dir in "${dirs[@]}"; do
        if [ -d "$dir" ]; then
            print_result "目录: $dir" "PASS"
        else
            print_result "目录: $dir" "FAIL" "目录不存在"
        fi
    done
    
    # 检查依赖配置文件
    local config_files=("doc/libyuv-ijk" "doc/soundtouch-ijk" "doc/FFmpeg")
    
    for config in "${config_files[@]}"; do
        if [ -d "$config" ]; then
            print_result "配置: $config" "PASS"
        else
            print_result "配置: $config" "FAIL" "配置目录不存在"
        fi
    done
}

# 检查网络连接
check_network() {
    echo ""
    echo "检查网络连接..."
    
    # 检查GitHub连接
    if wget --spider --quiet --timeout=10 https://github.com 2>/dev/null; then
        print_result "GitHub连接" "PASS"
    else
        print_result "GitHub连接" "WARN" "无法连接到GitHub，可能需要代理"
    fi
    
    # 检查Gitee连接
    if wget --spider --quiet --timeout=10 https://gitee.com 2>/dev/null; then
        print_result "Gitee连接" "PASS"
    else
        print_result "Gitee连接" "WARN" "无法连接到Gitee"
    fi
}

# 检查现有依赖
check_existing_dependencies() {
    echo ""
    echo "检查现有依赖..."
    
    local third_party_dir="ijkplayer/src/main/cpp/third_party"
    
    if [ -d "$third_party_dir" ]; then
        print_result "third_party目录" "PASS"
        
        # 检查各个依赖目录
        local deps=("ffmpeg" "soundtouch" "yuv" "openssl")
        
        for dep in "${deps[@]}"; do
            if [ -d "$third_party_dir/$dep" ]; then
                local file_count=$(find "$third_party_dir/$dep" -type f | wc -l)
                if [ "$file_count" -gt 1 ]; then
                    print_result "依赖: $dep" "PASS" "包含 $file_count 个文件"
                else
                    print_result "依赖: $dep" "WARN" "目录存在但文件较少 ($file_count 个文件)"
                fi
            else
                print_result "依赖: $dep" "FAIL" "目录不存在"
            fi
        done
    else
        print_result "third_party目录" "FAIL" "目录不存在"
    fi
}

# 提供修复建议
provide_suggestions() {
    echo ""
    echo "========================================"
    echo "修复建议"
    echo "========================================"
    
    if [ $FAILED_CHECKS -gt 0 ]; then
        echo -e "${RED}发现 $FAILED_CHECKS 个问题需要修复：${NC}"
        echo ""
        echo "1. 安装缺失的工具："
        echo "   pacman -S --needed base-devel mingw-w64-x86_64-toolchain"
        echo "   pacman -S git wget unzip cmake make autoconf automake libtool pkg-config"
        echo ""
        echo "2. 设置OpenHarmony SDK环境变量："
        echo "   export OHOS_SDK=/path/to/your/ohos-sdk"
        echo ""
        echo "3. 确保在项目根目录运行脚本"
        echo ""
        echo "4. 检查网络连接，必要时设置代理"
        echo ""
    fi
    
    if [ $PASSED_CHECKS -eq $TOTAL_CHECKS ]; then
        echo -e "${GREEN}✓ 所有检查都通过！您可以开始编译依赖库了。${NC}"
        echo ""
        echo "运行以下命令开始编译："
        echo "  chmod +x build_msys2.sh"
        echo "  ./build_msys2.sh"
    fi
}

# 主函数
main() {
    check_msys2
    check_tools
    check_ohos_sdk
    check_project_structure
    check_network
    check_existing_dependencies
    
    echo ""
    echo "========================================"
    echo "检查结果汇总"
    echo "========================================"
    echo -e "总检查项: $TOTAL_CHECKS"
    echo -e "${GREEN}通过: $PASSED_CHECKS${NC}"
    echo -e "${RED}失败: $FAILED_CHECKS${NC}"
    echo -e "${YELLOW}警告: $((TOTAL_CHECKS - PASSED_CHECKS - FAILED_CHECKS))${NC}"
    
    provide_suggestions
}

# 执行主函数
main "$@"
