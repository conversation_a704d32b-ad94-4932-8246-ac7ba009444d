# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

pkgname=gemmlowp
pkgver=master
pkgrel=0
pkgdesc="a small self-contained low-precision GEMM library"
url="https://github.com/google/gemmlowp"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache-2.0 license")
depends=()
makedepends=()

source="https://github.com/google/$pkgname.git"
commitid=16e8662c34917be0065110bfcd9cc27d30f52fdf

autounpack=false
downloadpackage=false
builddir=$pkgname-${commitid}
packagename=
cloneflag=true

prepare() {
    if [ $cloneflag == true ]
    then
        mkdir -p $builddir
        git clone -b $pkgver $source $builddir > $buildlog 2>&1
        if [ $? -ne 0 ]
        then
            return -1
        fi
        cd $builddir
        git reset --hard $commitid > $buildlog 2>&1
        if [ $? -ne 0 ]
        then
            return -2
        fi
       
        cd $OLDPWD
        cloneflag=false
    fi
    mkdir -p $builddir/contrib/$ARCH-build
    
    if $patchflag
    then
        cd $builddir/contrib
        patch -p1 < `pwd`/../../gemmlowp_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
}

build() {
    cd $builddir/contrib
    
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" \
        -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/contrib
    $MAKE -C $ARCH-build install >> $buildlog 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
