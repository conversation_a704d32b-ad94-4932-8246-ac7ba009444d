# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON> <<EMAIL>>, DZD <<EMAIL>>
# Maintainer: <PERSON> <<EMAIL>>

source HPKBUILD > /dev/null 2>&1    # 导入HPKBUILD文件
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

# 测试前的准备, 如果不需要可以不写。
checkprepare(){
    return 0
}

# 在OH环境执行测试的接口
openharmonycheck() {
    res=0                               # 记录返回值
    cp -r ${builddir}/${ARCH}-build/test/dict ${builddir}/${ARCH}-build/src > ${logfile} 2>&1
    cp -r ${builddir}/${ARCH}-build/test/dict ${builddir}/${ARCH}-build/examples/cpp >> ${logfile} 2>&1
    cd ${builddir}/${ARCH}-build/src        # 进入到测试目录
    ./simple_tests >> ${logfile} 2>&1
    if [ $? -ne 0 ]; then
       cd $OLDPWD
       return $res
    fi
    echo "test 1: pass" >> ${logfile}
    cd $OLDPWD
    cd ${builddir}/${ARCH}-build/examples/cpp
    ./simple_cpp_example >> ${logfile} 2>&1
    if [ $? -ne 0 ]; then
       cd $OLDPWD
       return $res
    fi 	
    echo "test 2: pass" >> ${logfile}
    echo "Total test: 2  pass: 2" >> ${logfile}
    res=$?
    cd $OLDPWD                          # 返回上一次目录
    return $res                         # 返回测试值
}

