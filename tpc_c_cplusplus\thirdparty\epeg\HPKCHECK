# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
epeginputimage=/test/input.jpg
epegthumbimage=/test/thumb.jpg
epeginputmd5="e4ace42a38124829346df688fdd6af08"
epegthumbmd5="55dca5615b695601ec4c9f6f220ad7a1"

openharmonycheck() {
    res=0
    cd $builddir/$ARCH-build
    echo "total test 1"  > $logfile
    ./epeg -m 180 ../../test/input.jpg ../../test/thumb.jpg  >> $logfile 2>&1
    res=$?
    if [ $res -ne 0 ]
    then
        echo "test error" >> $logfile
        cd $OLDPWD
        return $res
    fi

    #转换前后图片的md5值不等于Linux下同图片的md5值则为异常
    inputmd5=$(md5sum ${epeginputimage} | awk '{print $1}')
    thumbmd5=$(md5sum ${epegthumbimage} | awk '{print $1}')
    if [ "$inputmd5" = "$epeginputmd5" ] && [ "$thumbmd5" != "$epegthumbmd5" ]
    then
        echo "images do not match" >> $logfile
        cd $OLDPWD
        return -1
    fi

    mkdir -p $CHECK_RESULT_PATH/manual_confirm/${pkgname}
    cp ../../test/thumb.jpg $CHECK_RESULT_PATH/manual_confirm/${pkgname}
    echo "test pass" >> $logfile
    cd $OLDPWD
    return $res
}
