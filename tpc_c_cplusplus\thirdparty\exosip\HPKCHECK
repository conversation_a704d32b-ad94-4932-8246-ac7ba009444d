# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

checkprepare(){
    echo "1)The exosip library testing environment requires a PC and a board to form a local area network, where the PC acts as a server and runs server programs miniSIPServer on the PC" > ${logfile} 2>&1
    echo "2)Server program download address:https://www.myvoipapp.com/download/index.html" >> ${logfile} 2>&1
    echo "3)To test, it is necessary to establish extension numbers in the miniSIPServer program (the software defaults to two extension numbers of 100.101)" >> ${logfile} 2>&1
    echo -e "4)At the beginning of the testing phase, need the server IP address, board IP address, extension number, password \n" >> ${logfile} 2>&1
    
    echo "this is a example" >> ${logfile} 2>&1
    echo "./sip_monitor  -r sip:************* -u sip:102@************ -U 102 -P 102 -t TCP -s -e 180 -m --outbound \"<sip:*************;lr>\" " >> ${logfile} 2>&1
    echo " -r sip:*************     # server IP address" >> ${logfile} 2>&1
    echo " -u sip:101@************  # 101 extension number,************ board IP address" >> ${logfile} 2>&1
    echo " -U 101                   # extension number" >> ${logfile} 2>&1
    echo  -e " -P 101                   # password \n" >> ${logfile} 2>&1

    return 0;
}

openharmonycheck() {
    res=-1
    cd $LYCIUM_ROOT/usr/exosip/${ARCH}/bin
    # 该测试只是检测测试程序能否运行，不做功能判断。
    echo -e "Automation only verifies whether the program can run without judging the test results. Functional testing requires manual verification \n" >> ${logfile} 2>&1
    ./sip_monitor  -r sip:************* -u sip:123@*************-U 123 -P 123 -t TCP -s -e 180 -m --outbound "<sip:*************;lr>" >> ${logfile} 2>&1
    if grep -q "REGISTRATION REPORT" ${logfile}; then
        res=0
    fi    

    cd $OLDPWD
    
    return $res
}
