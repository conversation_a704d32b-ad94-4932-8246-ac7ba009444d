# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=nspr
pkgver=4.35
pkgrel=0
pkgdesc="Netscape Portable Runtime (NSPR) provides a platform-neutral API for system level and libc like functions."
url="https://archive.mozilla.org/pub/nspr/"
archs=("armeabi-v7a" "arm64-v8a")
license=("MPL-2.0")
depends=()
makedepends=()

source="https://archive.mozilla.org/pub/$pkgname/releases/v$pkgver/src/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

source envset.sh
host=
buildhost=true
patchflag=true

prepare() {
    if [ $buildhost == true ]
    then
        # 进行X86编译
        cp -arf $builddir $builddir-host
        cd $builddir-host/$pkgname
        ./configure "$@" > $publicbuildlog 2>&1
        $MAKE VERBOSE=1 >> $publicbuildlog 2>&1 
        cd $OLDPWD
        buildhost=false
    fi
    if [ "$patchflag" == true ]
    then
        cd $builddir
        # patch文件修改了pr/tests/abstract.c，修改本地通信的socket文件路径，解决在OHOS上测试出现本地连接失败的问题
        patch -p1 < ../nspr_oh_test.patch >> $publicbuildlog 2>&1
        patchflag=false
        cd $OLDPWD
    fi
    cp -arf $builddir $pkgname-$ARCH-build

    # 再进行交叉编译
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    elif [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    else
        echo "${ARCH} not support"
        return -1
    fi
}

build() {
    cd $pkgname-$ARCH-build/$pkgname
    ./configure "$@" --host=$host --with-pthreads >> $publicbuildlog 2>&1
    $MAKE VERBOSE=1 >> $publicbuildlog 2>&1
    ret=$?
    if [ $ret -ne 0 ]
    then
        # 交叉编译依赖X86编译生成的nsinstall文件
        cp $LYCIUM_ROOT/../thirdparty/$pkgname/$builddir-host/$pkgname/config/nsinstall ./config/
        $MAKE VERBOSE=1 >> $publicbuildlog 2>&1
        ret=$?
    fi
    cd $OLDPWD
    return $ret
}

package() {
    cd $pkgname-$ARCH-build/$pkgname
    $MAKE VERBOSE=1 install >> $publicbuildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

recoverpkgbuildenv() {
    unset host
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    elif [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    else
        echo "${ARCH} not support"
        return -1
    fi
}

check() {
    echo "The test must be on an OpenHarmony device!"
    cd $pkgname-$ARCH-build/$pkgname/pr/tests
    $MAKE VERBOSE=1 >> $publicbuildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret

    # 测试方式
    # 进入构建目录下的pr/test
    # 执行: make runtests
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build #${PWD}/$packagename
}
