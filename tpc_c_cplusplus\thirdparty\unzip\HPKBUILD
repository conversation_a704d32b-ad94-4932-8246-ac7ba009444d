# Contributor: <PERSON> <hanjin<PERSON><EMAIL>>
# Maintainer: <PERSON> <hanjin<PERSON><EMAIL>>
pkgname=unzip
pkgver=60
pkgrel=0
pkgdesc=""
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("Info-ZIP license")
depends=("bzip2")
makedepends=()

source="https://sourceforge.net/projects/infozip/files/UnZip%206.x%20%28latest%29/UnZip%206.0/$pkgname$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="make"

builddir=$pkgname${pkgver}
packagename=$builddir.tar.gz
patch_flag=true

cc=
as=
# zip 采用makefile编译构建，为了保留构建环境(方便测试)。因此同一份源码在解压后分为两份,各自编译互不干扰
prepare() {
    if [ "$patch_flag" == true ]
    then
        cd $builddir
        patch -p1 < ../unzip_ohos_pkg.patch > $buildlog 2>&1  #调整unix/Makefile
        cd $OLDPWD
        patch_flag=false
    fi
    cp -rf $builddir $builddir-$ARCH-build
    # 将 bzip2 源码加入到 unzip 项目，使 unzip 支持 bzip2 算法，如果不需要 bzip2 算法可以不拷贝。此处 copy 有 bzip2 带版本号的目录，导致 bzip2 的版本变更会影响到 unzip 的编译，为了避免两个项目耦合过深，所以在 unzip 项目，我这里设置为默认是不支持 bzip2 注释28行。如果需要只需解开28行注释确认版本号目录即可。
    # cp -rf `pwd`/../bzip2/bzip2-1.0.6/*  $builddir-$ARCH-build/bzip2/
    cd $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        cc=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang
        as=${OHOS_SDK}/native/llvm/bin/llvm-as
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        cc=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang
        as=${OHOS_SDK}/native/llvm/bin/llvm-as
    fi
    cd $OLDPWD # 1> /dev/null
}

build() {
    cd $builddir-$ARCH-build
    $MAKE CC=${cc} AS=${as} -f unix/Makefile generic > $buildlog 2>&1
    ret=$?
    if [ $ret == 0 ]
    then
        $MAKE CC=${cc} AS=${as} -f unix/Makefile generic_shlib >> $buildlog 2>&1
        ret=$?
    fi
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir-$ARCH-build
    $MAKE prefix=$LYCIUM_ROOT/usr/$pkgname/$ARCH -f unix/Makefile install >> $buildlog 2>&1
    # make install没有拷贝so和.h文件，需cp到usr目录
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include
    cp ./*.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib
    cp ./*.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib

    cd $OLDPWD
    unset cc as
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # make check
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build #${PWD}/$packagename
}
