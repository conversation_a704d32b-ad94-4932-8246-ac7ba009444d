# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL> luozhu <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=libjpeg-turbo
pkgver=2.1.91
pkgrel=0
pkgdesc="libjpeg-turbo is a JPEG image codec that uses SIMD instructions to accelerate baseline JPEG compression and decompression"
url="https://github.com/libjpeg-turbo/libjpeg-turbo"
archs=("armeabi-v7a" "arm64-v8a")
license=("IJG(Independent JPEG Group) License", "3-clause BSD License", "zlib License")
depends=()
makedepends=()
install=usr
# 官方下载地址https://github.com/${pkgname}/${pkgname}/archive/refs/tags/${pkgver}.tar.gz受网络影响可能存在下载失败的情况，现使用gitee镜像可以与官方仓库保持同步
source="https://gitee.com/mirrors/${pkgname}/repository/archive/${pkgver}.zip"

builddir=${pkgname}-${pkgver}
packagename=${builddir}.zip
patchflag=false
autounpack=true
downloadpackage=true
buildtools="cmake"

prepare() {
    mkdir -p ${builddir}/${ARCH}-build
}

build() {
    cd ${builddir}
     # -DCMAKE_C_FLAGS="-ffp-contract=off" 禁用 floating-point expression contraction.否则会因为浮点精度问题造成测试用例失败。
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DCMAKE_C_FLAGS="-ffp-contract=off" -B${ARCH}-build -S./ >> $buildlog 2>&1
    $MAKE VERBOSE=1 -C ${ARCH}-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd ${builddir}
    $MAKE -C ${ARCH}-build install >> $buildlog 2>&1
    cd ${OLDPWD}
}

check() {
    # 删除测试中cmake的路径,使测试使用系统的cmake，而不使用SDK下的cmake
    cd $builddir/$ARCH-build
    grep -rn "\".*\/cmake\"" | while read line;
    do
    {
        name=${line%%:*}
        sed -i "s/\".*\/cmake\"/\"cmake\"/g" $name
    }
    done
    cd $OLDPWD
    echo "The test must be on an OpenHarmony device!"
    # ctest
}

cleanbuild(){
    rm -rf ${PWD}/${builddir} #${PWD}/$packagename
}
