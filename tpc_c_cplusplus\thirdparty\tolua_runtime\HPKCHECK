# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1    # 导入HPKBUILD文件
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

# 在OH环境执行测试的接口
openharmonycheck() {
    res=1                                                           # 记录返回值
    cd $builddir-$ARCH-build
    
    ./demo_ohos > ${logfile} 2>&1   
    search_string="Init tolua_runtime success!!!"
    if grep -q "$search_string" $logfile; then
        res=0
    else
        res=1
    fi
    
    cd $OLDPWD                                                      # 返回上一次目录
    return $res                                                     # 返回测试值
}
