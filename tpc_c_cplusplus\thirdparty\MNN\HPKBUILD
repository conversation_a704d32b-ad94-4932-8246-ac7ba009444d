# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: <PERSON><PERSON> <<EMAIL>> luozhu <<EMAIL>>
# Maintainer: <PERSON><PERSON> <<EMAIL>>

pkgname=MNN
pkgver=2.6.3
pkgrel=0
pkgdesc="MNN is a highly efficient and lightweight deep learning framework.  It supports inference and training of deep learning models."
url="https://github.com/alibaba/MNN"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache 2.0")
depends=()
makedepends=()
source="https://github.com/alibaba/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true
builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz
buildtools="cmake"

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DMNN_BUILD_TEST=ON -B$ARCH-build -S./ > $buildlog 2>&1
    $MAKE VERBOSE=1 -C $ARCH-build >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    $MAKE -C $ARCH-build install VERBOSE=1 >> $buildlog 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

check() {
    if [ $ARCH == "armeabi-v7a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/arm-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    elif [ $ARCH == "arm64-v8a" ]
    then
        cp ${OHOS_SDK}/native/llvm/lib/aarch64-linux-ohos/libc++_shared.so $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    else
        echo "${ARCH} not support"
        return -1.
    fi
    echo "The test must be on an OpenHarmony device!"
    # ./run_test.out
}

# 清理环境
cleanbuild(){
     rm -rf ${PWD}/$builddir
}

