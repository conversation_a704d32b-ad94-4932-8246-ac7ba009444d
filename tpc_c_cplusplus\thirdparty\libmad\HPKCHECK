# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Contributor: liucheng<<EMAIL>>
# Maintainer: liucheng<<EMAIL>>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log

openharmonycheck() {
    ret=0
    cd $builddir-$ARCH
    ./minimad < ../bootsound.mp3 > bootsound.pcm
    ret=$?
    if [ $ret != 0 ]
    then
        echo "minimad create bootsound.pcm fail" > $logfile
        cd $OLDPWD
        return -1;
    else
        mkdir -p ${LYCIUM__MANUAL_CONFIRM_PATH}/${pkgname}/
        # 因为minimad的标准输出就是一个音频数据，所以需要手动加上执行成功的log打印
        echo "minimad create bootsound.pcm success" > $logfile
        cp bootsound.pcm ${LYCIUM__MANUAL_CONFIRM_PATH}/${pkgname}/
        cd $OLDPWD
        return 0
    fi
}
