# Contributor: l<PERSON><PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=lame 
pkgver=3.100 
pkgrel=0
pkgdesc="LAME is an educational tool to be used for learning about MP3 encoding."
url=""
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPLv2" "GPLv2")
depends=()
makedepends=()
 
source="https://nchc.dl.sourceforge.net/project/$pkgname/$pkgname/$pkgver/$pkgname-$pkgver.tar.gz"
builddir=$pkgname-$pkgver
packagename=$builddir.tar.gz
buildtools="configure"

source envset.sh
host=
prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
    cd $builddir
    cd $OLDPWD
}

build() {

    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host > `pwd`/build.log 2>&1
    make -j4 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir/$ARCH-build
    make install >> `pwd`/build.log 2>&1
    cd $OLDPWD
    unset host
}

check() {
    cd $builddir/$ARCH-build
    make check >> `pwd`/build.log 2>&1
    cd $OLDPWD
    if [ $ARCH == "armeabi-v7a" ]
    then
        unsetarm32ENV
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        unsetarm64ENV
    fi
    echo "The test must be on an OpenHarmony device!"
    # 在构建目录下，执行make test
}

cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/packagename
}
