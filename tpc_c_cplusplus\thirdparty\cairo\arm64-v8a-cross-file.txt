[binaries]
c = 'ohos_sdk/native/llvm/bin/aarch64-linux-ohos-clang'
cpp = 'ohos_sdk/native/llvm/bin/aarch64-linux-ohos-clang++'
ar = 'ohos_sdk/native/llvm/bin/llvm-ar'
strip = 'ohos_sdk/native/llvm/bin/llvm-strip'
ld = 'ohos_sdk/native/llvm/bin/ld.lld'
pkgconfig = '/usr/bin/pkg-config'

[host_machine]
system = 'linux'
cpu_family = 'aarch64'
cpu = 'arm64-v8a'
endian = 'little'

[properties]
needs_exe_wrapper = true
skip_sanity_check = true
sys_root = ''
platform = 'generic'
pkg_config_libdir = ''

[built-in options]
c_args = ['-D__MUSL__=1', '-mfpu=neon']
cpp_args = ['-D__MUSL__=1', '-mfpu=neon']
c_link_args = []
cpp_link_args = []
