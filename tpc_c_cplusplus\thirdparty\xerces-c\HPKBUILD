# Contributor: l<PERSON><PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
pkgname=xerces-c
pkgver=v3.2.4
pkgrel=0
pkgdesc="Apache Xerces-C validating XML parser"
url="https://github.com/apache/xerces-c"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache-2.0 license")
depends=()
makedepends=()

source="https://github.com/apache/$pkgname/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true

builddir=$pkgname-${pkgver:1}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    # 将安装目录加到 LD_LIBRARY_PATH 环境变量
    # ctest
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
