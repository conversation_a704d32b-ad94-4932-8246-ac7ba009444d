# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=libtess2 
pkgver=v1.0.2 
pkgrel=0 
pkgdesc="Game and tools oriented refactored version of GLU tesselator." 
url="https://github.com/memononen/libtess2" 
archs=("armeabi-v7a" "arm64-v8a")
license=("SGI FREE SOFTWARE LICENSE B")
depends=() 
makedepends=("premake4") 
install= 
source="https://github.com/memononen/$pkgname/archive/refs/tags/$pkgver.tar.gz" 

downloadpackage=true
patchflag=true
buildtools="make"

builddir=$pkgname-${pkgver:1} 
packagename=$builddir.tar.gz 

cc=
prepare() {
    if $patchflag
    then
        cd $builddir
        # 本库example依赖gl相关套件，ohos暂不支持
        # 所以打patch修改example源码，注释gl相关代码，只是进行example的计算（源库是一个算法库），不涉及gl的显示
        # 修改premake4.lua,让example不依赖gl相关套件
        patch -p1 < `pwd`/../libtess2_oh_pkg.patch
        # patch只需要打一次,关闭打patch
        patchflag=false
        cd $OLDPWD
    fi
    cp -rf $builddir $builddir-$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        cc=${OHOS_SDK}/native/llvm/bin/arm-linux-ohos-clang
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        cc=${OHOS_SDK}/native/llvm/bin/aarch64-linux-ohos-clang
    fi
}

build() {
    cd $builddir-$ARCH-build
    premake4 gmake > `pwd`/build.log 2>&1
    cd $OLDPWD
    cd $builddir-$ARCH-build/Build
    make CC=${cc} -j4 > `pwd`/build.log 2>&1
    # 对最关键一步的退出码进行判断
    ret=$?
    cd $OLDPWD
    return $ret
}

# 安装打包
package() {
    cd $builddir-$ARCH-build
    # 源库Makefile 没有make install，这里手动copy到usr目录
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    mkdir -p $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    cp Source/*.h Include/*.h $LYCIUM_ROOT/usr/$pkgname/$ARCH/include/
    cp Build/libtess2.a $LYCIUM_ROOT/usr/$pkgname/$ARCH/lib/
    cd $OLDPWD
    unset cc
}

# 测试，需要在 ohos 设备上进行
check() {
    echo "The test must be on an OpenHarmony device!"
    # ./example
    # loading...
    # go...
    # view0: -34.327606
    # view1: 377.603638
}

# 清理环境
cleanbuild(){
   rm -rf ${PWD}/$builddir $builddir-armeabi-v7a-build  $builddir-arm64-v8a-build #${PWD}/$packagename
}