# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
pkgname=libosip2
pkgver=5.3.1
pkgrel=0
pkgdesc="libosip2 is a C language library for parsing and processing the SIP protocol. It provides a powerful set of APIs used to implement SIP clients and servers."
url="https://ftp.gnu.org/gnu/osip/"
archs=("armeabi-v7a" "arm64-v8a")
license=("LGPL")
depends=()
makedepends=()

source="https://ftp.gnu.org/gnu/osip/$pkgname-$pkgver.tar.gz"

autounpack=true
downloadpackage=true
buildtools="configure"

builddir=$pkgname-${pkgver}
packagename=$builddir.tar.gz

source envset.sh
host=
prepare() {
    mkdir -p $builddir/$ARCH-build
    if [ $ARCH == "armeabi-v7a" ]
    then
        setarm32ENV
        host=arm-linux
    fi
    if [ $ARCH == "arm64-v8a" ]
    then
        setarm64ENV
        host=aarch64-linux
    fi
}

build() {
    cd $builddir/$ARCH-build
    ../configure "$@" --host=$host --enable-test > `pwd`/build.log 2>&1
    make -j4 VERBOSE=1 >> `pwd`/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    cd $builddir/$ARCH-build
    sed -i '/check: check-am/c\#check: check-am' src/test/Makefile
    cd $OLDPWD
    echo "The test must be on an OpenHarmony device!"
    #编译生成目录$ARCH-build/src/test下执行make chech
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
