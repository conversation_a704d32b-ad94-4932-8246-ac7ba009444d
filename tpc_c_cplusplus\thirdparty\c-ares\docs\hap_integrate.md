# c-ares 集成到应用hap

本库是在RK3568开发板上基于OpenHarmony3.2 Release版本的镜像验证的，如果是从未使用过RK3568，可以先查看[润和RK3568开发板标准系统快速上手](https://gitee.com/openharmony-sig/knowledge_demo_temp/tree/master/docs/rk3568_helloworld)。

## 开发环境

- ubuntu20.04
- [OpenHarmony3.2Release镜像](https://gitee.com/link?target=https%3A%2F%2Frepo.huaweicloud.com%2Fopenharmony%2Fos%2F3.2-Release%2Fdayu200_standard_arm32.tar.gz)
- [ohos_sdk_public ******* (API Version 10 Release)](http://download.ci.openharmony.cn/version/Master_Version/OpenHarmony_*******/20230608_091016/version-Master_Version-OpenHarmony_*******-20230608_091016-ohos-sdk-full.tar.gz)
- [DevEco Studio 3.1 Release](https://contentcenter-vali-drcn.dbankcdn.cn/pvt_2/DeveloperAlliance_package_901_9/81/v3/tgRUB84wR72nTfE8Ir_xMw/devecostudio-windows-3.1.0.501.zip?HW-CC-KV=V1&HW-CC-Date=20230621T074329Z&HW-CC-Expire=*********&HW-CC-Sign=22F6787DF6093ECB4D4E08F9379B114280E1F65DA710599E48EA38CB24F3DBF2)
- [准备三方库构建环境](../../../lycium/README.md#1编译环境准备)
- [准备三方库测试环境](../../../lycium/README.md#3ci环境准备)

## 编译三方库

- 下载本仓库

  ```shell
  git clone https://gitee.com/openharmony-sig/tpc_c_cplusplus.git --depth=1
  ```

- 三方库目录结构

  ```
  tpc_c_cplusplus/thirdparty/c-ares  #三方库c-ares的目录结构如下
  ├── docs                              #三方库相关文档的文件夹
  ├── HPKBUILD                          #构建脚本
  ├── SHA512SUM                         #三方库校验文件
  ├── README.OpenSource                 #说明三方库源码的下载地址，版本，license等信息
  ├── README_zh.md   
  ```

- 在lycium目录下编译三方库

  编译环境的搭建参考[准备三方库构建环境](../../../lycium/README.md#1编译环境准备)

  ```
  cd lycium
  ./build.sh c-ares
  ```

- 三方库头文件及生成的库

  在lycium目录下会生成usr目录，该目录下存在已编译完成的32位和64位三方库

  ```
  c-ares/arm64-v8a   c-ares/armeabi-v7a
  ```

- [测试三方库](#测试三方库)

## 应用中使用三方库

- 在IDE的cpp目录下新增thirdparty目录，将编译生成的库拷贝到该目录下，如下图所示：

  &nbsp;![thirdparty_install_dir](pic/c-ares_install_dir.png)

- 拷贝动态库到`\\entry\libs\${OHOS_ARCH}\`目录：

  动态库需要在`\\entry\libs\${OHOS_ARCH}\`目录，才能集成到hap包中，所以需要将对应的so文件拷贝到对应CPU架构的目录：

  &nbsp;![thirdparty_install_dir](pic/c-ares_install_dir2.png)

- 在最外层（cpp目录下）CMakeLists.txt中添加如下语句：

  ```
  #将三方库加入工程中，c-ares是对应的三方库名
  target_link_libraries(entry PUBLIC libace_napi.z.so ${CMAKE_CURRENT_SOURCE_DIR}/../../../libs/${OHOS_ARCH}/libcares.so)
  #将三方库的头文件加入工程中，c-ares是对应的三方库名
  target_include_directories(entry PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/c-ares/${OHOS_ARCH}/include)
  ```

## 测试三方库

三方库的测试使用原库自带的可执行文件来做测试

三方库的测试使用原库自带的测试用例来做测试，[准备三方库测试环境](../../../lycium/README.md#3ci环境准备)

进入到构建目录执行ctest运行测试用例（arm64-v8a-build为构建64位的目录，armeabi-v7a-build为构建32位的目录）
```
 ctest在ohos设备上运行第一个测试用例会失败，进入Testing/Temporary/LastTest.log观察发现有四个用例不过
 根据源库的iusse:https://github.com/c-ares/c-ares/issues/476, 作者回答是当前环境原因，ohos设备中没有/etc/services,所以这四个用例无法通过
 [  FAILED  ] 4 tests, listed below:
 [  FAILED  ] AddressFamiliesAI/MockChannelTestAI.FamilyV4ServiceName/0, where GetParam() = (2, false)
 [  FAILED  ] AddressFamiliesAI/MockChannelTestAI.FamilyV4ServiceName/1, where GetParam() = (2, true)
 [  FAILED  ] AddressFamiliesAI/MockChannelTestAI.FamilyV4ServiceName/2, where GetParam() = (10, false)
 [  FAILED  ] AddressFamiliesAI/MockChannelTestAI.FamilyV4ServiceName/3, where GetParam() = (10, true)
```

&nbsp;![c-ares_test](pic/c-ares_test1.png)
&nbsp;![c-ares_test](pic/c-ares_test2.png)
## 参考资料

- [润和RK3568开发板标准系统快速上手](https://gitee.com/openharmony-sig/knowledge_demo_temp/tree/master/docs/rk3568_helloworld)
- [OpenHarmony三方库地址](https://gitee.com/openharmony-tpc)
- [OpenHarmony知识体系](https://gitee.com/openharmony-sig/knowledge)
- [通过DevEco Studio开发一个NAPI工程](https://gitee.com/openharmony-sig/knowledge_demo_temp/blob/master/docs/napi_study/docs/hello_napi.md)