# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

pkgname=jsoncpp 
pkgver=1.9.5 
pkgrel=0 
pkgdesc=""
url="" 
archs=("armeabi-v7a" "arm64-v8a") # cpu 架构
license=("MIT License")
source="https://github.com/open-source-parsers/$pkgname/archive/refs/tags/$pkgver.tar.gz" 

patchflag=false 
autounpack=true 
downloadpackage=true 

builddir=$pkgname-$pkgver 
packagename=$builddir.tar.gz 

prepare() {
    mkdir -p $builddir/$ARCH-build
}


build() {
    cd $builddir
    # JSONCPP_WITH_POST_BUILD_UNITTEST打开时编译过程会执行jsoncpp_test可执行文件，导致编译失败
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DJSONCPP_WITH_POST_BUILD_UNITTEST=OFF -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

# 安装打包
package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

# 测试，需要在 ohos 设备上进行
check() {
    echo "The test must be on an OpenHarmony device!"
    # real test CMD
    #./jsoncpp_test
}

# 清理环境
cleanbuild(){
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}