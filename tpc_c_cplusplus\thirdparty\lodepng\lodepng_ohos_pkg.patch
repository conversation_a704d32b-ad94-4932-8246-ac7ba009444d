diff --git a/Makefile b/Makefile
index 78afa1b..5608b49 100644
--- a/Makefile
+++ b/Makefile
@@ -12,7 +12,7 @@ CXX ?= g++
 override CFLAGS := -W -Wall -Wextra -ansi -pedantic -O3 -Wno-unused-function $(CFLAGS)
 override CXXFLAGS := -W -Wall -Wextra -ansi -pedantic -O3 $(CXXFLAGS)
 
-all: unittest benchmark pngdetail showpng
+all: unittest pngdetail lodepng
 
 %.o: %.cpp
 	@mkdir -p `dirname $@`
@@ -30,5 +30,8 @@ pngdetail: lodepng.o lodepng_util.o pngdetail.o
 showpng: lodepng.o examples/example_sdl.o
 	$(CXX) -I ./ $^ $(CXXFLAGS) -lSDL2 -o $@
 
+lodepng:
+	$(CXX) lodepng.cpp -fPIC -shared -o  lodepng.so 
+
 clean:
 	rm -f unittest benchmark pngdetail showpng lodepng_unittest.o lodepng_benchmark.o lodepng.o lodepng_util.o pngdetail.o examples/example_sdl.o
