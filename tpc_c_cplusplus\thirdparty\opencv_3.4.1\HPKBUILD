# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# <AUTHOR> <EMAIL>, zhaoxu <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=opencv_3.4.1
pkgver=3.4.1
pkgrel=0
pkgdesc="OpenCV (Open Source Computer Vision Library) is an open source computer vision and machine learning software library.  OpenCV was built to provide a common infrastructure for computer vision applications and to accelerate the use of machine perception in the commercial products. "
url="https://github.com/opencv/opencv"
archs=("armeabi-v7a" "arm64-v8a")
license=("Apache-2.0 license")
depends=()
makedepends=()

source="https://github.com/opencv/${pkgname:0:6}/archive/refs/tags/$pkgver.tar.gz"

autounpack=true
downloadpackage=true

builddir=${pkgname:0:6}-${pkgver}
packagename=$builddir.tar.gz

prepare() {
    mkdir -p $builddir/$ARCH-build
}

build() {
    cd $builddir
    ${OHOS_SDK}/native/build-tools/cmake/bin/cmake "$@" -DOHOS_ARCH=$ARCH -DBUILD_SHARED_LIBS=OFF -B$ARCH-build -S./ -L > `pwd`/$ARCH-build/build.log 2>&1
    make VERBOSE=1 -j4 -C $ARCH-build >> `pwd`/$ARCH-build/build.log 2>&1
    ret=$?
    cd $OLDPWD
    return $ret
}

package() {
    cd $builddir
    make -C $ARCH-build install >> `pwd`/$ARCH-build/build.log 2>&1
    cd $OLDPWD
}

check() {
    echo "The test must be on an OpenHarmony device!"
    # TODO
}

# 清理环境
cleanbuild() {
    rm -rf ${PWD}/$builddir #${PWD}/$packagename
}
