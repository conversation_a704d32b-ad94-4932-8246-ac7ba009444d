# 三方库列表

| 三方库名称                                                   |
| ------------------------------------------------------------ |
| [xerces-c](../thirdparty/xerces-c/README_zh.md)              |
| [modbus](../thirdparty/modbus/README_zh.md)                  |
| [tinyxpath](../thirdparty/tinyxpath/README_zh.md)            |
| [bsdiff](../thirdparty/bsdiff/README_zh.md)                  |
| [concurrentqueue](../thirdparty/concurrentqueue/README_zh.md) |
| [iconv](../thirdparty/iconv/README_zh.md)                    |
| [jbig2enc](../thirdparty/jbig2enc/README_zh.md)              |
| [leptonica](../thirdparty/leptonica/README_zh.md)            |
| [libmp3lame](../thirdparty/libmp3lame/README_zh.md)          |
| [lzma](../thirdparty/lzma/README_zh.md)                      |
| [minizip-ng](../thirdparty/minizip-ng/README_zh.md)          |
| [mqtt](../thirdparty/mqtt/README_zh.md)                      |
| [openjpeg](../thirdparty/openjpeg/README_zh.md)              |
| [rapidjson](../thirdparty/rapidjson/README_zh.md)            |
| [tinyxml2](../thirdparty/tinyxml2/README_zh.md)              |
| [unrar](../thirdparty/unrar/README_zh.md)                    |
| [xz](../thirdparty/xz/README_zh.md)                          |
| [zstd](../thirdparty/zstd/README_zh.md)                      |
| [busybox](../thirdparty/busybox/README_zh.md)                |
| [cryptopp](../thirdparty/cryptopp/README_zh.md)              |
| [double-conversion](../thirdparty/double-conversion/README_zh.md) |
| [hunspell](../thirdparty/hunspell/README_zh.md)              |
| [jasper](../thirdparty/jasper/README_zh.md)                  |
| [json-schema-validator](../thirdparty/json-schema-validator/README_zh.md) |
| [libtommath](../thirdparty/libtommath/README_zh.md)          |
| [libxlsxwriter](../thirdparty/libxlsxwriter/README_zh.md)    |
| [nghttp3](../thirdparty/nghttp3/README_zh.md)                |
| [phf](../thirdparty/phf/README_zh.md)                        |
| [pugixml](../thirdparty/pugixml/README_zh.md)                |
| [tinyexr](../thirdparty/tinyexr/README_zh.md)                |
| [WavPack](../thirdparty/WavPack/README_zh.md)                |
| [libsrtp](../thirdparty/libsrtp/README_zh.md)                |
| [log4cplus](../thirdparty/log4cplus/README_zh.md)            |
| [zxing-cpp](../thirdparty/zxing-cpp/README_zh.md)            |
| [libsvm](../thirdparty/libsvm/README_zh.md)                  |
| [geos](../thirdparty/geos/README_zh.md)                      |
| [jbig2dec](../thirdparty/jbig2dec/README_zh.md)              |
| [faad2](../thirdparty/faad2/README_zh.md)                    |
| [libxls](../thirdparty/libxls/README_zh.md)                  |
| [libtess2](../thirdparty/libtess2/README_zh.md)              |
| [djvulibre](../thirdparty/djvulibre/README_zh.md)            |
| [expat](../thirdparty/libexpat/README_zh.md)                 |
| [exiv2](../thirdparty/exiv2/README_zh.md)                    |
| [libpng](../thirdparty/libpng/README_zh.md)                  |
| [soundtouch](../thirdparty/soundtouch/README_zh.md)          |
| [jbigkit](../thirdparty/jbigkit/README_zh.md)                |
| [zbar](../thirdparty/zbar/README_zh.md)                      |
| [marisa-trie](../thirdparty/marisa-trie/README_zh.md)        |
| [fribidi](../thirdparty/fribidi/README_zh.md)                |
| [cJSON](../thirdparty/cJSON/README_zh.md)                    |
| [libqrencode](../thirdparty/libqrencode/README_zh.md)        |
| [pupnp](../thirdparty/pupnp/README_zh.md)                    |
| [sonic](../thirdparty/sonic/README_zh.md)                    |
| [libzip](../thirdparty/libzip/README_zh.md)                  |
| [harfbuzz](../thirdparty/harfbuzz/README_zh.md)              |
| [googletest](../thirdparty/googletest/README_zh.md)          |
| [jsoncpp](../thirdparty/jsoncpp/README_zh.md)                |
| [kissfft](../thirdparty/kissfft/README_zh.md)                |
| [gmp](../thirdparty/gmp/README_zh.md)                        |
| [jpeg](../thirdparty/jpeg/README_zh.md)                      |
| [miniini](../thirdparty/miniini/README_zh.md)                |
| [pixman](../thirdparty/pixman/README_zh.md)                  |
| [libheif](../thirdparty/libheif/README_zh.md)                |
| [libxml2](../thirdparty/libxml2/README_zh.md)                |
| [sqlite](../thirdparty/sqlite/README_zh.md)                  |
| [libuuid](../thirdparty/libuuid/README_zh.md)                |
| [giflib](../thirdparty/giflib/README_zh.md)                  |
| [lcms2](../thirdparty/lcms2/README_zh.md)                    |
| [websocketpp](../thirdparty/websocketpp/README_zh.md)        |
| [p7zip](../thirdparty/p7zip/README_zh.md)                    |
| [freetype2](../thirdparty/freetype2/README_zh.md)            |
| [curl](../thirdparty/curl/README_zh.md)                      |
| [openssl](../thirdparty/openssl/README_zh.md)                |
| [libdash](../thirdparty/libdash/README_zh.md)                |
| [mythes](../thirdparty/mythes/README_zh.md)                  |
| [libass](../thirdparty/libass/README_zh.md)                  |
| [diff-match-patch-cpp-stl](../thirdparty/diff-match-patch-cpp-stl/README_zh.md) |
| [uchardet](../thirdparty/uchardet/README_zh.md)              |
| [unzip](../thirdparty/unzip/README_zh.md)                    |
| [xmlrpc-c](../thirdparty/xmlrpc-c/README_zh.md)              |
| [protobuf](../thirdparty/protobuf/README_zh.md)              |
| [unixODBC](../thirdparty/unixODBC/README_zh.md)              |
| [fmt](../thirdparty/fmt/README_zh.md)                        |
| [assimp](../thirdparty/assimp/README_zh.md)                  |
| [thrift](../thirdparty/thrift/README_zh.md)                  |
| [caffe](../thirdparty/caffe/README_zh.md)                    |
| [libyuv](../thirdparty/libyuv/README_zh.md)                  |
| [FFmpeg](../thirdparty/FFmpeg/README_zh.md)                  |
| [fftw3](../thirdparty/fftw3/README_zh.md)                    |
| [avro](../thirdparty/avro/README_zh.md)                      |
| [libarchive](../thirdparty/libarchive/README_zh.md)          |
| [glm](../thirdparty/glm/README_zh.md)                        |
| [stb](../thirdparty/stb/README_zh.md)                        |
| [md5-c](../thirdparty/md5-c/README_zh.md)                    |
| [fdk-aac](../thirdparty/fdk-aac/README_zh.md)                |
| [lame](../thirdparty/lame/README_zh.md)                      |
| [libwebp](../thirdparty/libwebp/README_zh.md)                |
| [tiff](../thirdparty/tiff/README_zh.md)                      |
| [behaviortree](../thirdparty/behaviortree/README_zh.md)      |
| [chipmunk2D](../thirdparty/chipmunk2D/README_zh.md)          |
| [lpeg](../thirdparty/lpeg/README_zh.md)                      |
| [lua-amf3](../thirdparty/lua-amf3/README_zh.md)              |
| [luaXML](../thirdparty/luaXML/README_zh.md)                  |
| [oneDNN](../thirdparty/oneDNN/README_zh.md)                  |
| [bctoolbox](../thirdparty/bctoolbox/README_zh.md)            |
| [bcunit](../thirdparty/bcunit/README_zh.md)                  |
| [fribidi](../thirdparty/fribidi/README_zh.md)                |
| [openfst](../thirdparty/openfst/README_zh.md)                |
| [openldap](../thirdparty/openldap/README_zh.md)              |
| [libosip2](../thirdparty/libosip2/README_zh.md)              |
| [pjsip](../thirdparty/pjsip/README_zh.md)                    |
| [snappy](../thirdparty/snappy/README_zh.md)                  |
| [soxr](../thirdparty/soxr/README_zh.md)                      |
| [tink](../thirdparty/tink/README_zh.md)                      |
| [uavs3d](../thirdparty/uavs3d/README_zh.md)                  |
| [vid.stab](../thirdparty/vid.stab/README_zh.md)              |
| [xxHash](../thirdparty/xxHash/README_zh.md)                  |
| [glog](../thirdparty/glog/README_zh.md)                      |
| [libusb](../thirdparty/libusb/README_zh.md)                  |
| [bzip2](../thirdparty/bzip2/README_zh.md)                    |
| [GmSSL](../thirdparty/GmSSL/README_zh.md)                    |

