# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

source HPKBUILD > /dev/null 2>&1
logfile=${LYCIUM_THIRDPARTY_ROOT}/${pkgname}/${pkgname}_${ARCH}_${OHOS_SDK_VER}_test.log
# 在OH环境执行测试的接口
openharmonycheck() {
    res=0
    cd $builddir-$ARCH-build
    make check-TESTS -C tests > ${logfile} 2>&1
 
    res=$?
    if [ $res -ne 0 ]
    then
        mkdir -p ${LYCIUM_FAULT_PATH}/${pkgname}
        cp tests/test-suite.log ${LYCIUM_FAULT_PATH}/${pkgname}/
    fi
    cd $OLDPWD

    return $res
}
